import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import { UserKeyService } from './user_key.service';
import { CreateUserKeyDto } from './dto/create-user_key.dto';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { ApiOperation } from '@nestjs/swagger';
import { QueryUserKeyDto } from './dto/query-user_key.dto';
import { EditUserKeyDto } from './dto/edit-user_key.dto';
import { DeleteUserKeyDto } from './dto/delete-user_key.dto';
import { CreateUserListenKeyDto } from './dto/create-user_listen_key.dto';

@Controller('user-key')
@UseGuards(JwtAuthGuard)
export class UserKeyController {
  constructor(private readonly userKeyService: UserKeyService) {}

  @Post('create')
  @ApiOperation({
    summary: 'user create new user key',
  })
  async create(@Body() createUserKeyDto: CreateUserKeyDto, @Req() req) {
    const user = req.user;
    return await this.userKeyService.create(user, createUserKeyDto);
  }

  @Get()
  @ApiOperation({
    summary: 'find all user keys of specific user (with filter)',
  })
  async findAll(@Req() req, @Query() query: QueryUserKeyDto) {
    const user = req.user;
    return await this.userKeyService.findAll(user.id, query);
  }

  @Post('/update')
  @ApiOperation({
    summary: 'Update user key name and status',
  })
  async updateUserKey(@Req() req, @Body() query: EditUserKeyDto) {
    const user = req.user;
    return await this.userKeyService.updateUserKey(user.id, query);
  }

  @Post('/delete')
  @ApiOperation({
    summary: 'delete user key by id',
  })
  @UseGuards(JwtAuthGuard)
  remove(@Req() req, @Body() body: DeleteUserKeyDto) {
    return this.userKeyService.remove(body, req.user);
  }

  @Post('/listen-key')
  @ApiOperation({
    summary: 'create user listen key for api_key',
  })
  @UseGuards(JwtAuthGuard)
  async createListenKey(@Req() req, @Body() body: CreateUserListenKeyDto) {
    return await this.userKeyService.createUserListenKey(body, req.user);
  }
}
