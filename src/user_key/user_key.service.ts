import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateUserKeyDto } from './dto/create-user_key.dto';
import { <PERSON>tity<PERSON>anager, Equal } from 'typeorm';
import { User<PERSON><PERSON>, UserKeyStatus } from './entities/user_key.entity';
import { AuthService } from 'src/auth/auth.service';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import { User } from 'src/user/entities/user.entity';
import { PlatformService } from 'src/platform/platform.service';
import { QueryUserKeyDto } from './dto/query-user_key.dto';
import { Platform } from 'src/platform/entities/platform.entity';
import { EditUserKeyDto } from './dto/edit-user_key.dto';
import { DeleteUserKeyDto } from './dto/delete-user_key.dto';
import { CreateUserListenKeyDto } from './dto/create-user_listen_key.dto';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import { decrypted, encrypted } from 'common/util/utils';
import configuration from 'config/configuration';
import { Balance } from 'src/balance/entities/balance.entity';
import {
  ArbitrageBot,
  ArbitBotStatus,
} from 'src/arbitrage_bot/entities/arbitrage_bot.entity';
import { Gridbot, GridBotStatus } from 'src/gridbot/entities/gridbot.entity';
import { PaginateByObject } from 'common/dto/pagination.dto';
import { PageDto, PageMetaDto } from 'common/dto/pageResponse.dto';
import { Constant } from 'src/constant/entities/constant.entity';
import Decimal from 'decimal.js';

@Injectable()
export class UserKeyService {
  constructor(
    private em: EntityManager,
    private authService: AuthService,
    private platformService: PlatformService,
  ) {}
  async create(user: User, createUserKeyDto: CreateUserKeyDto) {
    // Encrypt secret key
    const encryptedSecretKey = await encrypted(
      createUserKeyDto.secret_key,
      configuration().encryption.secret,
    );
    const platform = await this.em
      .createQueryBuilder(Platform, 'platform')
      .where('platform.id = :id', { id: createUserKeyDto.platform_id })
      .getOne();
    let platformHelper;

    platformHelper = await callPlatformHelper(platform.name.toLowerCase());
    await platformHelper.init(
      platform.name.toLowerCase() as any,
      createUserKeyDto.api_key,
      encryptedSecretKey,
    );

    let test_response;
    try {
      test_response = await platformHelper.readBalance();
    } catch (error) {
      console.log(error);
      throw new BadRequestException(
        CustomErrorMessages.USER_KEY.INVALID_USERKEY,
      );
    }
    delete createUserKeyDto.secret_key;
    try {
      let user_key = await this.em.save(UserKey, {
        ...createUserKeyDto,
        api_secret: encryptedSecretKey,
        platform_id: platform.id.toString(),
        user_id: user.id.toString(),
        taker_fee: createUserKeyDto.taker_fee || '0',
        maker_fee: createUserKeyDto.maker_fee || '0',
      });
      let balance = await this.em
        .createQueryBuilder(Balance, 'b')
        .where('b.user_key_id = :user_key_id', { user_key_id: user_key.id })
        .getOne();
      if (!balance) {
        const isHataMyr = platform.name.toLowerCase() === 'hata myr';
        const constant = await this.em
          .createQueryBuilder(Constant, 'constant')
          .select('constant.value')
          .where('constant.key = :key', { key: 'MYRRATE' })
          .getOne();
        const myrRate = constant ? constant.value : 4.4;
        test_response = test_response.map((item) => ({
          ...item,
          user_key_id: user_key.id,
          avai_balance_quote: isHataMyr
            ? new Decimal(item.avai_balance_quote)
                .div(new Decimal(myrRate))
                .toString()
            : item.avai_balance_quote,
          freeze_balance_quote: isHataMyr
            ? new Decimal(item.freeze_balance_quote)
                .div(new Decimal(myrRate))
                .toString()
            : item.freeze_balance_quote,
        }));
        await this.em
          .createQueryBuilder()
          .insert()
          .into(Balance)
          .values(test_response)
          .execute();
      }
      return `UserKey ${createUserKeyDto.name} created successfully`;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(
        CustomErrorMessages.USER_KEY.USER_KEY_DUPLICATE,
      );
    }
  }

  async findAll(user_id: number, query: QueryUserKeyDto) {
    const queryBuilder = this.em
      .createQueryBuilder(UserKey, 'userkey')
      .leftJoin('user', 'user', 'userkey.user_id = user.id')
      .leftJoin('platform', 'platform', 'userkey.platform_id = platform.id')
      .where('user.id = :user_id and userkey.deleted_at is NULL', {
        user_id: user_id,
      });
    if (query.name) {
      queryBuilder.andWhere('userkey.name = :name', { name: query.name });
    }
    if (query.platform_id) {
      queryBuilder.andWhere('platform.id = :platform_id', {
        platform_id: query.platform_id,
      });
    }

    if (query.id) {
      queryBuilder.andWhere('userkey.id = :id', {
        id: query.id,
      });
    }
    let userKeys = await queryBuilder
      .select([
        'userkey.id as id',
        'userkey.name as name',
        'userkey.api_key as api_key',
        'userkey.status as status',
        'userkey.taker_fee as taker_fee',
        'userkey.maker_fee as maker_fee',
      ])
      .addSelect(
        `JSON_OBJECT(
        'id', platform.id,
        'name', platform.name,
        'image', platform.image
      )`,
        'platform',
      )
      .getRawMany();
    let itemCount = userKeys.length;
    if (query.take == 0) {
      query.take = itemCount;
    }
    userKeys = PaginateByObject(userKeys, query.page, query.take);
    const pageMetaDto = new PageMetaDto({
      itemCount,
      pageOptionsDto: query,
    });
    return new PageDto(userKeys, pageMetaDto);
  }

  async updateUserKey(user_id: number, editUserKeyDto: EditUserKeyDto) {
    let userKey = await this.em
      .createQueryBuilder(UserKey, 'userkey')
      .leftJoin('user', 'user', 'userkey.user_id = user.id')
      .where('userkey.id = :id and user.id = :user_id', {
        id: editUserKeyDto.id,
        user_id,
      })
      .getOne();

    if (!userKey) {
      throw new BadRequestException(
        CustomErrorMessages.USER_KEY.USER_KEY_NOT_FOUND,
      );
    }

    if (editUserKeyDto.name) {
      userKey.name = editUserKeyDto.name;
    }

    if (editUserKeyDto.status) {
      userKey.status = editUserKeyDto.status as any as UserKeyStatus;
    }

    if (editUserKeyDto.taker_fee) {
      userKey.taker_fee = editUserKeyDto.taker_fee;
    }
    if (editUserKeyDto.maker_fee) {
      userKey.maker_fee = editUserKeyDto.maker_fee;
    }

    try {
      await this.em.save(userKey);
      return { message: 'User Key updated successfully', userKey };
    } catch (error) {
      throw new BadRequestException(
        CustomErrorMessages.USER_KEY.USER_KEY_DUPLICATE,
      );
    }
  }

  async remove(body: DeleteUserKeyDto, user: User) {
    let userKey = await this.em
      .createQueryBuilder(UserKey, 'userkey')
      .leftJoin('user', 'user', 'userkey.user_id = user.id')
      .where(
        "userkey.id = :id and user.id = :user_id and userkey.status != 'DELETED' ",
        {
          id: body.id,
          user_id: user.id,
        },
      )
      .getOne();
    if (!userKey) {
      throw new BadRequestException(
        CustomErrorMessages.USER_KEY.USER_KEY_NOT_FOUND,
      );
    }

    // Check for both ArbitrageBots and GridBots simultaneously
    const [existingArbitrageBot, existingGridBot] = await Promise.all([
      this.em
        .createQueryBuilder(ArbitrageBot, 'ab')
        .where(
          '(ab.base_key_id = :keyId OR ab.target_key_id = :keyId) AND ab.status != :status',
          {
            keyId: userKey.id,
            status: ArbitBotStatus.DELETED,
          },
        )
        .getOne(),

      this.em
        .createQueryBuilder(Gridbot, 'gb')
        .where('gb.user_key_id = :keyId AND gb.status != :status', {
          keyId: userKey.id,
          status: GridBotStatus.DELETED,
        })
        .getOne(),
    ]);

    if (existingArbitrageBot) {
      throw new BadRequestException(
        'Cannot delete user key: Please delete all associated arbitrage bots first',
      );
    }

    if (existingGridBot) {
      throw new BadRequestException(
        'Cannot delete user key: Please delete all associated grid bots first',
      );
    }

    const result = await this.em
      .createQueryBuilder()
      .update(UserKey)
      .set({ status: 'DELETED', deleted_at: () => 'CURRENT_TIMESTAMP' })
      .where('id = :id', { id: userKey.id })
      .execute();
    if (result.affected != 1) {
      throw new BadRequestException(
        CustomErrorMessages.USER_KEY.INVALID_USERKEY,
      );
    }
    return 'User key is deleted';
  }

  async createUserListenKey(body: CreateUserListenKeyDto, user: User) {
    let key = await this.em
      .createQueryBuilder(UserKey, 'uk')
      .leftJoin('platform', 'platform', 'uk.platform_id = platform.id')
      .where('uk.user_id = :user_id and uk.id = :id', {
        user_id: user.id,
        id: body.user_key_id,
      })
      .select([
        'uk.id as id',
        'uk.name as name',
        'uk.api_key as api_key',
        'uk.api_secret as secret_key',
        'uk.status as status',
      ])
      .addSelect(
        `JSON_OBJECT(
        'id', platform.id,
        'name', platform.name,
        'image', platform.image
      )`,
        'platform',
      )
      .getRawOne();
    if (!key) {
      throw new BadRequestException(
        CustomErrorMessages.USER_KEY.INVALID_USERKEY,
      );
    }
    let platformHelper = callPlatformHelper(key.platform.name);
    await platformHelper.init(
      key.platform.name.toLowerCase(),
      key.api_key,
      key.secret_key,
    );
    let listen_key = await platformHelper.createListenKey();
    await this.em.update(
      UserKey,
      { id: key.id },
      { listen_key: listen_key.key },
    );
    return listen_key;
  }
}
