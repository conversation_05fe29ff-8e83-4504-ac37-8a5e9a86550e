import { Platform } from 'src/platform/entities/platform.entity';
import { User } from 'src/user/entities/user.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Unique,
} from 'typeorm';

export enum UserKeyStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DELETED = 'DELETED',
}
@Entity()
@Unique('name_user', ['name', 'user_id'])
export class UserKey {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  user_id: string;

  @Column()
  api_key: string;

  @Column()
  api_secret: string;

  @Column({ default: '0' })
  taker_fee: string;

  @Column({ default: '0' })
  maker_fee: string;

  @Column()
  platform_id: string;

  @Column({ nullable: true }) // future record will be in json format { "binance": "abc123" ,hata": "def456"}
  listen_key: string;

  @Column({ default: UserKeyStatus.ACTIVE })
  status: UserKeyStatus;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}
