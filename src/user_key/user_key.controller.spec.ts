import { Test, TestingModule } from '@nestjs/testing';
import { User<PERSON>eyController } from './user_key.controller';
import { UserKeyService } from './user_key.service';

describe('UserKeyController', () => {
  let controller: UserKeyController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserKeyController],
      providers: [UserKeyService],
    }).compile();

    controller = module.get<UserKeyController>(UserKeyController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
