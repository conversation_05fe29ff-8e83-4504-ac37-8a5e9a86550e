import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsNumberString,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateUserKeyDto {
  @ApiProperty({ description: 'foreign key for platform' })
  @IsNotEmpty()
  @IsNumber()
  platform_id: number;

  @ApiProperty({ description: 'user key alias' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'user key api key' })
  @IsNotEmpty()
  @IsString()
  api_key: string;

  @ApiProperty({ description: 'user key secret key' })
  @IsNotEmpty()
  @IsString()
  secret_key: string;

  @ApiProperty({ description: 'taker fee for the user key' })
  @IsOptional()
  @IsNumberString()
  taker_fee: string;

  @ApiProperty({ description: 'maker fee for the user key' })
  @IsOptional()
  @IsNumberString()
  maker_fee: string;
}
