import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PageOptionsDto } from 'common/dto/pagination.dto';

export class QueryUserKeyDto extends PageOptionsDto {
  @ApiProperty({ description: 'foreign key for platform', required: false })
  @IsOptional()
  @IsString()
  platform_id: string;

  @ApiProperty({ description: 'user key alias', required: false })
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty({ description: 'user key id', required: false })
  @IsString()
  @IsOptional()
  id: string;
}
