import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsNotEmpty } from 'class-validator';

export enum Status {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export class EditUserKeyDto {
  @ApiProperty({ description: 'Id for the user key', required: true })
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiProperty({ description: 'New name for the user key', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Updated status of the user key',
    enum: Status,
    required: false,
  })
  @IsOptional()
  @IsEnum(Status)
  status?: Status;

  @ApiProperty({
    description: 'Updated taker fee of the user key',
    required: false,
  })
  @IsOptional()
  @IsString()
  taker_fee?: string;

  @ApiProperty({
    description: 'Updated maker fee of the user key',
    required: false,
  })
  @IsOptional()
  @IsString()
  maker_fee?: string;
}
