import { forwardR<PERSON>, Module } from '@nestjs/common';
import { UserKeyService } from './user_key.service';
import { User<PERSON>eyController } from './user_key.controller';
import { AuthModule } from 'src/auth/auth.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserKey } from './entities/user_key.entity';
import { PlatformModule } from 'src/platform/platform.module';

@Module({
  imports: [TypeOrmModule.forFeature([UserKey]), AuthModule, PlatformModule],
  controllers: [UserKeyController],
  providers: [UserKeyService],
  exports: [UserKeyService],
})
export class UserKeyModule {}
