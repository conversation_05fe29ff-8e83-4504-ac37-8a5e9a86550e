import { Injectable, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import Redis from 'ioredis';
import configuration from 'config/configuration';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private redisClient: Redis;

  constructor() {
    this.redisClient = new Redis({
      host: configuration().redis.host,
      password: configuration().redis.password,
    });
  }

  async onModuleInit() {
    console.log('RedisService initialized');
  }

  async onModuleDestroy() {
    await this.redisClient.quit();
    console.log('RedisService connection closed');
  }

  async setNewCache(key: string, data: string, expirationTime = 3600) {
    if (expirationTime) {
      await this.redisClient.set(key, data, 'EX', expirationTime);
    } else {
      await this.redisClient.set(key, data);
    }
  }

  async getCache(key: string) {
    return await this.redisClient.get(key);
  }

  async deleteCache(key: string) {
    await this.redisClient.del(key);
  }

  async addToQueue(queueName: string, queueData: string) {
    await this.redisClient.rpush(queueName, queueData);
  }

  async retrieveQueue(queueName: string, start: number = 0, stop: number = -1) {
    return await this.redisClient.lrange(queueName, start, stop);
  }

  async removeFromQueue(
    queueName: string,
    count: number | string = 1,
    element: number | string | Buffer,
  ) {
    console.log(queueName, count, element);
    let res = await this.redisClient.lrem(queueName, count, element);
    console.log(res);
  }

  async incr(key: string) {
    await this.redisClient.incr(key);
  }

  async decr(key: string) {
    await this.redisClient.decr(key);
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    if (ttl) {
      await this.redisClient.set(key, value, 'EX', ttl);
    } else {
      await this.redisClient.set(key, value);
    }
  }

  async get(key: string): Promise<string | null> {
    return await this.redisClient.get(key);
  }

  async scan(pattern: string): Promise<string[]> {
    let cursor = '0';
    const results: string[] = [];

    do {
      const [newCursor, keys] = await this.redisClient.scan(
        cursor,
        'MATCH',
        pattern,
        'COUNT',
        100,
      );
      cursor = newCursor;
      results.push(...keys);
    } while (cursor !== '0');

    return results;
  }

  async del(keys: string | string[]): Promise<void> {
    if (Array.isArray(keys)) {
      await this.redisClient.del(...keys);
    } else {
      await this.redisClient.del(keys);
    }
  }

  async rpush(key: string, value: string): Promise<void> {
    await this.redisClient.rpush(key, value);
  }

  async ttl(key: string): Promise<number> {
    return this.redisClient.ttl(key);
  }
}
