import { InjectQueue } from '@nestjs/bull';
import { BadRequestException, Injectable } from '@nestjs/common';
import { Queue } from 'bull';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import { generateSecret } from 'common/util/utils';
import configuration from 'config/configuration';
import * as crypto from 'crypto';

@Injectable()
export class AppService {
  constructor() {}
  async generateSecret() {
    return await generateSecret();
  }
}
