import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { CreateConstantDto } from './dto/create-constant.dto';
import { UpdateConstantDto } from './dto/update-constant.dto';
import { Constant } from './entities/constant.entity';
import { DeleteConstantDto } from './dto/delete-constant.dto';
import { QueryConstantDto } from './dto/query-constant.dto';

@Injectable()
export class ConstantService {
  constructor(private em: EntityManager) {}

  // async create(createConstantDto: CreateConstantDto) {
  //   const key = createConstantDto.key;
  //   const existingKey = await this.em
  //     .createQueryBuilder(Constant, 'constant')
  //     .where('constant.key = :key', { key })
  //     .getOne();

  //   if (existingKey) {
  //     throw new NotFoundException(`Constant with key '${key}' already exists.`);
  //   }

  //   await this.em.save(this.em.create(Constant, createConstantDto));
  //   return `Key '${key}' created successfully!`;
  // }

  async findAll(query: QueryConstantDto) {
    let queryBuilder = this.em.createQueryBuilder(Constant, 'constant');

    if (query.key) {
      queryBuilder.andWhere('constant.key = :key', {
        key: query.key,
      });
    }

    let keys = queryBuilder.getMany();
    return keys;
  }

  // async update(updateConstantDto: UpdateConstantDto) {
  //   let constant = await this.em
  //     .createQueryBuilder(Constant, 'constant')
  //     .where('constant.key = :key', { key: updateConstantDto.key })
  //     .getOne();

  //   if (!constant) {
  //     throw new NotFoundException(`Constant with key '${updateConstantDto.key}' not found.`);
  //   }
  //   constant.value = updateConstantDto.value;
  //   await this.em.save(constant);
  //   return `Key '${updateConstantDto.key}' updated successfully!`;
  // }

  // async remove(deleteConstantDto: DeleteConstantDto) {
  //   const { key } = deleteConstantDto;

  //   const constant = await this.em
  //     .createQueryBuilder(Constant, 'c')
  //     .where('c.key = :key', { key })
  //     .getOne();

  //   if (!constant) {
  //     throw new NotFoundException(`Constant with key '${key}' not found.`);
  //   }

  //   await this.em.delete(Constant, constant.id);
  //   return `Key successfully removed`;
  // }
}
