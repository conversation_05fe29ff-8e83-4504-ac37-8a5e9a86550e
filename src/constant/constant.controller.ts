import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { ConstantService } from './constant.service';
import { CreateConstantDto } from './dto/create-constant.dto';
import { UpdateConstantDto } from './dto/update-constant.dto';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { ApiExcludeController, ApiOperation } from '@nestjs/swagger';
import { DeleteConstantDto } from './dto/delete-constant.dto';
import { QueryConstantDto } from './dto/query-constant.dto';

@Controller('constant')
@UseGuards(JwtAuthGuard)
@ApiExcludeController()
export class ConstantController {
  constructor(private readonly constantService: ConstantService) {}

  // @Post()
  // @ApiOperation({
  //   summary: 'Create a new key',
  // })
  // async create(@Body() createConstantDto: CreateConstantDto) {
  //   return this.constantService.create(createConstantDto);
  // }

  @Get()
  @ApiOperation({
    summary: 'Search for a key',
  })
  async findAll(@Query() queryConstantDto: QueryConstantDto) {
    return this.constantService.findAll(queryConstantDto);
  }

  // @Post('/update')
  // @ApiOperation({
  //   summary: 'Update a key',
  // })
  // async update(@Body() updateConstantDto: UpdateConstantDto) {
  //   return this.constantService.update(updateConstantDto);
  // }

  // @Post('/delete')
  // @ApiOperation({
  //   summary: 'Delete a key',
  // })
  // async remove(@Body() deleteConstantDto: DeleteConstantDto) {
  //   return this.constantService.remove(deleteConstantDto);
  // }
}
