import {
  Column,
  CreateDate<PERSON>olumn,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum TransactionStatus {
  'OPEN' = 'OPEN',
  'NEW' = 'NEW',
  'PARTIAL_FILL' = 'PARTIAL_FILL',
  'COMPLETED' = 'COMPLETED',
  'CANCELLED' = 'CANCELLED',
}
@Entity()
export class Transaction {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  uuid: string;

  @Column()
  price: string;

  @Column()
  amount: string;

  @Column({ default: null, nullable: true })
  matched_order: string;

  @Column({ default: '0' })
  exc_amount: string;

  @Column({ default: '0' })
  prv_amount: string;

  @Column()
  is_buy: boolean;

  @Column({ default: null, nullable: true })
  order_id: string;

  @Column()
  bot_type: string;

  @Column({ default: '0' })
  fee: string;

  @Column({ default: TransactionStatus.OPEN })
  status: TransactionStatus;

  @Column()
  target_pair_id: string;

  @Column({ nullable: true })
  user_key_id: string;

  @Column()
  platform_id: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
