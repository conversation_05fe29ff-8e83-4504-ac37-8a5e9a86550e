import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { Transaction } from './entities/transaction.entity';
import { QueryTransactionDto } from './dto/query-transaction.dto';
import { User } from 'src/user/entities/user.entity';
import { PaginateByObject } from 'common/dto/pagination.dto';
import { PageDto, PageMetaDto } from 'common/dto/pageResponse.dto';
import { QueryOrderDto } from './dto/query-order.dto';
import { Gridbot } from 'src/gridbot/entities/gridbot.entity';

@Injectable()
export class TransactionsService {
  constructor(private readonly em: EntityManager) {}

  async findTransactions(user: User, filterDto: QueryTransactionDto) {
    const query = this.em
      .createQueryBuilder(Transaction, 'tx')
      .leftJoin('pair', 'pair', 'tx.target_pair_id = pair.id')
      .leftJoin('platform', 'platform', 'tx.platform_id = platform.id')
      .leftJoin(
        (qb) =>
          qb
            .from('gridbot', 'gb')
            .leftJoin('user_key', 'uk_grid', 'gb.user_key_id = uk_grid.id')
            .leftJoin('user', 'u_grid', 'uk_grid.user_id = u_grid.id')
            .select('gb.uuid', 'uuid')
            .addSelect('u_grid.id', 'user_id')
            .withDeleted(),
        'grid_data',
        'grid_data.uuid = tx.uuid',
      )
      .leftJoin(
        (qb) =>
          qb
            .from('arbitrage_bot', 'ab')
            .leftJoin('user_key', 'uk_arb', 'ab.base_key_id = uk_arb.id')
            .leftJoin('user', 'u_arb', 'uk_arb.user_id = u_arb.id')
            .select('ab.uuid', 'uuid')
            .addSelect('u_arb.id', 'user_id')
            .withDeleted(),
        'arb_data',
        'arb_data.uuid = tx.uuid',
      )
      .where('grid_data.user_id = :user_id or arb_data.user_id = :user_id', {
        user_id: user.id,
      });

    if (filterDto.uuid) {
      query.andWhere('tx.uuid = :uuid', { uuid: filterDto.uuid });
    }

    if (filterDto.pair) {
      query.andWhere('tx.target_pair_id = :pair', { pair: filterDto.pair });
    }

    if (filterDto.platform_id) {
      query.andWhere('tx.platform_id = :platform', {
        platform: filterDto.platform_id,
      });
    }

    if (filterDto.status) {
      query.andWhere('tx.status = :status', { status: filterDto.status });
    }
    let res = await query
      .select([
        'tx.id as id',
        'tx.uuid as uuid',
        'tx.price as price',
        'tx.fee as fee',
        'tx.amount as amount',
        'tx.exc_amount as exc_amount',
        'tx.is_buy as is_buy',
        'tx.order_id as order_id',
        'tx.bot_type as bot_type',
        'tx.status as status',
        `tx.created_at as created_at`,
        `tx.updated_at as updated_at`,
      ])
      .addSelect(
        `JSON_OBJECT(
        'id', platform.id,
        'name', platform.name,
        'image', platform.image
      )`,
        'platform',
      )
      .addSelect(
        `JSON_OBJECT(
        'id', pair.id,
        'symbol', pair.symbol
      )`,
        'pair',
      )
      .orderBy('tx.created_at', 'DESC')
      .getRawMany();
    let itemCount = res.length;
    if (filterDto.take == 0) {
      filterDto.take = itemCount;
    }
    res = PaginateByObject(res, filterDto.page, filterDto.take);
    const pageMetaDto = new PageMetaDto({
      itemCount,
      pageOptionsDto: filterDto,
    });
    return new PageDto(res, pageMetaDto);
  }

  async findOpenOrder(user: User, filterDto: QueryOrderDto) {
    //transaction should split to buy and sell,order by price
    let query = this.em
      .createQueryBuilder(Transaction, 'tx')
      .leftJoin('pair', 'pair', 'tx.target_pair_id = pair.id')
      .leftJoin('platform', 'platform', 'tx.platform_id = platform.id')
      .leftJoin(
        (qb) =>
          qb
            .from('gridbot', 'gb')
            .leftJoin('user_key', 'uk_grid', 'gb.user_key_id = uk_grid.id')
            .leftJoin('user', 'u_grid', 'uk_grid.user_id = u_grid.id')
            .select('gb.uuid', 'uuid')
            .addSelect('u_grid.id', 'user_id')
            .withDeleted(),
        'grid_data',
        'grid_data.uuid = tx.uuid',
      )
      .leftJoin(
        (qb) =>
          qb
            .from('arbitrage_bot', 'ab')
            .leftJoin('user_key', 'uk_arb', 'ab.base_key_id = uk_arb.id')
            .leftJoin('user', 'u_arb', 'uk_arb.user_id = u_arb.id')
            .select('ab.uuid', 'uuid')
            .addSelect('u_arb.id', 'user_id')
            .withDeleted(),
        'arb_data',
        'arb_data.uuid = tx.uuid',
      )
      .where(
        '(grid_data.user_id = :user_id OR arb_data.user_id = :user_id) AND tx.status IN (:...status)',
        {
          user_id: user.id,
          status: ['NEW', 'PARTIAL_FILL', 'OPEN'],
        },
      );
    const unionQuery = `
      SELECT 
        'GRIDBOT' as bot_type,
        gb.id,
        gb.uuid,
        gb.name,
        gb.status,
        gb.user_key_id as key_id,
        gb.platform_id,
        gb.base_pair_id as pair_id,
        gb.created_at,
        gb.updated_at
      FROM 
        gridbot gb
      WHERE 
        gb.uuid = ?
      
      UNION
      
      SELECT 
        'ARBITRAGEBOT' as bot_type,
        ab.id,
        ab.uuid,
        ab.name,
        ab.status,
        ab.base_key_id as key_id,
        ab.base_platform_id as platform_id,
        ab.base_pair_id as pair_id,
        ab.created_at,
        ab.updated_at
      FROM 
        arbitrage_bot ab
      WHERE 
        ab.uuid = ?
    `;

    // Execute the raw query with parameters
    const results = await this.em.query(unionQuery, [
      filterDto.uuid,
      filterDto.uuid,
    ]);
    console.log(results);
    // Common select fields for both buy and sell queries
    const selectFields = [
      'tx.price as price',
      'pair.price as pair_price',
      'SUM(tx.amount) as amount',
      'SUM(tx.exc_amount) as exc_amount',
      'pair.qty_dp as qty_dp',
      'pair.price_dp as price_dp',
    ];

    // Get buy orders (ascending price - lowest first)
    const buyQuery = query
      .clone()
      .andWhere('tx.uuid = :uuid', { uuid: filterDto.uuid })
      .andWhere('tx.is_buy = :isBuy', { isBuy: true })
      .select(selectFields)
      .groupBy('tx.price')
      .addGroupBy('pair.qty_dp')
      .addGroupBy('pair.price')
      .addGroupBy('pair.price_dp')
      .orderBy('tx.price', 'DESC');

    // Get sell orders (descending price - highest first)
    const sellQuery = query
      .clone()
      .andWhere('tx.uuid = :uuid', { uuid: filterDto.uuid })
      .andWhere('tx.is_buy = :isBuy', { isBuy: false })
      .select(selectFields)
      .groupBy('tx.price')
      .addGroupBy('pair.qty_dp')
      .addGroupBy('pair.price')
      .addGroupBy('pair.price_dp')
      .orderBy('tx.price', 'ASC');

    // Execute both queries
    const [buyOrders, sellOrders] = await Promise.all([
      buyQuery.getRawMany(),
      sellQuery.getRawMany(),
    ]);
    let amount = 0,
      total_buy = 0,
      total_sell = 0,
      qty_per_step = 0;
    const processedBuyOrders = buyOrders.map((order) => {
      qty_per_step = order.amount;
      amount += order.amount;
      total_buy +=
        (Number(order.amount) - Number(order.exc_amount)) * order.price;
      total_sell += Number(order.exc_amount) * order.pair_price;
      return (order.price = Number(order.price).toFixed(order.price_dp));
    });

    const processedSellOrders = sellOrders.map((order) => {
      amount += order.amount;
      qty_per_step = order.amount;
      total_sell +=
        (Number(order.amount) - Number(order.exc_amount)) * order.pair_price;
      total_buy += Number(order.exc_amount) * order.price;
      return (order.price = Number(order.price).toFixed(order.price_dp));
    });
    // Return the results
    return {
      qty_per_step: qty_per_step,
      total_buy: total_buy.toFixed(4),
      total_sell: total_sell.toFixed(4),
      buy_orders: processedBuyOrders,
      sell_orders: processedSellOrders,
    };
  }
}
