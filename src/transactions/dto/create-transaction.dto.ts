import { IsString, IsBoolean, IsOptional } from 'class-validator';
import { TransactionStatus } from '../entities/transaction.entity';

export class CreateTransactionDto {
  @IsString()
  uuid: string;

  @IsString()
  price: string;

  @IsString()
  amount: string;

  @IsBoolean()
  is_buy: boolean;

  @IsString()
  order_id: string;

  @IsString()
  bot_type: string;

  @IsOptional()
  @IsString()
  fee?: string; // optional, default is '0'

  @IsOptional()
  status?: TransactionStatus; // optional, default is TransactionStatus.NEW

  @IsString()
  target_pair_id: string;

  @IsString()
  platform_id: string;
}