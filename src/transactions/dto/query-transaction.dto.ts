import { IsOptional, IsString, IsEnum } from 'class-validator';
import { TransactionStatus } from '../entities/transaction.entity';
import { PageOptionsDto } from 'common/dto/pagination.dto';

export class QueryTransactionDto extends PageOptionsDto {
  @IsOptional()
  @IsString()
  uuid?: string;

  @IsOptional()
  @IsString()
  pair?: string;

  @IsOptional()
  @IsString()
  platform_id?: string;

  @IsOptional()
  @IsEnum(TransactionStatus)
  status?: TransactionStatus;
}
