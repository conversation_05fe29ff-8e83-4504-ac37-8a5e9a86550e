import {
  Controller,
  Get,
  Query,
  BadRequestException,
  UseGuards,
  Req,
} from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { TransactionsService } from './transactions.service';
import { QueryTransactionDto } from './dto/query-transaction.dto';
import { TransactionStatus } from './entities/transaction.entity';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { QueryOrderDto } from './dto/query-order.dto';

@ApiTags('transactions')
@Controller('transactions')
export class TransactionsController {
  constructor(private readonly transactionsService: TransactionsService) {}

  @Get()
  @ApiQuery({
    name: 'status',
    required: false,
    enum: TransactionStatus,
    description: 'Transaction status',
  })
  @ApiOperation({
    summary: 'Find all transactions ',
  })
  @UseGuards(JwtAuthGuard)
  async findTransactions(@Req() req, @Query() filterDto: QueryTransactionDto) {
    try {
      return await this.transactionsService.findTransactions(
        req.user,
        filterDto,
      );
    } catch (error) {
      console.error('Error retrieving transactions:', error);
      throw new BadRequestException(
        `Failed to retrieve transactions: ${error.message}`,
      );
    }
  }

  @Get('open-orders')
  @ApiOperation({
    summary: 'Find all open orders belongs to uuid',
  })
  @UseGuards(JwtAuthGuard)
  async findOpenOrder(@Req() req, @Query() filterDto: QueryOrderDto) {
    try {
      return await this.transactionsService.findOpenOrder(req.user, filterDto);
    } catch (error) {
      console.error('Error retrieving transactions:', error);
      throw new BadRequestException(
        `Failed to retrieve transactions: ${error.message}`,
      );
    }
  }
}
