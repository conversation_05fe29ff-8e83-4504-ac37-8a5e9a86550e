import { ExecutionContext, HttpException, Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { EntityManager } from 'typeorm';
import { User } from '../user/entities/user.entity';
import { SessionList } from 'src/session_list/entities/session_list.entity';
import * as jwt from 'jsonwebtoken';

@Injectable()
export class JwtAuthGuard extends AuthGuard('ulog') {
  constructor(private readonly em: EntityManager) {
    super();
  }

  canActivate(context: ExecutionContext) {
    return super.canActivate(context);
  }

  handleRequest(err, user, info, context: ExecutionContext) {
    // Check if error is due to token expiration
    if (info && this.isTokenExpiredError(info)) {
      // Handle expired token asynchronously (don't await to avoid blocking)
      // Problem: When token is EXPIRED, user will be NULL
      // Because JWT validation failed, so no user object is created
      this.handleExpiredToken(context).catch((cleanupError) => {
        console.error('Error during session cleanup:', cleanupError);
      });
    }
    if (err || !user) {
      throw new HttpException(
        {
          status: 401,
          error: 'Authentication failed',
          message: 'Must be logged in before performing any action',
          details: err?.message || info?.message,
        },
        401,
      );
    }
    return user;
  }

  /**
   * Check if the error is due to JWT token expiration
   * Common JWT error types:
   * - TokenExpiredError: JWT has expired
   * - JsonWebTokenError: JWT is malformed
   * - NotBeforeError: JWT is not active yet
   */
  private isTokenExpiredError(err: any): boolean {
    return (
      err?.name === 'TokenExpiredError' ||
      err?.message?.includes('jwt expired') ||
      (err?.message && err.message.toLowerCase().includes('expired'))
    );
  }

  /**
   * Handle expired token by disabling the specific session
   * This method extracts user info from the expired token and disables the session
   */
  private async handleExpiredToken(context: ExecutionContext): Promise<void> {
    try {
      const request = context.switchToHttp().getRequest();

      // STEP 1: Get the expired token, Extract token from request
      // JWT Expired Error → We know token is expired
      // BUT → We don't know WHICH USER's sessions to disable
      // Why? Because even though it's expired, it still contains user information

      // Method 1: From cookie (web browsers typically use this)
      let token = request.cookies?.['ulog'];

      // Method 2: From Authorization header (mobile apps, API clients use this)
      // Format: "Authorization: Bearer <token>"
      if (!token && request.headers.authorization) {
        const authHeader = request.headers.authorization;
        // Check if it starts with "Bearer " (note the space after Bearer)
        if (authHeader.startsWith('Bearer ')) {
          // Extract everything after "Bearer " (7 characters)
          token = authHeader.substring(7);
        }
      }

      if (!token) {
        console.log('No token found in request for expired token cleanup');
        return;
      }

      // STEP 2: Decode expired token to get user information
      // jwt.decode() extracts payload WITHOUT verifying signature - it just extracts the payload
      // Why? We need to extract user email from the expired token
      // This is safe because we only need user info, not authentication
      const decoded = jwt.decode(token) as any;

      // STEP 3: Validate decoded token structure
      // Your JWT structure:
      // {
      //   email: "<EMAIL>",    // ✅ Always present
      //   is_2fa: false,               // ✅ Always present (true/false)
      //   iat: 1234567890,             // ✅ Issued at timestamp
      //   exp: 1234567890              // ✅ Expiration timestamp
      // }
      if (!decoded) {
        console.log('Failed to decode expired token');
        return;
      }

      // Check if email exists in token (your tokens always have email)
      if (!decoded.email) {
        console.log('No email found in expired token payload:', decoded);
        return;
      }

      // STEP 4: Find user by email from token
      // Why? We need the user.id to update their sessions
      const user = await this.em.findOneBy(User, {
        email: decoded.email,
      });

      if (!user) {
        console.log(`User not found for email: ${decoded.email}`);
        return;
      }

      // STEP 5: Disable the specific session
      // In your system: session_id = the actual JWT token
      const result = await this.em
        .createQueryBuilder()
        .update(SessionList)
        .set({ status: false })
        .where('user_id = :userId AND session_id = :sessionId', {
          userId: user.id.toString(), // Convert to string to match your schema
          sessionId: token,
        })
        .execute();

      console.log(
        `Disabled ${result.affected} expired session(s) for user ${user.email}`,
      );

      // Log token details for debugging
      if (decoded.exp) {
        const expiredAt = new Date(decoded.exp * 1000);
        console.log(`Token expired at: ${expiredAt.toISOString()}`);
      }
    } catch (cleanupError) {
      console.error(
        'Error during expired token session cleanup:',
        cleanupError,
      );
    }
  }
}
