import { BadRequestException, Injectable } from '@nestjs/common';
import * as crypto from 'crypto';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import configuration from 'config/configuration';

@Injectable()
export class AuthService {
  // Ensure the key is 32 bytes for AES-256
  private encryptionKey = crypto
    .createHash('sha256')
    .update(configuration().encryption.secret)
    .digest('base64')
    .substr(0, 32);

  private ivLength = 16;

  /**
   * Encrypts a given string using AES-256-CBC
   * @param {string} text - The text to encrypt
   * @returns {string} - The encrypted string
   */
  encrypt(text: string): string {
    const iv = crypto.randomBytes(this.ivLength);
    const cipher = crypto.createCipheriv(
      'aes-256-cbc',
      Buffer.from(this.encryptionKey),
      iv,
    );
    let encrypted = cipher.update(text);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return iv.toString('hex') + ':' + encrypted.toString('hex');
  }

  /**
   * Decrypts an encrypted string using AES-256-CBC
   * @param {string} encryptedText - The encrypted text
   * @returns {string} - The decrypted string
   */
  decrypt(encryptedText: string): string {
    const parts = encryptedText.split(':');
    if (parts.length !== 2) {
      throw new BadRequestException(
        CustomErrorMessages.AUTH.INVALID_ENCRYPTED_TEXT,
      );
    }
    const iv = Buffer.from(parts[0], 'hex');
    const encryptedTextBuffer = Buffer.from(parts[1], 'hex');
    const decipher = crypto.createDecipheriv(
      'aes-256-cbc',
      Buffer.from(this.encryptionKey),
      iv,
    );
    let decrypted = decipher.update(encryptedTextBuffer);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    return decrypted.toString();
  }
}
