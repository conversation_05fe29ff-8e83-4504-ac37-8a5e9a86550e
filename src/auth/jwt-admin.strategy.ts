import { Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import {
  HttpException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { EntityManager } from 'typeorm';
import * as jwt from 'jsonwebtoken';
import { User } from 'src/user/entities/user.entity';
import configuration from 'config/configuration';
import { adminCookieExtractor } from './cookie-extractor';

@Injectable()
export class JwtAdminStrategy extends PassportStrategy(Strategy, 'adlog') {
  constructor(private em: EntityManager) {
    super({
      jwtFromRequest: adminCookieExtractor(),
      ignoreExpiration: false,
      secretOrKey: configuration().jwtConstant.secret,
      passReqToCallback: true,
    });
  }

  async validate(payload: any) {
    if (!payload.headers.cookie) {
      throw new UnauthorizedException('please provide valid token');
    }
    if (payload.headers.cookie.search('adlog=') == -1) {
      throw new UnauthorizedException('invalid token');
    }

    try {
      let token_1 = payload.headers.cookie.split(' ');
      let token = token_1
        .find((item) => item.search('adlog=') != -1)
        .split('adlog=')[1];
      if (token[token.length - 1] == ';') {
        token = token.slice(0, -1);
      }
      var decode = jwt.verify(token, configuration().jwtConstant.secret);
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('token is expired');
      }
      if (error.name === 'user is not valid') {
        throw new UnauthorizedException('user is not valid');
      }
      if (error.name === 'please provide valid token') {
        throw new UnauthorizedException('please provide valid token');
      }
      throw new HttpException(error.message, error.status);
    }
    let user = await this.em.findOneBy(User, { email: decode['email'] });
    if (!user) {
      throw new UnauthorizedException('Invalid user');
    }
    return user;
  }
}
