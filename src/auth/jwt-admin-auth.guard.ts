import { ExecutionContext, HttpException, Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class JwtAdminAuthGuard extends AuthGuard('adlog') {
  constructor() {
    super();
  }
  canActivate(context: ExecutionContext) {
    return super.canActivate(context);
  }

  handleRequest(err, user, info) {
    if (err) {
      console.log('Error in guard:', err);
      throw new HttpException(err.message, 400);
    } else if (!user) {
      console.log('No user in guard:', user);
      throw new HttpException('Must be login before perform any actionn', 401);
    }
    return user;
  }
}
