export function cookieExtractor() {
  return function (request) {
    var token = null;
    if (request && request.cookies) {
      token = request.cookies['ulog'];
    }
    return token;
  };
}

export function adminCookieExtractor() {
  return function (request) {
    var token = null;
    if (request && request.cookies) {
      token = request.cookies['adlog'];
      const ip =
        request.headers['x-forwarded-for'] ||
        request.ip ||
        request.connection.remoteAddress;
      console.log(`Authentication attempt from IP: ${ip}`);
    }
    return token;
  };
}

export function user2FACookieExtractor() {
  return function (request) {
    var token = null;
    if (request && request.cookies) {
      token = request.cookies['u-2fa'];
      const ip =
        request.headers['x-forwarded-for'] ||
        request.ip ||
        request.connection.remoteAddress;
      console.log(`Authentication attempt from IP: ${ip}`);
    }
    return token;
  };
}
