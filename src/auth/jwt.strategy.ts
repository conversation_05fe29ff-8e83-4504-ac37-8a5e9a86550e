import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-jwt';
import configuration from 'config/configuration';
import { EntityManager } from 'typeorm';
import { User } from '../user/entities/user.entity';
import { cookieExtractor } from './cookie-extractor';
import { SessionList } from 'src/session_list/entities/session_list.entity';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'ulog') {
  constructor(private em: EntityManager) {
    super({
      jwtFromRequest: (req) => {
        let token = cookieExtractor()(req);

        if (!token && req.headers.authorization) {
          const authHeader = req.headers.authorization;
          if (authHeader.startsWith('Bearer ')) {
            token = authHeader.substring(7);
          }
        }

        return token;
      },
      ignoreExpiration: false,
      secretOrKey: configuration().jwtConstant.secret,
      passReqToCallback: true,
    });
  }

  async validate(req: any, payload: any) {
    try {
      let cookie = req.cookies['ulog'];
      const [user, session] = await Promise.all([
        this.em.findOneBy(User, {
          email: payload.email,
        }),
        this.em
          .createQueryBuilder(SessionList, 'session')
          .where('session.session_id = :session_id AND session.status = true', {
            session_id: cookie,
          })
          .getOne(),
      ]);

      if (!user) {
        throw new UnauthorizedException('User not found');
      } else if (!session) {
        throw new UnauthorizedException('Session not found');
      }

      return user;
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }
  }

  cookieExtractor = function (req) {
    var token = null;
    if (req && req.cookies) {
      token = req.cookies['ulog'];
    }
    return token;
  };
}
