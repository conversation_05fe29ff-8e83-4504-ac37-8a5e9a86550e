import { Controller, Get, Post, Body, Patch, Param, Delete, Req, Query, UseGuards } from '@nestjs/common';
import { SessionListService } from './session_list.service';
import { CreateSessionListDto } from './dto/create-session_list.dto';
import { UpdateSessionListDto } from './dto/update-session_list.dto';
import { ApiOperation } from '@nestjs/swagger';
import { QuerySessionListDto } from './dto/query-session_list.dto';
import { TerminateSessionListDto } from './dto/terminate-session_list.dto';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';

@Controller('session-list')
@UseGuards(JwtAuthGuard)
export class SessionListController {
  constructor(private readonly sessionListService: SessionListService) {}

  @Post()
  create(
    @Body() createSessionListDto: CreateSessionListDto
  ) {
    return this.sessionListService.create(createSessionListDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Find all session lists',
  })
  async findAll(
    @Req() req: any, // Assuming you might need the request object for some reason
    @Query() query: QuerySessionListDto,
  ) {
    return await this.sessionListService.findAll(
      req.user,
      query
    );
  }

  @Post('terminate')
  @ApiOperation({
    summary: 'Terminate a session list',
  })
  async terminate(
    @Req() req: any,
    @Body() TerminateSessionListDto: TerminateSessionListDto
  ) {
    return await this.sessionListService.terminate(
      req.user,
      TerminateSessionListDto
    );
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.sessionListService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateSessionListDto: UpdateSessionListDto) {
    return this.sessionListService.update(+id, updateSessionListDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.sessionListService.remove(+id);
  }
}
