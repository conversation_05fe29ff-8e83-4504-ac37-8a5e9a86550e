import {
  Column,
  CreateDate<PERSON>olumn,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class SessionList {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  session_id: string;

  @Column()
  user_agent: string;

  @Column()
  ip_address: string;

  @Column()
  status: boolean;

  @Column()
  user_id: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
