import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateSessionListDto } from './dto/create-session_list.dto';
import { UpdateSessionListDto } from './dto/update-session_list.dto';
import { EntityManager } from 'typeorm';
import { User } from 'src/user/entities/user.entity';
import { QuerySessionListDto } from './dto/query-session_list.dto';
import { SessionList } from './entities/session_list.entity';
import { PaginateByObject } from 'common/dto/pagination.dto';
import { PageDto, PageMetaDto } from 'common/dto/pageResponse.dto';
import { TerminateSessionListDto } from './dto/terminate-session_list.dto';
@Injectable()
export class SessionListService {

  constructor(
    private readonly em: EntityManager,

  ) {}

  create(createSessionListDto: CreateSessionListDto) {
    return 'This action adds a new sessionList';
  }

  async findAll(user: User, querySessionListDto: QuerySessionListDto) {

    // console.log('Find All Parameter'    // Calculate total

    let queryBuilder = this.em
      .createQueryBuilder(SessionList, 'sl')
      .andWhere('sl.user_id = :userId',{
        userId: user.id,
      });

    // Filter by status logic
    if (querySessionListDto.status != undefined && querySessionListDto.status != null) {
      if (querySessionListDto.status == "1") {
        queryBuilder.andWhere('sl.status = :status AND DATEDIFF(NOW(), sl.created_at) < 5', {
          status: 1,
        });
      } else {
        queryBuilder.andWhere('sl.status = :status', {
          status: 0,
        });
      }
    }
    

    let sessionList = await queryBuilder
      .select([
        'sl.id as session_id',
        'sl.user_agent as user_agent',
        'sl.ip_address as ip_address',
        'sl.status as status',
        'sl.created_at as created_at',
        'sl.updated_at as updated_at',
      ])
      .orderBy('sl.created_at', querySessionListDto.order)
      .getRawMany();

      let itemCount = sessionList.length;
      sessionList = PaginateByObject(sessionList, querySessionListDto.page, querySessionListDto.take);
      const pageMetaDto = new PageMetaDto({
        itemCount,
        pageOptionsDto: querySessionListDto,
      });

      return new PageDto(
        sessionList,
        pageMetaDto,
      );

  }

  async terminate(user: User, terminateSessionListDto: TerminateSessionListDto) {
  const session = await this.em
    .createQueryBuilder(SessionList, 'sl')
    .where('sl.id = :session_id', {
      session_id: terminateSessionListDto.session_id,
    })
    .andWhere('sl.user_id = :user_id', {
      user_id: user.id,
    })
    .getOne();

  if (!session) {
    throw new BadRequestException(
      `Session with id ${terminateSessionListDto.session_id} not found`
    );
  }

  // Check if session is already terminated
  if (session.status == false) {
    throw new BadRequestException(
      `Session with id ${terminateSessionListDto.session_id} is already terminated`
    );
  }

  const result = await this.em
    .createQueryBuilder()
    .update(SessionList)
    .set({ status: 0 })
    .where('id = :session_id', {
      session_id: terminateSessionListDto.session_id,
    })
    .andWhere('user_id = :user_id', {
      user_id: user.id,
    })
    .execute();

  return `Session with id ${terminateSessionListDto.session_id} terminated successfully`;
}


  findOne(id: number) {
    return `This action returns a #${id} sessionList`;
  }

  update(id: number, updateSessionListDto: UpdateSessionListDto) {
    return `This action updates a #${id} sessionList`;
  }

  remove(id: number) {
    return `This action removes a #${id} sessionList`;
  }
}
