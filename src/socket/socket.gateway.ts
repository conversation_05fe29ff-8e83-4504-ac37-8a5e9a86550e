import {
  WebSocketGateway,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  WebSocketServer,
  OnGatewayDisconnect,
  OnGatewayConnection,
  OnGatewayInit,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

@WebSocketGateway(3005, {
  cors: {
    origin: [
      'http://localhost:3000',
      'http://localhost:5173',
      'https://arcabot.cake5x.com',
      'https://stg-arcabot.cake5x.com',
    ],
    // methods: ['GET', 'POST'],
    // allowedHeaders: ['Content-Type', 'Authorization'],
    // credentials: true,
  },
  namespace: 'ws',
  pingInterval: 25000, // 25s between pings
  pingTimeout: 5000, // disconnect if no pong received in 5s
})
export class SocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  constructor() {}

  @WebSocketServer()
  server: Server;
  count: number = 0;

  handleConnection(client: Socket, ...args: any[]) {
    console.log(`Client connected: ${client.id}`);
    this.count++;
    console.log(`Total connected clients: ${this.count}`);
  }

  handleDisconnect(client: Socket) {
    console.log(`Client disconnected: ${client.id}`);
    this.count--;
    console.log(`Total connected clients: ${this.count}`);
  }
}
