import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import configuration from 'config/configuration';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { UserModule } from './user/user.module';
import { PlatformModule } from './platform/platform.module';
import { UserKeyModule } from './user_key/user_key.module';
import { RedisModule } from './redis/redis.module';
import { ScheduleModule } from '@nestjs/schedule';
import { RateModule } from './rate/rate.module';
import { MulterModule } from '@nestjs/platform-express';
import { PairModule } from './pair/pair.module';
import { GridbotModule } from './gridbot/gridbot.module';
import { QueueModule } from './queue/queue.module';
import { TransactionsModule } from './transactions/transactions.module';
import { BalanceModule } from './balance/balance.module';
import { BullModule } from '@nestjs/bull';
import { ArbitrageBotModule } from './arbitrage_bot/arbitrage_bot.module';
import { TradeModule } from './trade/trade.module';
import { ConstantModule } from './constant/constant.module';
import { BotEventModule } from './bot-event/bot-event.module';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ActivityLogInterceptor } from 'common/interceptor/activity-log.interceptor';
import { SessionListModule } from './session_list/session_list.module';
import { SocketModule } from './socket/socket.module';
import { LoggerModule } from 'nestjs-pino';
import { randomUUID } from 'crypto';

@Module({
  imports: [
    // For better caching, install and add:
    // npm install @nestjs/cache-manager cache-manager
    // CacheModule.register({
    //   ttl: 60, // Default cache TTL in seconds
    //   max: 100, // Maximum number of items in cache
    //   isGlobal: true,
    // }),
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: configuration().database.host,
      port: configuration().database.port,
      username: configuration().database.username,
      password: configuration().database.password,
      database: configuration().database.database,
      entities: ['dist/**/*.entity{.ts,.js}'],
      synchronize:
        configuration().database.synchronize === 'true' ? true : false,
      extra: {
        connectionLimit: 10,
      },
      cache: {
        duration: 30000,
      },
      // dropSchema: true,
    }),
    ScheduleModule.forRoot(),
    AuthModule,
    UserModule,
    MulterModule.registerAsync({
      useFactory: () => ({
        dest: './upload',
      }),
    }),
    PlatformModule,
    UserKeyModule,
    PairModule,
    // CronModule,
    RedisModule,
    GridbotModule,
    QueueModule,
    TransactionsModule,
    BalanceModule,
    ArbitrageBotModule,
    TradeModule,
    ConstantModule,
    BotEventModule,
    SessionListModule,
    SocketModule,
    LoggerModule.forRoot({
      pinoHttp: {
        level: configuration().environment === 'PRODUCTION' ? 'info':'debug',
        genReqId: (req) => {
          return randomUUID();
        },
        serializers: {
          req(req) {
            return {
              method: req.method,
              url: req.url,
              query: req.query, 
            }
          },
          res(res) {
            return {
              statusCode: res.statusCode,
            };
          },
        },

        customProps: (req) => {
          const { user, id } = req as unknown as {
            user: { id?: string; address?: string };
            id: string;
          };
          return {
            userId: user?.id,
            userAddress: user?.address,
            traceId: id,
          };
        },

        customSuccessMessage(req, res) {
          return `${res.statusCode} ${req.method} ${req.url}`;
        },
        customErrorMessage(req, res, err) {
          return `${res.statusCode} ${req.method} ${req.url} - ${err.message}`;
        },

        transport: configuration().environment === 'PRODUCTION' ?  {
          target: '@logtail/pino',
          options: {
            sourceToken: configuration().taillog.token,
            options: {
              endpoint: configuration().taillog.url,
            },
          },
        } :  { 
        target: 'pino-pretty', 
        options: { 
	        singleLine: true 
        }, 
	        level: 'info' 
        },
      },
      exclude: ['status'],
    }),		
  
  ],
  controllers: [AppController],
  providers: [
    AppService,
    { provide: APP_INTERCEPTOR, useClass: ActivityLogInterceptor },
  ],
})
export class AppModule {}
