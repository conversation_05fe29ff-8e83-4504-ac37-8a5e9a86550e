import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsNumberString,
  IsString,
} from 'class-validator';

export class CreateRateDto {
  @ApiProperty({ description: 'base currency' })
  @IsString()
  @IsNotEmpty()
  base: string;

  @ApiProperty({ description: 'quote currency' })
  @IsString()
  @IsNotEmpty()
  quote: string;

  @ApiProperty({ description: 'currency conversion' })
  @IsNumberString()
  @IsNotEmpty()
  rate: string;
}
