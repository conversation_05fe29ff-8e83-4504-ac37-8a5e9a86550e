import {
  Check,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
@Check(`"base" <> "quote"`)
export class Rate {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  base: string;

  @Column()
  quote: string;

  @Column()
  symbol: string;

  @Column({ precision: 3 })
  rate: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}
