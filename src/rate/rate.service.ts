import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateRateDto } from './dto/create-rate.dto';
import { UpdateRateDto } from './dto/update-rate.dto';
import { EntityManager } from 'typeorm';
import { Rate } from './entities/rate.entity';
import { CustomErrorMessages } from 'common/helper/errorMessage';

@Injectable()
export class RateService {
  constructor(private readonly em: EntityManager) {}

  async create(createRateDto: CreateRateDto) {
    const rate = this.em.create(Rate, createRateDto);
    rate.symbol = rate.base + rate.quote;
    await this.em.save(rate);
    return `Rate ${rate.base + rate.quote} created successfully!`;
  }

  async findAll() {
    return await this.em.find(Rate);
  }

  async findOne(id: number) {
    return await this.em.findOneBy(Rate, { id });
  }

  async findBySymbol(symbol: string) {
    return await this.em.findOneBy(Rate, { symbol });
  }

  async update(id: number, updateRateDto: UpdateRateDto) {
    const rate = await this.em.findOneBy(Rate, { id });
    if (!rate) {
      throw new BadRequestException(CustomErrorMessages.RATE.INVALID_RATE_ID);
    }
    rate.base = updateRateDto.base;
    rate.quote = updateRateDto.quote;
    rate.symbol = updateRateDto.base + updateRateDto.quote;
    rate.rate = updateRateDto.rate;
    await this.em.save(Rate, rate);
    return `Rate ${rate.symbol} updated successfully`;
  }

  async updateRateBySymbol(updateRateDto: UpdateRateDto) {
    const rateEntity = await this.em.findOneBy(Rate, {
      symbol: updateRateDto.base + updateRateDto.quote,
    });
    if (!rateEntity) {
      throw new BadRequestException(CustomErrorMessages.RATE.INVALID_SYMBOL);
    }
    rateEntity.rate = updateRateDto.rate;
    await this.em.save(Rate, rateEntity);
    return `Rate ${rateEntity.symbol} updated successfully`;
  }

  async remove(id: number) {
    const result = await this.em.softDelete(Rate, { id });
    if (result.affected === 0) {
      throw new BadRequestException(CustomErrorMessages.RATE.INVALID_RATE_ID);
    }
    return `Rate successfully removed`;
  }

  /**
   * Helper function to convert currency
   * @param value - Value of currency
   * @param rate - Conversion rate
   * @param toTarget - (true) Convert from the source currency to the target currency / (false) Convert from the target currency back to the source currency
   * @returns {number} - Converted currency
   */
  convertCurrency(value: number, rate: number, toTarget = true): number {
    return toTarget ? value * rate : value / rate;
  }
}
