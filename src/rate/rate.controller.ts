import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { RateService } from './rate.service';
import { CreateRateDto } from './dto/create-rate.dto';
import { UpdateRateDto } from './dto/update-rate.dto';
import { CustomErrorMessages } from 'common/helper/errorMessage';

@Controller('rate')
export class RateController {
  private readonly password = 'techticssecretkey';
  constructor(private readonly rateService: RateService) {}

  @Post()
  async create(@Body() createRateDto: CreateRateDto) {
    return await this.rateService.create(createRateDto);
  }

  @Get()
  async findAll() {
    return await this.rateService.findAll();
  }

  @Get('/get/:id')
  async findOne(@Param('id') id: string) {
    console.log('wwww');
    return await this.rateService.findOne(+id);
  }

  // @Post(':id')
  // async update(@Param('id') id: string, @Body() updateRateDto: UpdateRateDto) {
  //   return await this.rateService.update(+id, updateRateDto);
  // }

  @Get('update-rate')
  async updateRate(@Query() updateRateDto: UpdateRateDto) {
    console.log(updateRateDto);
    // Check if the password matches
    if (updateRateDto.pw !== this.password) {
      throw new BadRequestException(CustomErrorMessages.RATE.INVALID_PASSWORD);
    }
    // Validate the rate format
    const rateValue = parseFloat(updateRateDto.rate);
    if (isNaN(rateValue) || rateValue <= 0) {
      throw new BadRequestException(
        CustomErrorMessages.RATE.INVALID_RATE_VALUE,
      );
    }
    return await this.rateService.updateRateBySymbol(updateRateDto);
  }

  @Post('/delete/:id')
  async remove(@Param('id') id: string) {
    return await this.rateService.remove(+id);
  }
}
