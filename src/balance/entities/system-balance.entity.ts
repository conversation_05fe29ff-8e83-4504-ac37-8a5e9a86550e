import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('system_balance')
export class SystemBalance {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  symbol: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, default: '0' })
  avai_balance: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, default: '0' })
  freeze_balance: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, default: '0' })
  avai_balance_quote: string;

  @Column({ type: 'decimal', precision: 20, scale: 8, default: '0' })
  freeze_balance_quote: string;

  @Column()
  user_key_id: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}