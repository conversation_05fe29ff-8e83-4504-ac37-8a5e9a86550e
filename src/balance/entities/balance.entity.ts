import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('balance')
export class Balance {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  symbol: string;

  @Column({ type: 'float' })
  avai_balance: string;

  @Column({ type: 'float' })
  freeze_balance: string;

  @Column({ type: 'float' })
  avai_balance_quote: string;

  @Column({ type: 'float' })
  freeze_balance_quote: string;

  @Column()
  user_key_id: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
