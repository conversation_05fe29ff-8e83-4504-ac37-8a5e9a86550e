import {
  Controller,
  Get,
  UseGuards,
  Query,
  Req,
  Post,
  Body,
} from '@nestjs/common';
import { BalanceService } from './balance.service';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { ApiOperation } from '@nestjs/swagger';
import { QueryBalanceDto } from './dto/query-balance.dto';
import { UpdateBalanceDto } from './dto/update-balance.dto';

@Controller('balance')
@UseGuards(JwtAuthGuard)
export class BalanceController {
  constructor(private readonly balanceService: BalanceService) {}

  @Get()
  @ApiOperation({
    summary: 'Find all balance (with filter)',
  })
  async findAll(@Req() req: any, @Query() query: QueryBalanceDto) {
    return await this.balanceService.findAll(req.user, query);
  }

  @Post('/update')
  @ApiOperation({
    summary:
      'Let user to manual update their account balance for single platform',
  })
  async updateBalance(@Req() req, @Body() body: UpdateBalanceDto) {
    const user = req.user;
    return await this.balanceService.updateSinglePlatformBalance(user, body);
  }

  @Post('/system-update')
  @ApiOperation({
  summary:
  'Let user to manual update their account to system balance for single platform',
  })
  async updateSystemBalance(@Req() req, @Body() body: UpdateBalanceDto) {
  const user = req.user;
  return await this.balanceService.updateSinglePlatformSystemBalance(user, body);
  }
}
