import { Injectable } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { Balance } from './entities/balance.entity';
import { QueryBalanceDto } from './dto/query-balance.dto';
import { User } from 'src/user/entities/user.entity';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import { decrypted } from 'common/util/utils';
import configuration from 'config/configuration';
import { UserKey } from 'src/user_key/entities/user_key.entity';
import { Pair } from 'src/pair/entities/pair.entity';
import { UpdateBalanceDto } from './dto/update-balance.dto';
import { Constant } from 'src/constant/entities/constant.entity';
import Decimal from 'decimal.js';
import { SystemBalance } from './entities/system-balance.entity';
@Injectable()
export class BalanceService {
  constructor(private em: EntityManager) {}

  async findAll(user: User, query: QueryBalanceDto) {
    const queryBuilder = this.em
      .createQueryBuilder(Balance, 'balance')
      .leftJoin('user_key', 'user_key', 'balance.user_key_id = user_key.id')
      .where('user_key.user_id = :user_id', { user_id: user.id });
    if (query.user_key_id) {
      queryBuilder.andWhere('balance.user_key_id = :user_key_id', {
        user_key_id: query.user_key_id,
      });
    }
    if (query.platform_id) {
      queryBuilder.andWhere('user_key.platform_id = :platform_id', {
        platform_id: query.platform_id,
      });
    }
    if (query.show_zero_balance !== 'true') {
      queryBuilder.andWhere(
        `(balance.avai_balance != 0 OR
          balance.freeze_balance != 0 OR
          balance.avai_balance_quote != 0 OR
          balance.freeze_balance_quote != 0)`,
      );
    }
    return await queryBuilder.getMany();
  }

  async updateSinglePlatformBalance(user: User, body: UpdateBalanceDto) {
    let userId = user.id;
    let user_key_id = body.user_key_id;

    const user_key = await this.em
      .createQueryBuilder(UserKey, 'uk')
      .leftJoin('platform', 'p', 'uk.platform_id = p.id')
      .select([
        'uk.id AS user_key_id',
        'uk.user_id AS user_id',
        'uk.platform_id AS platform_id',
        'uk.api_key AS api_key',
        'uk.api_secret AS api_secret',
        'p.name AS platform_name',
      ])
      .where('uk.id = :user_key_id', { user_key_id })
      .getRawOne();

    const duplicateUserKeys = await this.em
      .createQueryBuilder(UserKey, 'user_key')
      .select([
        'user_key.id as user_key_id',
        'user_key.api_key',
        'user_key.api_secret',
        'user_key.platform_id',
      ])
      .where('user_key.user_id = :userId', { userId })
      .andWhere('user_key.platform_id = :platformId', {
        platformId: user_key.platform_id,
      })
      .andWhere('user_key.api_key = :apiKey', {
        apiKey: user_key.api_key,
      })
      .getRawMany();

    try {
      let platformHelper = await callPlatformHelper(
        user_key.platform_name.toLowerCase(),
      );
      await platformHelper.init(
        user_key.platform_name.toLowerCase(),
        user_key.api_key,
        user_key.api_secret,
      );

      let balances = await platformHelper.readBalance();

      const userKeyIds = duplicateUserKeys.map(
        (keyData) => keyData.user_key_id,
      );

      for (const balance of balances) {
        const pair = await this.em
          .createQueryBuilder(Pair, 'pair')
          .where('pair.base = :asset AND pair.quote = :quote', {
            asset: balance.symbol,
            quote: 'USDT',
          })
          .getOne();

        let avai_balance_quote = 0;
        let freeze_balance_quote = 0;
        if (user_key.platform_name.toLowerCase() == 'binance') {
          if (balance.symbol === 'USDT') {
            avai_balance_quote =
              new Decimal(balance.avai_balance).toNumber() ?? 0;
            freeze_balance_quote =
              new Decimal(balance.freeze_balance).toNumber() ?? 0;
          } else {
            avai_balance_quote = pair
              ? new Decimal(balance.avai_balance)
                  .mul(new Decimal(pair.price))
                  .toNumber()
              : 0;
            freeze_balance_quote = pair
              ? new Decimal(balance.freeze_balance)
                  .mul(new Decimal(pair.price))
                  .toNumber()
              : 0;
          }
        } else if (user_key.platform_name.toLowerCase() == 'hata myr') {
          // if (balance.symbol == 'MYR') {
          let constant = await this.em
            .createQueryBuilder(Constant, 'constant')
            .select('constant.value')
            .where('constant.key = :key', { key: 'MYRRATE' })
            .getOne();

          const myrRate = constant ? constant.value : 4.4;

          avai_balance_quote = new Decimal(balance.avai_balance_quote)
            .div(new Decimal(myrRate))
            .toNumber();
          freeze_balance_quote = new Decimal(balance.freeze_balance_quote)
            .div(new Decimal(myrRate))
            .toNumber();
        } else {
          avai_balance_quote = new Decimal(
            balance.avai_balance_quote,
          ).toNumber();
          freeze_balance_quote = new Decimal(
            balance.freeze_balance_quote,
          ).toNumber();
          // }
        }

        // Check for existing records
        const existingBalances = await this.em
          .createQueryBuilder(Balance, 'balance')
          .where(
            'balance.symbol = :symbol AND balance.user_key_id IN (:...userKeyIds)',
            {
              symbol: balance.symbol,
              userKeyIds,
            },
          )
          .getMany();

        // Map existing balances by user_key_id for quick lookup
        const balancesByKeyId = existingBalances.reduce((map, item) => {
          map[item.user_key_id] = item;
          return map;
        }, {});

        // Prepare batch of entities to save
        const balancesToSave = [];

        for (const keyId of userKeyIds) {
          if (balancesByKeyId[keyId]) {
            // Update existing
            balancesByKeyId[keyId].avai_balance = balance.avai_balance;
            balancesByKeyId[keyId].freeze_balance = balance.freeze_balance;
            balancesByKeyId[keyId].avai_balance_quote =
              avai_balance_quote.toString();
            balancesByKeyId[keyId].freeze_balance_quote =
              freeze_balance_quote.toString();
            balancesToSave.push(balancesByKeyId[keyId]);
          } else {
            // Create new
            balancesToSave.push(
              this.em.create(Balance, {
                symbol: balance.symbol,
                user_key_id: keyId,
                avai_balance: balance.avai_balance,
                freeze_balance: balance.freeze_balance,
                avai_balance_quote: avai_balance_quote.toString(),
                freeze_balance_quote: freeze_balance_quote.toString(),
              }),
            );
          }
        }

        // Save all in one batch
        await this.em.save(balancesToSave);
      }
      return { message: 'All balances updated successfully' };
    } catch (error) {
      console.log('err', error);
    }
  }

  async updateSinglePlatformSystemBalance(user: User, body: UpdateBalanceDto) {
    console.log("===Start Update System Balance===");
    
    let userId = user.id;
    let user_key_id = body.user_key_id;

    const user_key = await this.em
      .createQueryBuilder(UserKey, 'uk')
      .leftJoin('platform', 'p', 'uk.platform_id = p.id')
      .select([
        'uk.id AS user_key_id',
        'uk.user_id AS user_id',
        'uk.platform_id AS platform_id',
        'uk.api_key AS api_key',
        'uk.api_secret AS api_secret',
        'p.name AS platform_name',
      ])
      .where('uk.id = :user_key_id', { user_key_id })
      .getRawOne();

    const duplicateUserKeys = await this.em
      .createQueryBuilder(UserKey, 'user_key')
      .select([
        'user_key.id as user_key_id',
        'user_key.api_key',
        'user_key.api_secret',
        'user_key.platform_id',
      ])
      .where('user_key.user_id = :userId', { userId })
      .andWhere('user_key.platform_id = :platformId', {
        platformId: user_key.platform_id,
      })
      .andWhere('user_key.api_key = :apiKey', {
        apiKey: user_key.api_key,
      })
      .getRawMany();

    try {
      let platformHelper = await callPlatformHelper(
        user_key.platform_name.toLowerCase(),
      );
      await platformHelper.init(
        user_key.platform_name.toLowerCase(),
        user_key.api_key,
        user_key.api_secret,
      );

      let balances = await platformHelper.readBalance();

      const userKeyIds = duplicateUserKeys.map(
        (keyData) => keyData.user_key_id,
      );

      for (const balance of balances) {

        const pair = await this.em
          .createQueryBuilder(Pair, 'pair')
          .where('pair.base = :asset AND pair.quote = :quote', {
            asset: balance.symbol,
            quote: 'USDT',
          })
          .getOne();

        let avai_balance_quote = 0;
        let freeze_balance_quote = 0;
        if (user_key.platform_name.toLowerCase() == 'binance') {
          if (balance.symbol === 'USDT') {
            avai_balance_quote =
              new Decimal(balance.avai_balance).toNumber() ?? 0;
            freeze_balance_quote =
              new Decimal(balance.freeze_balance).toNumber() ?? 0;
          } else {
            avai_balance_quote = pair
              ? new Decimal(balance.avai_balance)
                  .mul(new Decimal(pair.price))
                  .toNumber()
              : 0;
            freeze_balance_quote = pair
              ? new Decimal(balance.freeze_balance)
                  .mul(new Decimal(pair.price))
                  .toNumber()
              : 0;
          }
        } else if (user_key.platform_name.toLowerCase() == 'hata myr') {
          // if (balance.symbol == 'MYR') {
          let constant = await this.em
            .createQueryBuilder(Constant, 'constant')
            .select('constant.value')
            .where('constant.key = :key', { key: 'MYRRATE' })
            .getOne();

          const myrRate = constant ? constant.value : 4.4;

          avai_balance_quote = new Decimal(balance.avai_balance_quote)
            .div(new Decimal(myrRate))
            .toNumber();
          freeze_balance_quote = new Decimal(balance.freeze_balance_quote)
            .div(new Decimal(myrRate))
            .toNumber();
        } else {
          avai_balance_quote = new Decimal(
            balance.avai_balance_quote,
          ).toNumber();
          freeze_balance_quote = new Decimal(
            balance.freeze_balance_quote,
          ).toNumber();
          // }
        }

        // Check for existing records
        const existingBalances = await this.em
          .createQueryBuilder(SystemBalance, 'balance')
          .where(
            'balance.symbol = :symbol AND balance.user_key_id IN (:...userKeyIds)',
            {
              symbol: balance.symbol,
              userKeyIds,
            },
          )
          .getMany();

        // Map existing balances by user_key_id for quick lookup
        const balancesByKeyId = existingBalances.reduce((map, item) => {
          map[item.user_key_id] = item;
          return map;
        }, {});

        // Prepare batch of entities to save
        const balancesToSave = [];

        for (const keyId of userKeyIds) {
          if (balancesByKeyId[keyId]) {
            // Update existing
            const sb = balancesByKeyId[keyId];
            sb.avai_balance = balance.avai_balance;
            sb.freeze_balance = balance.freeze_balance;
            sb.avai_balance_quote = avai_balance_quote.toString();
            sb.freeze_balance_quote = freeze_balance_quote.toString();

            balancesToSave.push(sb);

          } else {
            // Create new
            const newBalance = this.em.create(SystemBalance, {
              symbol: balance.symbol,
              user_key_id: keyId,
              avai_balance: balance.avai_balance,
              freeze_balance: balance.freeze_balance,
              avai_balance_quote: avai_balance_quote.toString(),
              freeze_balance_quote: freeze_balance_quote.toString(),
            });

            console.log(
              `Created new system balance for symbol "${balance.symbol}", user_key_id ${keyId}: ` +
              `avai=${newBalance.avai_balance}, freeze=${newBalance.freeze_balance}`
            );

            balancesToSave.push(newBalance);

          }
        }

        // Save all in one batch
        await this.em.save(balancesToSave);
      }

      console.log("===Complete Update System Balance===");
      return { message: 'All balances updated successfully' };
    } catch (error) {
      console.log('CRITICAL ERROR in balance init:', error);
      throw error;
    }
  }
}
