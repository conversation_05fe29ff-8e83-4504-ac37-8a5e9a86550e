import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsIn, IsOptional, IsString } from 'class-validator';
import { Transform } from 'stream';
export class QueryBalanceDto {
  @ApiPropertyOptional({
    description: 'User Key ID associated with this balance',
    required: false,
  })
  @IsString()
  @IsOptional()
  user_key_id: string;

  @ApiPropertyOptional({
    description: 'Platform ID associated with this balance',
    required: false,
  })
  @IsString()
  @IsOptional()
  platform_id: string;

  @ApiPropertyOptional({
    description:
      'Set to "true" to include balances where relevant amounts are all zero. Defaults to hiding zero balances.', 
    required: false,
    enum: ['true', 'false'], 
  })
  @IsOptional()
  @IsString() 
  @IsIn(['true', 'false']) 
  show_zero_balance?: string; 
}


