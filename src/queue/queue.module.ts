import { BullModule, BullModuleOptions } from '@nestjs/bull';
import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import configuration from 'config/configuration';
import { StartGridQueueService } from './start-grid-queue.service';
import { PlaceOrderQueueService } from './place-order-queue.service';
import { CancelOrderQueueService } from './cancel-order-queue.service';
import { StopGridQueueService } from './stop-grid-queue.service';
import { UpdateOrderQueueService } from './update-order-queue.service';
import { StartArbitQueueService } from './start-arbit-queue.service';
import { StopArbitQueueService } from './stop-arbit-queue.service';
import { ArbitCheckingQueueService } from './checking-arbit-queue.service';
import { CreateTransactionQueueService } from './create-transaction-queue.service';
import { GridCheckingQueueService } from './checking-grid-queue.service';
import { CalculateRealizeQueueService } from './calculate_realize-queue.service';
import { CreateBotEventQueueService } from './create-bot-event-queue.service';
import { SocketDataQueueService } from './process-socket-queue.service';
import { CheckOrderStatusQueueService } from './check-order-status-queue.service';
import { BalanceUpdateQueueService } from './balance-update-queue.service';
import { BalanceService } from 'src/balance/balance.service';
import { SocketModule } from 'src/socket/socket.module';

const bullConfig: BullModuleOptions = {
  redis: {
    host: configuration().redis.host,
    port: configuration().redis.port,
    password: configuration().redis.password,
  },
};

@Module({
  imports: [
    SocketModule,
    BullModule.forRoot(bullConfig),

    BullModule.registerQueue(
      { name: 'start-grid' },
      { name: 'stop-grid' },
      { name: 'start-arbit' },
      { name: 'stop-arbit' },
      { name: 'place-order' },
      { name: 'cancel-order' },
      { name: 'arbit-checking' },
      { name: 'grid-checking' },
      { name: 'update-order' },
      { name: 'create-transaction' },
      { name: 'realize-checking' },
      { name: 'create-event' },
      { name: 'socket-data' },
      { name: 'check-order-status' },
      { name: 'balance-update' },
    ),
  ],
  providers: [
    CalculateRealizeQueueService,
    StartGridQueueService,
    StartArbitQueueService,
    PlaceOrderQueueService,
    CancelOrderQueueService,
    StopGridQueueService,
    StopArbitQueueService,
    UpdateOrderQueueService,
    ArbitCheckingQueueService,
    CreateTransactionQueueService,
    GridCheckingQueueService,
    CreateBotEventQueueService,
    SocketDataQueueService,
    CheckOrderStatusQueueService,
    BalanceUpdateQueueService,
    BalanceService
  ],
})
export class QueueModule {}
