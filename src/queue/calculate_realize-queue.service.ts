import {
  InjectQueue,
  OnQueueA<PERSON>,
  OnQueueError,
  OnQueueFailed,
  Process,
  Processor,
} from '@nestjs/bull';
import { BadRequestException } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import {
  calculateConstBaseAndQuote,
  decrypted,
  refreshConnection,
  truncateToDecimals,
} from 'common/util/utils';
import configuration from 'config/configuration';
import Decimal from 'decimal.js';
import Redis from 'ioredis';
import {
  ArbitBotStatus,
  ArbitrageBot,
} from 'src/arbitrage_bot/entities/arbitrage_bot.entity';
import { Gridbot, GridBotStatus } from 'src/gridbot/entities/gridbot.entity';
import { EntityManager, In, Not } from 'typeorm';

@Processor('realize-checking')
export class CalculateRealizeQueueService {
  constructor(private em: EntityManager) {}

  @Process('processing')
  async readOperationJob3(job: Job) {
    //call stream service
    //subscribe to redis-changes pair1 and pair2
    //keep current state and compare the price everytime price update from redis

    let data = job.data;
    console.log('Hello data', data);
    const matchedTradesQuery = `
    WITH parsed_matches AS (
      SELECT distinct
        transaction.matched_order,
        transaction.bot_type,
        transaction.uuid,
        SUBSTRING_INDEX(transaction.matched_order, '-', 1) AS buy_order_id,
        SUBSTRING_INDEX(transaction.matched_order, '-', -1) AS sell_order_id
        
      FROM 
        transaction
      WHERE 
        transaction.uuid = ?
        AND transaction.matched_order IS NOT NULL
        AND transaction.matched_order LIKE '%-%' 
        
    ),

    buy_trades AS (
      SELECT 
        pm.matched_order,
        t.match_id AS buy_order_id,
        SUM(t.price * t.amount)/SUM(t.amount) AS buy_price,
        SUM(t.amount) AS buy_amount,
        SUM(t.fee * t.price) AS buy_fee
      FROM 
        parsed_matches pm
      left JOIN 
        trade t ON t.match_id = pm.buy_order_id
      WHERE
        t.is_buy = TRUE group by t.match_id , pm.matched_order
    ),

    sell_trades AS (
      SELECT 
        pm.matched_order,
        t.match_id AS sell_order_id,
        SUM(t.price * t.amount)/SUM(t.amount) AS sell_price,
        SUM(t.amount) AS sell_amount,
        SUM(t.fee) AS sell_fee
      FROM 
        parsed_matches pm
      left JOIN 
        trade t ON t.match_id = pm.sell_order_id 
      WHERE
        t.is_buy = FALSE group by t.match_id , pm.matched_order
    )

    SELECT
      pm.matched_order,
      pm.bot_type,
      pm.uuid,
      bt.buy_order_id,
      st.sell_order_id,
      CAST(bt.buy_price AS CHAR) as buy_price,
      CAST(bt.buy_amount AS CHAR) as buy_amount,
      CAST(bt.buy_fee AS CHAR)  as buy_fee,
      CAST(st.sell_price AS CHAR) as sell_price,
      CAST(st.sell_amount AS CHAR) as sell_amount,
      CAST(st.sell_fee AS CHAR) as sell_fee,
      CAST((st.sell_price * st.sell_amount) - (bt.buy_price * bt.buy_amount) AS CHAR) AS gross_pnl,
      CAST((bt.buy_fee + st.sell_fee) AS CHAR) AS total_fees,
      CAST((st.sell_price * st.sell_amount) - (bt.buy_price * bt.buy_amount) - (bt.buy_fee + st.sell_fee) AS CHAR) AS net_pnl
    FROM
    parsed_matches pm
    LEFT JOIN
        buy_trades bt ON pm.matched_order = bt.matched_order
      LEFT JOIN
        sell_trades st ON pm.matched_order = st.matched_order
      WHERE
        bt.buy_amount = st.sell_amount and
        (bt.buy_order_id IS NOT NULL
        OR st.sell_order_id IS NOT NULL)
      ORDER BY
        pm.matched_order;
    
    `;

    // Execute the raw query with parameters
    let matchedTrades = await this.em.query(matchedTradesQuery, [data.uuid]);
    if (matchedTrades.length != 0) {
      //if bot type = grid then go to gridbot table, else go to arbitragebot table
      let bot_detail;
      if (matchedTrades[0].bot_type === 'GRIDBOT') {
        bot_detail = await this.em
          .createQueryBuilder(Gridbot, 'gb')
          .where('gb.uuid = :uuid', {
            uuid: matchedTrades[0].uuid,
          })
          .withDeleted()
          .getOne();
      } else if (matchedTrades[0].bot_type === 'ARBITRAGEBOT') {
        bot_detail = await this.em
          .createQueryBuilder(ArbitrageBot, 'ab')
          .where('ab.uuid = :uuid', {
            uuid: matchedTrades[0].uuid,
          })
          .withDeleted()
          .getOne();
      }
      if (!bot_detail) {
        throw new BadRequestException('Bot not found');
      }
      let total_pnl = matchedTrades.reduce((acc, curr) => {
        return acc + Number(curr.net_pnl);
      }, 0);

      await this.em
        .createQueryBuilder()
        .update(
          matchedTrades[0].bot_type === 'GRIDBOT' ? Gridbot : ArbitrageBot,
        )
        .set({
          realized_profit: total_pnl.toFixed(4),
        })
        .where(`uuid = :uuid `, {
          uuid: matchedTrades[0].uuid,
        })
        .execute();
    }
  }

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} start grid of type ${job.name} with data ${job.data}...`,
    );
  }

  @OnQueueError()
  onError(job: Job) {
    console.log(job);
    console.log(
      `Processing job ${job.id} error grid of type ${job.name} with data ${job.data}...`,
    );
  }

  @OnQueueFailed()
  onFail(job: Job) {
    console.log(job);
    console.log(
      `Processing job ${job.id} fail grid of type ${job.name} with data ${job.data}...`,
    );
  }
}
