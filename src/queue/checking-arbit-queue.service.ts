import {
  InjectQueue,
  OnQueueActive,
  OnQueueError,
  OnQueueFailed,
  Process,
  Processor,
} from '@nestjs/bull';
import { BadRequestException } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import {
  calculateConstBaseAndQuote,
  decrypted,
  refreshConnection,
  truncateToDecimals,
} from 'common/util/utils';
import configuration from 'config/configuration';
import Decimal from 'decimal.js';
import Redis from 'ioredis';
import {
  ArbitBotStatus,
  ArbitrageBot,
} from 'src/arbitrage_bot/entities/arbitrage_bot.entity';
import { Gridbot, GridBotStatus } from 'src/gridbot/entities/gridbot.entity';
import {
  Transaction,
  TransactionStatus,
} from 'src/transactions/entities/transaction.entity';
import { EntityManager, In, Not } from 'typeorm';
import { CreateBotEventQueueService } from './create-bot-event-queue.service';
import { Trade } from 'src/trade/entities/trade.entity';

@Processor('arbit-checking')
export class ArbitCheckingQueueService {
  private redisClient: Redis;
  constructor(
    private em: EntityManager,
    @InjectQueue('place-order') private placeOrderQueue: Queue,
    @InjectQueue('create-event') private createEventQueue: Queue,
  ) {
    this.redisClient = new Redis({
      host: configuration().redis.host,
      password: configuration().redis.password,
    });
  }

  @Process('check-criterea')
  async readOperationJob3(job: Job) {
    //call stream service
    //subscribe to redis-changes pair1 and pair2
    //keep current state and compare the price everytime price update from redis

    let data = job.data.rawData;
    // let queryBuilder = this.em
    //   .createQueryBuilder(ArbitrageBot, 'ab')
    //   .leftJoin(
    //     'user_key',
    //     'base_user_key',
    //     'ab.base_key_id = base_user_key.id',
    //   )
    //   .leftJoin(
    //     'user_key',
    //     'target_user_key',
    //     'ab.target_key_id = target_user_key.id',
    //   )
    //   .leftJoin('user', 'user', 'base_user_key.user_id = user.id')
    //   .leftJoin(
    //     'platform',
    //     'base_platform',
    //     'ab.base_platform_id = base_platform.id',
    //   )
    //   .leftJoin(
    //     'platform',
    //     'target_platform',
    //     'ab.target_platform_id = target_platform.id',
    //   )
    //   .leftJoin('pair', 'base_pair', 'ab.base_pair_id = base_pair.id ')
    //   .leftJoin('pair', 'target_pair', 'ab.target_pair_id = target_pair.id ')
    //   // .where(" ab.status in ('ACTIVE', 'BUY_ACTIVE', 'SELL_ACTIVE')")
    //   .where(" ab.status in ('ACTIVE')")
    //   .andWhere('ab.uuid = :uuid', {
    //     uuid: data.uuid,
    //   });

    // if (data.side === 'buy') {
    //   queryBuilder.andWhere('ab.base_pair_id = :pair_id', {
    //     pair_id: data.id,
    //   });
    // } else {
    //   queryBuilder.andWhere('ab.target_pair_id = :pair_id', {
    //     pair_id: data.id,
    //   });
    // }

    // let arbitrageBots = await queryBuilder
    //   .select([
    //     'ab.id as id',
    //     'ab.name as name',
    //     'ab.uuid as uuid',
    //     'ab.percentage as percentage',
    //     'ab.rate as rate',
    //     'ab.type as type',
    //     'ab.initial_fund as initial_fund',
    //     'ab.status as status',
    //   ])
    //   .addSelect(
    //     `JSON_OBJECT(
    //     'id', base_platform.id,
    //     'name', base_platform.name
    //   )`,
    //     'base_platform',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //     'id', target_platform.id,
    //     'name', target_platform.name
    //   )`,
    //     'target_platform',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //     'id', base_user_key.id,
    //     'api_key', base_user_key.api_key,
    //     'api_secret', base_user_key.api_secret,
    //     'taker_fee', base_user_key.taker_fee,
    //     'maker_fee', base_user_key.maker_fee
    //   )`,
    //     'base_user_key',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //     'id', target_user_key.id,
    //     'api_key', target_user_key.api_key,
    //     'api_secret', target_user_key.api_secret,
    //     'taker_fee', target_user_key.taker_fee,
    //     'maker_fee', target_user_key.maker_fee
    //   )`,
    //     'target_user_key',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //     'id', base_pair.id,
    //     'symbol', base_pair.symbol,
    //     'min_notional', base_pair.min_notional,
    //     'price_dp', base_pair.price_dp,
    //     'qty_dp', base_pair.qty_dp,
    //     'base', base_pair.base,
    //     'quote', base_pair.quote,
    //     'tick', CAST(base_pair.tick AS CHAR),
    //     'step', CAST(base_pair.step AS CHAR),
    //     'symbol', base_pair.symbol
    //   )`,
    //     'base_pair',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //     'id', target_pair.id,
    //     'symbol', target_pair.symbol,
    //     'min_notional', target_pair.min_notional,
    //     'price_dp', target_pair.price_dp,
    //     'qty_dp', target_pair.qty_dp,
    //     'base', target_pair.base,
    //     'quote', target_pair.quote,
    //     'tick', CAST(target_pair.tick AS CHAR),
    //     'step', CAST(target_pair.step AS CHAR),
    //     'symbol', target_pair.symbol
    //   )`,
    //     'target_pair',
    //   )
    //   .getRawMany();
    let base_pair = {
      id: data[0].base_pair_id,
      symbol: data[0].base_pair_symbol,
      min_notional: data[0].base_pair_min_notional,
      price_dp: data[0].base_pair_price_dp,
      qty_dp: data[0].base_pair_qty_dp,
      base: data[0].base_pair_base,
      quote: data[0].base_pair_quote,
      tick: data[0].base_pair_tick,
      step: data[0].base_pair_step,
    };
    let target_pair = {
      id: data[0].target_pair_id,
      symbol: data[0].target_pair_symbol,
      min_notional: data[0].target_pair_min_notional,
      price_dp: data[0].target_pair_price_dp,
      qty_dp: data[0].target_pair_qty_dp,
      base: data[0].target_pair_base,
      quote: data[0].target_pair_quote,
      tick: data[0].target_pair_tick,
      step: data[0].target_pair_step,
    };
    for (const arbitrage_bot of data) {
      try {
        const rate = new Decimal(arbitrage_bot.rate);
        const percentage = new Decimal(arbitrage_bot.percentage);

        // 1. fetch both sides' ask/bid
        const [baseAskRaw, baseBidRaw, targetAskRaw, targetBidRaw] =
          await Promise.all([
            this.redisClient.get(`${base_pair.id}:ask`),
            this.redisClient.get(`${base_pair.id}:bid`),
            this.redisClient.get(`${target_pair.id}:ask`),
            this.redisClient.get(`${target_pair.id}:bid`),
          ]);
        if (!baseAskRaw || !baseBidRaw || !targetAskRaw || !targetBidRaw) {
          continue;
        }

        let baseAsk, baseBid, targetAsk, targetBid;
        try {
          // Parse the raw JSON data
          const baseAskData = JSON.parse(baseAskRaw);
          const baseBidData = JSON.parse(baseBidRaw);
          const targetAskData = JSON.parse(targetAskRaw);
          const targetBidData = JSON.parse(targetBidRaw);

          // Check if all necessary price properties exist
          if (
            Date.now() - baseAskData.last_updated > 10000 ||
            Date.now() - baseBidData.last_updated > 10000 ||
            Date.now() - targetAskData.last_updated > 10000 ||
            Date.now() - targetBidData.last_updated > 10000
          ) {
            console.log('Skipping iteration: Data is too old');
            // continue; // or continue if inside a loop
          }

          // If all checks pass, create the Decimal instances
          baseAsk = new Decimal(baseAskData.price);
          baseBid = new Decimal(baseBidData.price);
          targetAsk = new Decimal(targetAskData.price);
          targetBid = new Decimal(targetBidData.price);

          // Continue with your processing here...
        } catch (error) {
          console.log('Error processing price data:', error.message);
          continue; // or continue if inside a loop
        }
        // 2. compute thresholds
        //Buy on base → Sell on target
        // const forwardThreshold = baseAsk
        //   .mul(rate)
        //   .mul(Decimal(1).add(percentage)); // need targetBid ≥ this to profit
        //Sell on base → Buy on target
        const forwardThreshold = baseAsk
          .mul(rate)
          .minus(targetBid)
          .div(targetBid);
        // const reverseThreshold = targetAsk
        //   .div(rate)
        //   .div(Decimal(1).add(percentage)); // need baseBid ≥ this to profit
        const reverseThreshold = targetAsk
          .div(rate)
          .minus(baseBid)
          .div(baseBid);
        let baseCaller = callPlatformHelper(
          arbitrage_bot.base_platform.name.toLowerCase(),
        );
        await baseCaller.init(
          arbitrage_bot.base_platform.name.toLowerCase(),
          arbitrage_bot.base_user_key.api_key,
          arbitrage_bot.base_user_key.api_secret,
        );

        let targetCaller = callPlatformHelper(
          arbitrage_bot.target_platform.name.toLowerCase(),
        );
        await targetCaller.init(
          arbitrage_bot.target_platform.name.toLowerCase(),
          arbitrage_bot.target_user_key.api_key,
          arbitrage_bot.target_user_key.api_secret,
        );

        console.log('forwardThreshold', forwardThreshold.toNumber());
        console.log('targetBid', targetBid.toNumber());
        console.log('reverseThreshold', reverseThreshold.toNumber());
        console.log('baseBid', baseBid.toNumber());

        // 3a. FORWARD ARBITRAGE: buy on base @ask → sell on target @bid
        if (
          arbitrage_bot.status === 'ACTIVE' &&
          // arbitrage_bot.status === 'BUY_ACTIVE') &&
          reverseThreshold.greaterThanOrEqualTo(percentage)
        ) {
          // init callers

          // check balances
          console.log('forward arbitrage');
          const baseBalances = await baseCaller.readBalance(
            base_pair.quote,
            arbitrage_bot.base_platform.name.toLowerCase(),
          );
          const targetBalances = await targetCaller.readBalance(
            target_pair.base,
            arbitrage_bot.target_platform.name.toLowerCase(),
          );
          const baseQuoteBal = baseBalances.find(
            (b) => b.symbol === base_pair.quote,
          ).avai_balance;
          const targetBaseBal = targetBalances.find(
            (b) => b.symbol === target_pair.base,
          ).avai_balance;

          console.log('baseQuoteBal', baseQuoteBal);
          console.log('targetBaseBal', targetBaseBal);
          console.log('arbitrage_bot.initial_fund', arbitrage_bot.initial_fund);
          console.log('targetBid', targetBid.toNumber());
          console.log(
            new Decimal(baseQuoteBal).greaterThanOrEqualTo(
              arbitrage_bot.initial_fund,
            ) &&
              new Decimal(targetBaseBal).greaterThanOrEqualTo(
                new Decimal(arbitrage_bot.initial_fund).div(targetBid),
              ),
          );
          // ensure minimum funds
          if (
            !(
              new Decimal(baseQuoteBal).greaterThanOrEqualTo(
                arbitrage_bot.initial_fund,
              ) &&
              new Decimal(targetBaseBal).greaterThanOrEqualTo(
                new Decimal(arbitrage_bot.initial_fund).div(targetBid),
              )
            )
          ) {
            console.log('insufficient balance');
            // await this.em
            //   .createQueryBuilder()
            //   .update(ArbitrageBot)
            //   .set({ status: ArbitBotStatus.INSUFFICIENT })
            //   .where('uuid = :uuid', { uuid: arbitrage_bot.uuid })
            //   .execute();
            continue;
          }

          // compute amounts
          let maxBaseQty = Decimal.min(
            baseQuoteBal / JSON.parse(baseAskRaw).price,
            JSON.parse(baseAskRaw).qty,
          );
          let maxTargetQty = this.getAvailableQuantity(
            JSON.parse(baseAskRaw),
            JSON.parse(targetBidRaw),
            targetBaseBal,
            maxBaseQty,
          );
          console.log('maxBaseQty', maxBaseQty.toNumber());
          console.log('maxTargetQty', maxTargetQty.toNumber());
          if (maxTargetQty.mul(targetBid).lessThan(maxBaseQty)) {
            maxBaseQty = maxTargetQty.mul(targetBid);
          }

          // enqueue buy on base
          const buyPayload = {
            platform: arbitrage_bot.base_platform,
            user_key: arbitrage_bot.base_user_key,
            order: {
              symbol: base_pair.symbol,
              side: 'BUY',
              type: 'MARKET',
              price: baseAsk.toNumber(),
            },
            uuid: arbitrage_bot.uuid,
            amount: truncateToDecimals(maxTargetQty, base_pair.qty_dp),
            bot_type: 'ARBITRAGEBOT',
            is_buy: true,
            pair: base_pair,
            fee_pct: arbitrage_bot.fee_pct,
          };
          buyPayload.order[
            arbitrage_bot.base_platform.name.toLowerCase() === 'binance'
              ? 'quoteOrderQty'
              : 'quote_qty'
          ] = truncateToDecimals(
            maxTargetQty.mul(baseAsk.toNumber()),
            base_pair.price_dp,
          );

          // enqueue sell on target
          const sellPayload = {
            platform: arbitrage_bot.target_platform,
            user_key: arbitrage_bot.target_user_key,
            order: {
              symbol: target_pair.symbol,
              side: 'SELL',
              type: 'MARKET',
              price: targetBid.toNumber(),
            },
            uuid: arbitrage_bot.uuid,
            amount: truncateToDecimals(maxTargetQty, target_pair.qty_dp),
            bot_type: 'ARBITRAGEBOT',
            is_buy: false,
            pair: target_pair,
            fee_pct: arbitrage_bot.fee_pct,
          };
          sellPayload.order[
            arbitrage_bot.target_platform.name.toLowerCase() === 'binance'
              ? 'quoteOrderQty'
              : 'quote_qty'
          ] = truncateToDecimals(maxTargetQty, target_pair.qty_dp);
          console.log(
            'buy on base @ask → sell on target @bid',
            buyPayload,
            sellPayload,
          );
          try {
            let buy_matched_order = 'trade' + Date.now();
            let sell_matched_order = 'trade' + Date.now() + 1;
            await this.placeOrder(
              buyPayload,
              buyPayload,
              buy_matched_order,
              sell_matched_order,
            );
            await this.placeOrder(
              sellPayload,
              sellPayload,
              buy_matched_order,
              sell_matched_order,
            );
          } catch (error) {
            console.log(error);
            return;
          }
          // await this.placeOrderQueue.add('processing', buyPayload);
          // await this.placeOrderQueue.add('processing', sellPayload);

          // Placing Order bot event
          await this.createEventQueue.add(
            'processing',
            {
              botType: 'arbitragebot',
              uuid: arbitrage_bot.uuid,
              eventType: CreateBotEventQueueService.EVENT_TYPES.PLACING_ORDER,
              params: [
                buyPayload.order.side, // BUY
                1, // index or sequence number if available
                2, // total orders in this arbitrage cycle if available
                buyPayload.order.price,
                buyPayload.pair?.quote,
                buyPayload.amount,
                buyPayload.pair?.base,
                //buyPayload.pair is storing plain JS Obj query from DB, got quote and base already
              ],
            },
            { removeOnComplete: true },
          );

          await this.createEventQueue.add(
            'processing',
            {
              botType: 'arbitragebot',
              uuid: arbitrage_bot.uuid,
              eventType: CreateBotEventQueueService.EVENT_TYPES.PLACING_ORDER,
              params: [
                sellPayload.order.side, // SELL
                2, // index or sequence number if available
                2, // total orders in this arbitrage cycle if available
                sellPayload.order.price,
                sellPayload.pair?.quote,
                sellPayload.amount,
                sellPayload.pair?.base,
              ],
            },
            { removeOnComplete: true },
          );

          // await this.em
          //   .createQueryBuilder()
          //   .update(ArbitrageBot)
          //   .set({ status: ArbitBotStatus.SELL_ACTIVE })
          //   .where('uuid = :uuid', { uuid: arbitrage_bot.uuid })
          //   .execute();

          // 3b. REVERSE ARBITRAGE: buy on target @ask → sell on base @bid
        } else if (
          arbitrage_bot.status === 'ACTIVE' &&
          // arbitrage_bot.status === 'SELL_ACTIVE') &&
          forwardThreshold.greaterThanOrEqualTo(percentage)
        ) {
          // init callers with swapped roles

          // check balances (note swapped)
          console.log('reverse arbitrage');
          const targetBalances = await targetCaller.readBalance(
            target_pair.quote,
            arbitrage_bot.target_platform.name.toLowerCase(),
          );
          const baseBalances = await baseCaller.readBalance(
            base_pair.base,
            arbitrage_bot.base_platform.name.toLowerCase(),
          );
          const targetQuoteBal = targetBalances.find(
            (b) => b.symbol === target_pair.quote,
          ).avai_balance;
          const baseBaseBal = baseBalances.find(
            (b) => b.symbol === base_pair.base,
          ).avai_balance;
          console.log('targetQuoteBal', targetQuoteBal);
          console.log('baseBaseBal', baseBaseBal);
          console.log('arbitrage_bot.initial_fund', arbitrage_bot.initial_fund);
          console.log('baseBid', baseBid.toNumber());
          console.log(
            new Decimal(targetQuoteBal).greaterThanOrEqualTo(
              arbitrage_bot.initial_fund,
            ) &&
              new Decimal(baseBaseBal).greaterThanOrEqualTo(
                new Decimal(arbitrage_bot.initial_fund).div(baseBid),
              ),
          );

          if (
            !(
              new Decimal(targetQuoteBal).greaterThanOrEqualTo(
                arbitrage_bot.initial_fund,
              ) &&
              new Decimal(baseBaseBal).greaterThanOrEqualTo(
                new Decimal(arbitrage_bot.initial_fund).div(baseBid),
              )
            )
          ) {
            console.log('insufficient balance');
            // await this.em
            //   .createQueryBuilder()
            //   .update(ArbitrageBot)
            //   .set({ status: ArbitBotStatus.INSUFFICIENT })
            //   .where('uuid = :uuid', { uuid: arbitrage_bot.uuid })
            //   .execute();
            continue;
          }

          // compute amounts for reverse flow
          let maxTargetQty = Decimal.min(
            targetQuoteBal,
            JSON.parse(targetAskRaw).qty,
          );
          let maxBaseQty = this.getAvailableQuantity(
            JSON.parse(targetAskRaw),
            JSON.parse(baseBidRaw),
            baseBaseBal,
            maxTargetQty,
          );
          console.log('maxBaseQty', maxBaseQty.toNumber());
          console.log('maxTargetQty', maxTargetQty.toNumber());
          if (maxBaseQty.mul(baseBid).lessThan(maxTargetQty)) {
            maxTargetQty = maxBaseQty.div(baseBid);
          }

          // enqueue buy on target
          const buyOnTarget = {
            platform: arbitrage_bot.target_platform,
            user_key: arbitrage_bot.target_user_key,
            order: {
              symbol: target_pair.symbol,
              side: 'BUY',
              type: 'MARKET',
              price: targetAsk.toNumber(),
            },
            uuid: arbitrage_bot.uuid,
            amount: truncateToDecimals(maxTargetQty, target_pair.qty_dp),
            bot_type: 'ARBITRAGEBOT',
            is_buy: true,
            pair: target_pair,
            fee_pct: arbitrage_bot.fee_pct,
          };
          buyOnTarget.order[
            arbitrage_bot.target_platform.name.toLowerCase() === 'binance'
              ? 'quoteOrderQty'
              : 'quote_qty'
          ] = truncateToDecimals(maxTargetQty, target_pair.qty_dp);

          // enqueue sell on base
          const sellOnBase = {
            platform: arbitrage_bot.base_platform,
            user_key: arbitrage_bot.base_user_key,
            order: {
              symbol: base_pair.symbol,
              side: 'SELL',
              type: 'MARKET',
              price: baseBid.toNumber(),
            },
            uuid: arbitrage_bot.uuid,
            amount: truncateToDecimals(maxBaseQty, base_pair.qty_dp),
            bot_type: 'ARBITRAGEBOT',
            is_buy: false,
            pair: base_pair,
            fee_pct: arbitrage_bot.fee_pct,
          };
          sellOnBase.order[
            arbitrage_bot.base_platform.name.toLowerCase() === 'binance'
              ? 'quoteOrderQty'
              : 'quote_qty'
          ] = truncateToDecimals(maxTargetQty, target_pair.qty_dp);
          console.log(
            'buy on target @ask → sell on base @bid',
            buyOnTarget,
            sellOnBase,
          );
          // place order
          try {
            let buy_matched_order = 'trade' + Date.now();
            let sell_matched_order = 'trade' + Date.now() + 1;
            await this.placeOrder(
              buyOnTarget,
              buyOnTarget,
              buy_matched_order,
              sell_matched_order,
            );
            await this.placeOrder(
              sellOnBase,
              sellOnBase,
              buy_matched_order,
              sell_matched_order,
            );
          } catch (error) {
            console.log(error);
            return;
          }
          // create transaction (revert when place order fail)
          // create trade (revert when place order fail)
          // await this.placeOrderQueue.add('processing', buyOnTarget);
          // await this.placeOrderQueue.add('processing', sellOnBase);

          // Placing Order bot event
          await this.createEventQueue.add(
            'processing',
            {
              botType: 'arbitragebot',
              uuid: arbitrage_bot.uuid,
              eventType: CreateBotEventQueueService.EVENT_TYPES.PLACING_ORDER,
              params: [
                buyOnTarget.order.side, // 'BUY'
                1, // index
                2, // total orders
                buyOnTarget.order.price,
                buyOnTarget.pair?.quote,
                buyOnTarget.amount,
                buyOnTarget.pair?.base,
              ],
            },
            { removeOnComplete: true },
          );

          // After building sellOnBase
          await this.createEventQueue.add(
            'processing',
            {
              botType: 'arbitragebot',
              uuid: arbitrage_bot.uuid,
              eventType: CreateBotEventQueueService.EVENT_TYPES.PLACING_ORDER,
              params: [
                sellOnBase.order.side, // 'SELL'
                2, // index
                2, // total orders
                sellOnBase.order.price,
                sellOnBase.pair?.quote,
                sellOnBase.amount,
                sellOnBase.pair?.base,
              ],
            },
            { removeOnComplete: true },
          );

          // await this.em
          //   .createQueryBuilder()
          //   .update(ArbitrageBot)
          //   .set({ status: ArbitBotStatus.BUY_ACTIVE })
          //   .where('uuid = :uuid', { uuid: arbitrage_bot.uuid })
          //   .execute();
        } else {
          // no arbitrage opportunity
          continue;
        }
      } catch (error) {
        console.log(error);
        await this.em
          .createQueryBuilder()
          .update(ArbitrageBot)
          .set({ status: ArbitBotStatus.ERROR })
          .where('uuid = :uuid', { uuid: arbitrage_bot.uuid })
          .execute();
        console.log(error);
      }
    }
  }

  getAvailableQuantity(buy_price, sell_price, quote_balance, available_base) {
    // Calculate available quantity by dividing buy quantity by buy price

    const available_buy_qty = Decimal(available_base);

    // Get the sell quantity
    const available_sell_qty = sell_price.qty;
    // Return the lower value between the two
    return Decimal.min(available_buy_qty, available_sell_qty, quote_balance);
  }

  async createTransaction(
    order_id,
    transaction_object,
    buy_matched_order,
    sell_matched_order,
  ) {
    try {
      let transaction = new Transaction();
      transaction.uuid = transaction_object.uuid;
      transaction.price = transaction_object.order.price;
      transaction.amount = transaction_object.amount.toString();
      transaction.matched_order = buy_matched_order + '-' + sell_matched_order;
      //matched_order
      transaction.is_buy = transaction_object.is_buy;
      transaction.order_id = order_id;
      transaction.bot_type = transaction_object.bot_type;
      // transaction.fee = transaction_object.fee;
      transaction.status = TransactionStatus.COMPLETED;
      transaction.target_pair_id = transaction_object.pair.id;
      transaction.user_key_id = transaction_object.user_key.id;
      transaction.platform_id = transaction_object.platform.id;
      await this.em.save(Transaction, transaction);
    } catch (error) {
      console.log(error);
    }
  }

  async createTrade(transaction, matched_order) {
    try {
      let trade = new Trade();
      trade.uuid = transaction.uuid;
      trade.price = transaction.order.price;
      trade.amount = transaction.amount.toString();
      trade.quote_amount = Decimal(transaction.order.price)
        .mul(transaction.amount)
        .toString();
      trade.pair_id = transaction.pair.id;
      trade.is_buy = transaction.is_buy;
      trade.bot_type = transaction.bot_type;
      trade.fee = transaction.is_buy
        ? new Decimal(transaction.user_key.taker_fee)
            .mul(trade.quote_amount)
            .toString()
        : new Decimal(transaction.user_key.taker_fee)
            .mul(transaction.amount)
            .toString();
      trade.fee_currency = transaction.is_buy
        ? transaction.pair.quote
        : transaction.pair.base;
      trade.platform_id = transaction.platform.id;
      trade.match_id = matched_order;
      trade.trade_time = new Date();
      await this.em.save(Trade, trade);
    } catch (error) {
      console.log(error);
    }
  }

  async placeOrder(
    order_object,
    transaction_object,
    buy_matched_order,
    sell_matched_order,
  ) {
    let platform = order_object.platform;
    let platformHelper = await callPlatformHelper(platform.name.toLowerCase());
    let user_key = order_object.user_key;
    let order = order_object.order;
    await platformHelper.init(
      platform.name.toLowerCase(),
      user_key.api_key,
      user_key.api_secret,
    );

    let place_order = await platformHelper.createOrder(order);
    await this.createTransaction(
      place_order.id,
      transaction_object,
      buy_matched_order,
      sell_matched_order,
    );
    await this.createTrade(
      transaction_object,
      transaction_object.is_buy ? buy_matched_order : sell_matched_order,
    );
    return place_order;
  }

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} start grid of type ${job.name} with data ${job.data}...`,
    );
  }

  @OnQueueError()
  onError(job: Job) {
    console.log(job);
    console.log(
      `Processing job ${job.id} error grid of type ${job.name} with data ${job.data}...`,
    );
  }

  @OnQueueFailed()
  onFail(job: Job) {
    console.log(job);
    console.log(
      `Processing job ${job.id} fail grid of type ${job.name} with data ${job.data}...`,
    );
  }
}
