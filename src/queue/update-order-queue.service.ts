import { InjectQueue, OnQueueActive, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import { decrypted, truncateToDecimals } from 'common/util/utils';
import configuration from 'config/configuration';
import Decimal from 'decimal.js';
import moment from 'moment-timezone';
import {
  ArbitrageBot,
  ArbitBotStatus,
} from 'src/arbitrage_bot/entities/arbitrage_bot.entity';
import { Gridbot, GridBotStatus } from 'src/gridbot/entities/gridbot.entity';
import { Trade } from 'src/trade/entities/trade.entity';
import {
  Transaction,
  TransactionStatus,
} from 'src/transactions/entities/transaction.entity';
import { EntityManager, In, Not } from 'typeorm';
import { CreateBotEventQueueService } from './create-bot-event-queue.service';

@Processor('update-order')
export class UpdateOrderQueueService {
  constructor(
    private em: EntityManager,
    @InjectQueue('create-transaction') private createTransactionQueue: Queue,
    @InjectQueue('create-event') private createEventQueue: Queue,
    @InjectQueue('realize-checking') private realizeCheckingQueue: Queue,
    @InjectQueue('balance-update') private balanceUpdateQueue: Queue,
    @InjectQueue('place-order') private placeOrderQueue: Queue,
  ) {}

  @Process('processing')
  async readOperationJob3(job: Job) {
    let data = job.data;
    let queryBuilder, bot;
    let transaction;
    try {
      transaction = await this.em
        .createQueryBuilder(Transaction, 't')
        .where(
          "t.order_id = :order_id and status not in ('CANCELLED','COMPLETED')",
          { order_id: data.order_id },
        )
        .getOne();
      if (!transaction || transaction.bot_type != 'GRIDBOT') {
        return;
      }

      if (data.type === 'cancel') {
        switch (transaction.bot_type) {
          case 'GRIDBOT': {
            queryBuilder = this.em
              .createQueryBuilder(Gridbot, 'gb')
              .leftJoin('user_key', 'user_key', 'gb.user_key_id = user_key.id')
              .leftJoin('user', 'user', 'user_key.user_id = user.id')
              .leftJoin('platform', 'platform', 'gb.platform_id = platform.id')
              .leftJoin('pair', 'pair', 'gb.base_pair_id = pair.id ')
              .where(`gb.uuid = :uuid and gb.status = 'ACTIVE'`, {
                uuid: transaction.uuid,
              });

            bot = await queryBuilder
              .select([
                'gb.id as id',
                'gb.name as name',
                'gb.uuid as uuid',
                'gb.lower_threshold as lower_threshold',
                'gb.upper_threshold as upper_threshold',
                'gb.step as step',
                'gb.initial_fund as initial_fund',
                'gb.sell_at_stop as sell_at_stop',
                'gb.status as status',
              ]) // 'gb.fee_pct as fee_pct' is not used in future
              .addSelect(
                `JSON_OBJECT(
            'id', platform.id,
            'name', platform.name
          )`,
                'platform',
              )
              .addSelect(
                `JSON_OBJECT(
            'id', user_key.id,
            'api_key', user_key.api_key,
            'api_secret', user_key.api_secret,
            'maker_fee', user_key.maker_fee
          )`, //taker fee is not used in gridbot
                'user_key',
              )
              .addSelect(
                `JSON_OBJECT(
            'id', pair.id,
            'symbol', pair.symbol,
            'min_notional', pair.min_notional,
            'price_dp', pair.price_dp,
            'qty_dp', pair.qty_dp,
            'base', pair.base,
            'quote', pair.quote,
            'tick', CAST(pair.tick AS CHAR),
            'step', CAST(pair.step AS CHAR),
            'symbol', pair.symbol
          )`,
                'pair',
              )
              .getRawOne();
            break;
          }

          case 'ARBITRAGEBOT': {
            // const bot = await this.em
            //   .createQueryBuilder(Arbitragebot, 'ab')
            //   // .where(...) if needed
            //   .getOne();
            break;
          }

          case 'PRICEBOT': {
            // const bot = await this.em
            //   .createQueryBuilder(Pricebot, 'pb')
            //   // .where(...) if needed
            //   .getOne();
            break;
          }
        }

        // Update transaction status first
        transaction.status = TransactionStatus.CANCELLED;
        await this.em.save(Transaction, transaction);

        // BALANCE UPDATE: Handle order cancellation - unfreeze the funds
        if (bot) {
          //Balance Cancellation for BUY order
          if (transaction.is_buy) {
            const actualAmount = new Decimal(transaction.amount) // Use amount - previous amount to make sure the amount is net
              .minus(transaction.prv_amount)
              .minus(transaction.exc_amount);
            const currencyAmount = truncateToDecimals(
              actualAmount.mul(transaction.price),
              bot.pair.price_dp,
            ).toString();

            // Order cancellation (buy)
            await this.balanceUpdateQueue.add('processing', {
              user_key_id: bot.user_key.id,
              symbol: bot.pair.quote,
              available_balance_delta: currencyAmount, // Buy order should use currency like MYR, so get the net amount(qty) and * the price to get how many currency need
              freeze_balance_delta: new Decimal(currencyAmount)
                .negated()
                .toString(),
              fee: transaction.fee,
              price_dp: bot.pair.price_dp,
            });
          } else {
            //Balance Cancellation for SELL order

            const baseAmount = new Decimal(transaction.amount)
              .minus(transaction.prv_amount)
              .minus(transaction.exc_amount)
              .toString();

            await this.balanceUpdateQueue.add('processing', {
              user_key_id: bot.user_key.id,
              symbol: bot.pair.base,
              available_balance_delta: baseAmount, // Sell order should use currency like XRP, so get the net amount(qty)
              freeze_balance_delta: new Decimal(baseAmount)
                .negated()
                .toString(), // Minus base freeze XRP
              fee: transaction.fee,
              price_dp: bot.pair.price_dp,
            });
          }
        } else {
          console.log('no bot was found');
          return;
        }

        if (transaction.bot_type == 'MANUAL') {
          return;
        }

        // Create new transaction for the remaining amount
        await this.createTransactionQueue.add('processing', {
          platform: bot.platform,
          user_key: bot.user_key,
          order: {
            symbol: bot.pair.symbol,
            side: transaction.is_buy ? 'BUY' : 'SELL',
            type: 'LIMIT',
            quantity: Decimal(transaction.amount)
              .minus(transaction.exc_amount)
              .minus(transaction.prv_amount),
            price: transaction.price,
          },
          uuid: bot.uuid,
          amount: transaction.amount,
          fee_pct: bot.user_key.maker_fee, // use maker_fee to replace fee_pct
          prv_amount: Decimal(transaction.exc_amount).plus(
            transaction.prv_amount,
          ),
          bot_type: transaction.bot_type,
          is_buy: transaction.is_buy,
          pair: bot.pair,
        });

        await this.createEventQueue.add(
          'processing',
          {
            botType: 'gridbot',
            uuid: bot.uuid,
            eventType: CreateBotEventQueueService.EVENT_TYPES.ORDER_CANCELLED,
            params: [],
          },
          { removeOnComplete: true },
        );
      } else if (data.type === 'trade') {
        if (Decimal(transaction.exc_amount).eq(data.exec_qty)) {
          console.log('Same transaction');
          return;
        }

        queryBuilder = this.em
          .createQueryBuilder(Gridbot, 'gb')
          .leftJoin('user_key', 'user_key', 'user_key.id = gb.user_key_id')
          .leftJoin('user', 'user', 'user.id = user_key.user_id ')
          .leftJoin('platform', 'platform', 'platform.id = gb.platform_id')
          .leftJoin('pair', 'pair', 'pair.id = gb.base_pair_id ')
          .where(`gb.uuid = :uuid and gb.status = 'ACTIVE'`, {
            uuid: transaction.uuid,
          });

        bot = await queryBuilder
          .select([
            'gb.id as id',
            'gb.name as name',
            'gb.uuid as uuid',
            'gb.lower_threshold as lower_threshold',
            'gb.upper_threshold as upper_threshold',
            'gb.step as step',
            'gb.initial_fund as initial_fund',
            'gb.sell_at_stop as sell_at_stop',
            'gb.status as status',
          ]) // 'gb.fee_pct as fee_pct' is not used in future
          .addSelect(
            `JSON_OBJECT(
            'id', platform.id,
            'name', platform.name
          )`,
            'platform',
          )
          .addSelect(
            `JSON_OBJECT(
            'id', user_key.id,
            'api_key', user_key.api_key,
            'api_secret', user_key.api_secret,
            'fee', user_key.maker_fee
          )`, //taker fee is not used in gridbot
            'user_key',
          )
          .addSelect(
            `JSON_OBJECT(
            'id', pair.id,
            'symbol', pair.symbol,
            'min_notional', pair.min_notional,
            'price_dp', pair.price_dp,
            'qty_dp', pair.qty_dp,
            'base', pair.base,
            'quote', pair.quote,
            'tick', CAST(pair.tick AS CHAR),
            'step', CAST(pair.step AS CHAR)
          )`,
            'pair',
          )
          .getRawOne();

        // Calculate filled amount and fee for this round checking
        const filledAmount = Decimal(data.exec_qty).minus(
          transaction.exc_amount,
        );

        const fee = transaction.is_buy
          ? Decimal(bot.user_key.fee).mul(filledAmount).toString()
          : Decimal(bot.user_key.fee)
              .mul(filledAmount)
              .mul(Decimal(data.price))
              .toString();

        // BALANCE UPDATE: Handle fully filled, remove freeze first from payment side then add available to receive side
        //Let say XRPMYR buy order filled, remove the MYR(quote) freeze and add to XRP(base) available
        //Let say XRPMYR sell order filled, remove the XRP(base) freeze and add to MYR(quote) available

        //Balance Filled for BUY order
        if (transaction.is_buy) {
          const actualAmount = new Decimal(transaction.amount) // Use amount - previous amount to make sure the amount is net
            .minus(transaction.prv_amount)
            .minus(transaction.exc_amount);
          const currencyAmount = truncateToDecimals(
            actualAmount.mul(transaction.price),
            bot.pair.price_dp,
          ).toString();

          // Order fill (buy)
          await this.balanceUpdateQueue.add('processing', {
            user_key_id: bot.user_key.id,
            symbol: bot.pair.quote, //Remove quote freeze (MYR)
            available_balance_delta: '0',
            freeze_balance_delta: new Decimal(currencyAmount)
              .negated()
              .toString(), // Buy order should use currency like MYR, so get the net amount(qty) and * the price to get how many currency need
            fee: transaction.fee,
            price_dp: bot.pair.price_dp,
          });

          await this.balanceUpdateQueue.add('processing', {
            user_key_id: bot.user_key.id,
            symbol: bot.pair.base, //Add base available (XRP)
            available_balance_delta: actualAmount.toString(), // Buy order should use currency like MYR, so get the net amount(qty) and * the price to get how many currency need
            freeze_balance_delta: '0',
            fee: transaction.fee,
            price_dp: bot.pair.price_dp,
          });
        } else {
          const baseAmount = new Decimal(transaction.amount)
            .minus(transaction.prv_amount)
            .minus(transaction.exc_amount);

          const currencyAmount = truncateToDecimals(
            baseAmount.mul(transaction.price),
            bot.pair.price_dp,
          ).toString();

          await this.balanceUpdateQueue.add('processing', {
            user_key_id: bot.user_key.id,
            symbol: bot.pair.base, //Remove base freeze (XRP)
            available_balance_delta: '0',
            freeze_balance_delta: new Decimal(baseAmount).negated().toString(),
            fee: transaction.fee,
            price_dp: bot.pair.price_dp,
          });

          await this.balanceUpdateQueue.add('processing', {
            user_key_id: bot.user_key.id,
            symbol: bot.pair.quote, //Add quote freeze (MYR)
            available_balance_delta: currencyAmount.toString(),
            freeze_balance_delta: '0',
            fee: transaction.fee,
            price_dp: bot.pair.price_dp,
          });
        }

        if (
          Decimal(data.exec_qty)
            .add(transaction.prv_amount)
            .eq(transaction.amount)
        ) {
          // Order is fully filled
          transaction.status = TransactionStatus.COMPLETED;
          //place reverse order here

          console.log('in trade', bot);
          const numberOfSteps = Decimal(bot.upper_threshold)
            .minus(bot.lower_threshold)
            .dividedBy(bot.step);

          let is_complete_checking = transaction.matched_order.split('-');

          console.log('is_complete_checking : ', is_complete_checking);
          console.log(
            transaction.matched_order.split('-'),
            "transaction.matched_order.split('-')",
          );

          let order_object = {
            platform: bot.platform,
            user_key: bot.user_key,
            order: {
              symbol: bot.pair.symbol,
              side: transaction.is_buy ? 'SELL' : 'BUY', //when sell order completed place buy and vice versa
              type: 'LIMIT',
              quantity: transaction.amount,
              price: transaction.is_buy
                ? Decimal(transaction.price).add(
                    numberOfSteps.toFixed(bot.pair.price_dp),
                  )
                : Decimal(transaction.price).minus(
                    numberOfSteps.toFixed(bot.pair.price_dp),
                  ), // where sell order completed , price down then buy again and vice versa
            },
            uuid: bot.uuid,
            fee_pct: bot.user_key.fee, // use maker_fee to replace fee_pct
            amount: transaction.amount,
            bot_type: transaction.bot_type,
            is_buy: transaction.is_buy ? false : true,
            pair: bot.pair,
          };

          if (!is_complete_checking[0] || !is_complete_checking[1]) {
            console.log('in complete trade, should update and use new record');
            let match_order = 'trade' + Date.now();
            transaction.matched_order = transaction.is_buy
              ? transaction.matched_order + match_order
              : match_order + transaction.matched_order;
            console.log(transaction.matched_order);
            order_object['matched_order'] = transaction.matched_order;
          }

          await this.realizeCheckingQueue.add(
            'processing',
            {
              uuid: transaction.uuid,
            },
            { delay: 10000 },
          );

          let gridbot = await this.em
            .createQueryBuilder(Gridbot, 'gb')
            .where('gb.uuid = :uuid', {
              uuid: transaction.uuid,
            })
            .getOne();

          // Update gridbot current balances
          gridbot.current_base = transaction.is_buy
            ? Decimal(gridbot.current_base)
                .add(data.exec_qty)
                .minus(fee)
                .toString()
            : Decimal(gridbot.current_base).minus(data.exec_qty).toString();
          gridbot.current_quote = transaction.is_buy
            ? Decimal(gridbot.current_quote)
                .minus(Decimal(data.exec_qty).mul(data.price))
                .toString()
            : Decimal(gridbot.current_quote)
                .add(Decimal(data.exec_qty).mul(data.price))
                .minus(fee)
                .toString();
          let new_transaction = await this.createTransaction(order_object);
          order_object['id'] = new_transaction.id;
          await Promise.all([
            this.em.save(gridbot),
            this.placeOrderQueue.add('processing', order_object),
            this.createEventQueue.add(
              'processing',
              {
                botType: 'gridbot',
                uuid: bot.uuid,
                eventType:
                  CreateBotEventQueueService.EVENT_TYPES.ORDER_EXECUTED,
                params: [
                  transaction.is_buy ? 'BUY' : 'SELL',
                  transaction.price,
                  bot.pair.quote,
                  transaction.amount,
                  bot.pair.base,
                ],
              },
              { removeOnComplete: true },
            ),
          ]);
        } else if (
          Decimal(data.exec_qty)
            .add(transaction.prv_amount)
            .greaterThan(transaction.amount)
        ) {
          return;
        }

        console.log(bot);
        let match_id = transaction.matched_order.split('-');

        // Create trade record
        let trade = new Trade();
        trade.amount = filledAmount.toString();
        trade.fee = fee;
        trade.fee_currency = transaction.is_buy
          ? bot.pair.base
          : bot.pair.quote;
        trade.is_buy = transaction.is_buy;
        trade.match_id = transaction.is_buy ? match_id[0] : match_id[1];
        trade.pair_id = transaction.target_pair_id;
        trade.platform_id = transaction.platform_id;
        trade.price = data.price;
        trade.quote_amount = Decimal(data.price).mul(trade.amount).toString();
        // trade.trade_id =
        trade.trade_time = new Date(data.time);
        trade.uuid = transaction.uuid;
        trade.bot_type = transaction.bot_type;
        await this.em.save(Trade, trade);

        // Update transaction executed amount
        transaction.exc_amount = Decimal(data.exec_qty).toString();
        await this.em.save(Transaction, transaction);
      }
    } catch (error) {
      console.log(error);
      switch (transaction.bot_type) {
        case 'GRIDBOT': {
          await this.em
            .createQueryBuilder()
            .update(Gridbot)
            .set({ status: GridBotStatus.ERROR })
            .where('uuid = :uuid', { uuid: transaction.uuid })
            .execute();
          break;
        }
        case 'ARBITRAGEBOT': {
          await this.em
            .createQueryBuilder()
            .update(ArbitrageBot)
            .set({ status: ArbitBotStatus.ERROR })
            .where('uuid = :uuid', { uuid: transaction.uuid })
            .execute();
          break;
        }
        case 'PRICEBOT':
          {
            break;
          }
          console.log('Error while updating order: ', error.message);
          console.log(error);
      }
      // get uuid
      //check balance
      //market buy
      //place order
      //record order
    }
  }

  async createTransaction(data: any) {
    let order = data.order;
    let user_key = data.user_key;
    let record = new Transaction();
    if (data.matched_order) {
      record.matched_order = data.matched_order;
    }
    record.uuid = data.uuid;
    record.fee = new Decimal(order.price)
      .mul(new Decimal(data.amount))
      .mul(new Decimal(data.fee_pct))
      .toString();
    record.amount = data.amount;
    record.bot_type = data.bot_type;
    record.is_buy = data.is_buy;
    record.platform_id = data.platform.id;
    record.price = order.price.toString();
    record.prv_amount = data.prv_amount ? data.prv_amount : '0';
    record.target_pair_id = data.pair.id;
    record.user_key_id = user_key.id;
    record.status = TransactionStatus.NEW;

    return await this.em.save(Transaction, record);
  }


  @Process('luno-order-update')
  async handleLunoOrderUpdate(job: Job) {
      const data = job.data;
      console.log(`[LUNO] Processing Luno order update:`, data);

      try {
          if (data.type === 'trade') {
              // Directly process trade updates
              return this.readOperationJob3({
                  data: {
                      order_id: data.order_id,
                      type: 'trade',
                      exec_qty: data.exec_qty,
                      price: data.price,
                      time: data.time
                  },
                  id: job.id
              } as Job);
          }
          else if (data.type === 'cancel') {
              const lunoData = data.lunoOrderData;
              
              // Check transaction status first
              const transaction = await this.em
                  .createQueryBuilder(Transaction, 't')
                  .where("t.order_id = :order_id", { order_id: data.order_id })
                  .getOne();

              if (!transaction) {
                  console.log(`[LUNO] Transaction not found for order ${data.order_id}`);
                  return;
              }

              // If already completed, skip processing
              if (transaction.status === TransactionStatus.CANCELLED) {
                  console.log(`[LUNO] Order ${data.order_id} already cancelled`);
                  return;
              }

              // If partially filled, process as trade first
              // if (new Decimal(transaction.exc_amount).greaterThan(0)) {
              //     console.log(`[LUNO] Processing partial fill before cancellation`);
              //     await this.readOperationJob3({
              //         data: {
              //             order_id: data.order_id,
              //             type: 'trade',
              //             exec_qty: transaction.exc_amount,
              //             price: transaction.price,
              //             time: Date.now()
              //         },
              //         id: job.id
              //     } as Job);
              // }

              // Process cancellation
              return this.readOperationJob3({
                  data: {
                      order_id: data.order_id,
                      type: 'cancel'
                  },
                  id: job.id
              } as Job);
          }
      } catch (error) {
          console.error(`[LUNO] Error processing Luno order update:`, error);
          throw error;
      }
  }

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} updateQueue of type ${job.name} with data ${job.data}...`,
    );
  }
}
