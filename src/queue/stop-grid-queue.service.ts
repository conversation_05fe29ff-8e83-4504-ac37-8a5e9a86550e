import { InjectQueue, OnQueueActive, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { Gridbot, GridBotStatus } from 'src/gridbot/entities/gridbot.entity';
import { Transaction } from 'src/transactions/entities/transaction.entity';
import { <PERSON><PERSON><PERSON><PERSON>ana<PERSON>, In, Not } from 'typeorm';
import { CreateBotEventQueueService } from './create-bot-event-queue.service';
import { Pair } from 'src/pair/entities/pair.entity';

@Processor('stop-grid')
export class StopGridQueueService {
  constructor(
    private em: EntityManager,
    @InjectQueue('cancel-order') private cancelOrderQueue: Queue,
    @InjectQueue('create-event') private createEventQueue: Queue,
  ) {}

  @Process('end-grid')
  async readOperationJob3(job: Job) {
    let data = job.data;
    let orders = await this.em
      .createQueryBuilder(Transaction, 't')
      .where("t.uuid = :uuid and t.status not in ('COMPLETED','CANCELLED')", {
        uuid: data.uuid,
      })
      .getMany();
    let index = 1;
    for (const order of orders) {
      let pair = await this.em
        .createQueryBuilder(Pair, 'p')
        .where('p.id = :id', { id: order.target_pair_id })
        .getOne();
      await this.cancelOrderQueue.add('processing', {
        order,
        platform: data.platform,
        user_key: data.user_key,
        uuid: data.uuid,
        pair,
      });
      await this.createEventQueue.add(
        'processing',
        {
          botType: 'gridbot',
          uuid: data.uuid,
          eventType: CreateBotEventQueueService.EVENT_TYPES.CANCEL_ORDER,
          params: [index++, orders.length],
        },
        { removeOnComplete: true },
      );
    }
    await this.em
      .createQueryBuilder()
      .update(Gridbot)
      .set({
        status: GridBotStatus.DELETED,
        deleted_at: () => 'CURRENT_TIMESTAMP',
      })
      .where('uuid = :uuid', { uuid: data.uuid })
      .execute();
    // let nft_address = data.nft_address;

    await this.createEventQueue.add(
      'processing',
      {
        botType: 'gridbot',
        uuid: data.uuid,
        eventType: CreateBotEventQueueService.EVENT_TYPES.BOT_SHUTDOWN,
        params: [],
      },
      { removeOnComplete: true },
    );
  }

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} stop grid of type ${job.name} with data ${job.data}...`,
    );
  }
}
