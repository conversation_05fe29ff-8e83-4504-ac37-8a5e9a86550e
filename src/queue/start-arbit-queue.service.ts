import { InjectQueue, OnQueueActive, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { addSocketConnection } from 'common/util/utils';
import {
  ArbitBotStatus,
  ArbitrageBot,
} from 'src/arbitrage_bot/entities/arbitrage_bot.entity';
import { EntityManager } from 'typeorm';
import { HataPlatformHelper } from 'common/helper/hata-platform.helper';
import Redis from 'ioredis';
import configuration from 'config/configuration';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import { CreateBotEventQueueService } from './create-bot-event-queue.service';
import { PinoLogger } from 'nestjs-pino';

@Processor('start-arbit')
export class StartArbitQueueService {
  private redisClient: Redis;

  constructor(
    private em: EntityManager,
    // @InjectQueue('place-order') private placeOrderQueue: Queue, //No placingorder in init-arbit
    @InjectQueue('create-event') private createEventQueue: Queue,
    private readonly logger: PinoLogger,
  ) {
    this.redisClient = new Redis({
      host: configuration().redis.host,
      password: configuration().redis.password,
    });
  }

  @Process('init-arbit')
  async readOperationJob3(job: Job) {
    this.logger.info({ jobData: job.data }, 'Start processing init-arbit job');
    console.log('Start processing init-arbit job', job.data);
    try {
      const { base_platform, base_pair, target_platform, target_pair, uuid } = job.data;
      this.logger.info('Updating redis data for base and target pairs');
      console.log('Updating redis data for base and target pairs');
      await Promise.all([
        this.updateRedisData(base_pair, base_platform),
        this.updateRedisData(target_pair, target_platform),
      ]);
      this.logger.info('Setting ArbitrageBot status to ACTIVE in DB', { uuid });
      console.log('Setting ArbitrageBot status to ACTIVE in DB', uuid);
      await this.em
        .createQueryBuilder()
        .update(ArbitrageBot)
        .set({ status: ArbitBotStatus.ACTIVE })
        .where('uuid = :uuid', { uuid })
        .execute();
      this.logger.info('Added socket connections for base and target pairs');
      console.log('Added socket connections for base and target pairs');
      await addSocketConnection(base_pair.id, base_platform.id);
      await addSocketConnection(target_pair.id, target_platform.id);
      this.logger.info('Adding processing event to createEventQueue', { uuid });
      console.log('Adding processing event to createEventQueue', uuid);
      await this.createEventQueue.add(
        'processing',
        {
          botType: 'arbitragebot',
          uuid,
          eventType: CreateBotEventQueueService.EVENT_TYPES.BOT_STARTED,
          params: [],
        },
        { removeOnComplete: true },
      );
      this.logger.info('Finished processing init-arbit job', { uuid });
      console.log('Finished processing init-arbit job', uuid);
    } catch (error) {
      this.logger.error({ error, jobData: job.data }, 'Error in readOperationJob3');
      console.error('Error in readOperationJob3', error);
      throw error;
    }
  }

  async updateRedisData(pair: any, platform: any) {
    this.logger.info('Updating redis data for pair', { pair, platform });
    console.log('Updating redis data for pair', pair, platform);
    try {
      let platformHelper = callPlatformHelper(platform.name.toLowerCase());
      await platformHelper.init(platform.name.toLowerCase(), '', '');
      let orderbook = await platformHelper.getOrderBook(pair.symbol);
      let lask = {
        price: orderbook.asks.length != 0 ? orderbook.asks[0].price : null,
        qty: orderbook.asks.length != 0 ? orderbook.asks[0].qty : null,
      };
      let lbid = {
        price: orderbook.bids.length != 0 ? orderbook.bids[0].price : null,
        qty: orderbook.bids.length != 0 ? orderbook.bids[0].qty : null,
      };
      await this.redisClient.set(
        pair.id + ':ask',
        JSON.stringify({
          ...lask,
          last_updated: Date.now(),
          last_interaction: Date.now(),
        }),
      );
      await this.redisClient.set(
        pair.id + ':bid',
        JSON.stringify({
          ...lbid,
          last_updated: Date.now(),
          last_interaction: Date.now(),
        }),
      );
      this.logger.info('Redis data updated for pair', { pairId: pair.id });
      console.log('Redis data updated for pair', pair.id);
    } catch (error) {
      this.logger.error({ error, pair, platform }, 'Error in updateRedisData');
      console.error('Error in updateRedisData', error);
      throw error;
    }
  }

  @OnQueueActive()
  onActive(job: Job) {
    this.logger.info({ jobId: job.id, jobName: job.name, jobData: job.data }, 'Job is now active');
    console.log(
      `Processing job ${job.id} start arbit of type ${job.name} with data`,
      job.data,
    );
  }
}
