import {
  InjectQueue,
  OnQueueActive,
  OnQueueError,
  OnQueueFailed,
  Process,
  Processor,
} from '@nestjs/bull';
import { BadRequestException } from '@nestjs/common';
import { PinoLogger } from 'nestjs-pino';
import { Job, Queue } from 'bull';
import configuration from 'config/configuration';
import Redis from 'ioredis';

import {
  ArbitBotStatus,
  ArbitrageBot,
} from 'src/arbitrage_bot/entities/arbitrage_bot.entity';
import { Gridbot, GridBotStatus } from 'src/gridbot/entities/gridbot.entity';
import { SocketGateway } from 'src/socket/socket.gateway';
import { EntityManager, In, Not } from 'typeorm';

@Processor('socket-data')
export class SocketDataQueueService {
  private redisClient: Redis;

  // Handle the order book message from luno first message
  private orderBooks: { [pairId: string]: { bids: any[], asks: any[], sequence: string } } = {};

  constructor(
    @InjectQueue('grid-checking') private gridCheckingQueue: Queue,
    private gateWay: SocketGateway,
    private readonly logger: PinoLogger,
  ) {
    this.redisClient = new Redis({
      host: configuration().redis.host,
      password: configuration().redis.password,
    });
  }

  @Process({ concurrency: 5, name: 'binance' })
  async readOperationJob3(job: Job) {
    let data = job.data;
    this.handleBinanceMessage(data.pair, data.message);
  }

  @Process({ concurrency: 1, name: 'hata' })
  async readOperationJob4(job: Job) {
    let data = job.data;
    this.handleHataMessage(data.pair, data.message);
  }

  @Process({ concurrency: 5, name: 'luno' })
  async handleLunoStream(job: Job) {
    const { pair, message } = job.data;
    this.handleLunoMessage(pair, message);
  }

  private async handleLunoMessage(pair: any, message: any) {
    const pairId = pair.id;
    
    try {
      switch (message.event_name) {
        case 'orderBookSnapshot':
          await this.handleOrderBookSnapshot(pair, message.data);
          break;
          
        case 'orderBookTop':
          await this.handleOrderBookTop(pair, message.data);
          break;
          
        // case 'createUpdate':
        //   await this.handleCreateUpdate(pair, message.data);
        //   break;
          
        // case 'deleteUpdate':
        //   await this.handleDeleteUpdate(pair, message.data);
        //   break;
          
        // case 'tradeUpdate':
        //   await this.handleTradeUpdate(pair, message.data);
        //   break;
          
        // case 'statusUpdate':
        //   await this.handleStatusUpdate(pair, message.data);
        //   break;
          
        default:
          console.log(`[LUNO] Unknown message type: ${message.event_name}`, message);
          this.logger.warn(`[LUNO] Unknown message type: ${message.event_name}`, message);
      }
    } catch (error) {
      this.logger.error(`Error handling Luno message for pair ${pairId}: ${error.message}`, error);
    }
  }

  private async handleBinanceMessage(pair: any, message: any) {
    // Handle incoming messages from the WebSocket
    // Route message to appropriate queue based on event_name
    let data = message;
    let lbid = {};
    let lask = {};
    let bid = {};
    let ask = {};
    lask[pair.id] = { price: data.a, qty: data.A };
    lbid[pair.id] = { price: data.b, qty: data.B };
    if (!lask[pair.id].price || !lbid[pair.id].price) {
      return;
    }
    ask[pair.id] = JSON.parse(await this.redisClient.get(pair.id + ':ask'));

    if (
      !ask[pair.id] ||
      (lask[pair.id] && Date.now() - ask[pair.id].last_interaction > 1000)
    ) {
      await this.redisClient.set(
        pair.id + ':ask',
        JSON.stringify({
          ...lask[pair.id],
          last_updated: Date.now(),
          last_interaction: Date.now(),
        }),
      );
      this.gateWay.server.emit(pair.id + ':ask', lask[pair.id]);
      // await this.gridCheckingQueue.add('check-criterea', {
      //   id: pair.id,
      //   price: lask[pair.id].price,
      //   side: 'sell',
      // });
    }
    bid[pair.id] = JSON.parse(await this.redisClient.get(pair.id + ':bid'));
    if (
      !bid[pair.id] ||
      (lbid[pair.id] && Date.now() - bid[pair.id].last_interaction > 1000)
    ) {
      await this.redisClient.set(
        pair.id + ':bid',
        JSON.stringify({
          ...lbid[pair.id],
          last_updated: Date.now(),
          last_interaction: Date.now(),
        }),
      );
      this.gateWay.server.emit(pair.id + ':bid', lbid[pair.id]);
      // await this.gridCheckingQueue.add('check-criterea', {
      //   id: pair.id,
      //   price: lbid[pair.id].price,
      //   side: 'buy',
      // });
    }
    // }
  }

  private async handleHataMessage(pair: any, message: any) {
    let data = message.data;
    // Handle incoming messages from the WebSocket
    // Route message to appropriate queue based on event_name
    if (message.event_name.includes('depth')) {
      let lbid = {};
      let lask = {};
      let bid = {};
      let ask = {};
      lask[pair.id] = data.asks;
      lbid[pair.id] = data.bids;
      bid[pair.id] = JSON.parse(await this.redisClient.get(pair.id + ':bid'));
      ask[pair.id] = JSON.parse(await this.redisClient.get(pair.id + ':ask'));
      lbid[pair.id] = data.bids[0];
      if (
        !bid[pair.id] ||
        (lbid[pair.id] && Date.now() - bid[pair.id].last_interaction > 1000)
      ) {
        await this.redisClient.set(
          pair.id + ':bid',
          JSON.stringify({
            ...lbid[pair.id],
            last_updated: Date.now(),
            last_interaction: Date.now(),
          }),
        );
        this.gateWay.server.emit(pair.id + ':bid', lbid[pair.id]);
        // await this.gridCheckingQueue.add('check-criterea', {
        //   id: pair.id,
        //   price: lbid[pair.id].price,
        //   side: 'buy',
        // });
      }
      lask[pair.id] = data.asks[0];
      if (
        !ask[pair.id] ||
        (lask[pair.id] && Date.now() - ask[pair.id].last_interaction > 1000)
      ) {
        await this.redisClient.set(
          pair.id + ':ask',
          JSON.stringify({
            ...lask[pair.id],
            last_updated: Date.now(),
            last_interaction: Date.now(),
          }),
        );
        this.gateWay.server.emit(pair.id + ':ask', lask[pair.id]);
        // await this.gridCheckingQueue.add('check-criterea', {
        //   id: pair.id,
        //   price: lask[pair.id].price,
        //   side: 'sell',
        // });
      }
    }
  }

  private async handleOrderBookSnapshot(pair: any, data: any) {
    const pairId = pair.id;
    
    // Initialize or update the order book
    this.orderBooks[pairId] = {
      bids: data.bids || [],
      asks: data.asks || [],
      sequence: data.sequence
    };
    
    console.log(`[LUNO] Order book snapshot for pair ${pairId}, sequence: ${data.sequence}`);
    
    // Emit best bid and ask if available
    await this.emitBestPrices(pair);
  }

  /**
   * Handle orderBookTop messages from REST API calls
   * This replaces the WebSocket streaming approach
   */
  private async handleOrderBookTop(pair: any, data: any) {
    const pairId = pair.id;
    
    try {
      // console.log(`[LUNO] Processing orderBookTop for pair ${pairId} (${pair.symbol})`);
      this.logger.info(`[LUNO] Processing orderBookTop for pair ${pairId} (${pair.symbol})`, {
        hasAsks: data.asks && data.asks.length > 0,
        hasBids: data.bids && data.bids.length > 0,
        source: data.source || 'unknown'
      });
      
      // Process best ask (lowest sell price)
      if (data.asks && data.asks.length > 0) {
        const bestAsk = data.asks[0]; // First ask is the best (lowest price)
        await this.updateRedisAndEmit(pairId, 'ask', {
          price: bestAsk.price,
          qty: bestAsk.volume
        });
        
        // console.log(`[LUNO] Updated best ask for pair ${pairId}: ${bestAsk.price} @ ${bestAsk.volume}`);
      }
      
      // Process best bid (highest buy price)
      if (data.bids && data.bids.length > 0) {
        const bestBid = data.bids[0]; // First bid is the best (highest price)
        await this.updateRedisAndEmit(pairId, 'bid', {
          price: bestBid.price,
          qty: bestBid.volume
        });
        
        // console.log(`[LUNO] Updated best bid for pair ${pairId}: ${bestBid.price} @ ${bestBid.volume}`);
      }
      
      // Optional: Trigger grid checking if needed
      // if (data.asks && data.asks.length > 0) {
      //   await this.gridCheckingQueue.add('check-criterea', {
      //     id: pairId,
      //     price: data.asks[0].price,
      //     side: 'sell',
      //   });
      // }
      // 
      // if (data.bids && data.bids.length > 0) {
      //   await this.gridCheckingQueue.add('check-criterea', {
      //     id: pairId,
      //     price: data.bids[0].price,
      //     side: 'buy',
      //   });
      // }
      
    } catch (error) {
      this.logger.error(`Error processing orderBookTop for pair ${pairId}: ${error.message}`, error);
      console.error(`[LUNO] Error processing orderBookTop for pair ${pairId}:`, error);
    }
  }

  /**
   * Helper method to update Redis and emit socket events
   * Follows the same pattern as Binance and Hata handlers
   */
  private async updateRedisAndEmit(pairId: string, side: 'bid' | 'ask', priceData: { price: string, qty: string }) {
    try {
      const redisKey = `${pairId}:${side}`;
      const existingData = await this.redisClient.get(redisKey);
      let shouldUpdate = true;
      
      if (existingData) {
        const parsed = JSON.parse(existingData);
        // Only update if more than 1 second has passed (following existing pattern)
        shouldUpdate = Date.now() - parsed.last_interaction > 1000;
      }
      
      if (shouldUpdate) {
        const dataToStore = {
          price: priceData.price,
          qty: priceData.qty,
          last_updated: Date.now(),
          last_interaction: Date.now(),
        };
        
        await this.redisClient.set(redisKey, JSON.stringify(dataToStore));
        
        // Emit to socket clients
        this.gateWay.server.emit(redisKey, {
          price: priceData.price,
          qty: priceData.qty
        });
        
        this.logger.debug(`[LUNO] Updated Redis and emitted ${side} for pair ${pairId}: ${priceData.price} @ ${priceData.qty}`);
      }
    } catch (error) {
      this.logger.error(`Error updating Redis and emitting for ${pairId}:${side}`, error);
    }
  }

  // private async handleCreateUpdate(pair: any, data: any) {
  //   const pairId = pair.id;
  //   const { create_update, sequence } = data;
    
  //   if (!this.orderBooks[pairId]) {
  //     this.logger.warn(`[LUNO] No order book for pair ${pairId}, skipping create update`);
  //     return;
  //   }
    
  //   // Check sequence
  //   if (parseInt(sequence) !== parseInt(this.orderBooks[pairId].sequence) + 1) {
  //     this.logger.error(`[LUNO] Sequence mismatch for pair ${pairId}. Expected: ${parseInt(this.orderBooks[pairId].sequence) + 1}, Got: ${sequence}`);
  //     // You might want to reconnect here
  //     return;
  //   }
    
  //   this.orderBooks[pairId].sequence = sequence;
    
  //   // Add the new order to the appropriate side
  //   const order = {
  //     id: create_update.order_id,
  //     price: create_update.price,
  //     volume: create_update.volume
  //   };
    
  //   if (create_update.type === 'BID') {
  //     this.orderBooks[pairId].bids.push(order);
  //     // Sort bids by price (highest first)
  //     this.orderBooks[pairId].bids.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
  //   } else if (create_update.type === 'ASK') {
  //     this.orderBooks[pairId].asks.push(order);
  //     // Sort asks by price (lowest first)
  //     this.orderBooks[pairId].asks.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
  //   }
    
  //   console.log(`[LUNO] Created ${create_update.type} order ${create_update.order_id} for pair ${pairId}`);
    
  //   // Emit updated best prices
  //   await this.emitBestPrices(pair);
  // }

  // private async handleDeleteUpdate(pair: any, data: any) {
  //   const pairId = pair.id;
  //   const { delete_update, sequence } = data;
    
  //   if (!this.orderBooks[pairId]) {
  //     this.logger.warn(`[LUNO] No order book for pair ${pairId}, skipping delete update`);
  //     return;
  //   }
    
  //   // Check sequence
  //   if (parseInt(sequence) !== parseInt(this.orderBooks[pairId].sequence) + 1) {
  //     this.logger.error(`[LUNO] Sequence mismatch for pair ${pairId}. Expected: ${parseInt(this.orderBooks[pairId].sequence) + 1}, Got: ${sequence}`);
  //     return;
  //   }
    
  //   this.orderBooks[pairId].sequence = sequence;
    
  //   // Remove the order from both sides
  //   this.orderBooks[pairId].bids = this.orderBooks[pairId].bids.filter(
  //     order => order.id !== delete_update.order_id
  //   );
  //   this.orderBooks[pairId].asks = this.orderBooks[pairId].asks.filter(
  //     order => order.id !== delete_update.order_id
  //   );
    
  //   console.log(`[LUNO] Deleted order ${delete_update.order_id} for pair ${pairId}`);
    
  //   // Emit updated best prices
  //   await this.emitBestPrices(pair);
  // }

  // private async handleTradeUpdate(pair: any, data: any) {
  //   const pairId = pair.id;
  //   const { trade, sequence } = data;
    
  //   if (!this.orderBooks[pairId]) {
  //     this.logger.warn(`[LUNO] No order book for pair ${pairId}, skipping trade update`);
  //     return;
  //   }
    
  //   // Check sequence
  //   if (parseInt(sequence) !== parseInt(this.orderBooks[pairId].sequence) + 1) {
  //     this.logger.error(`[LUNO] Sequence mismatch for pair ${pairId}. Expected: ${parseInt(this.orderBooks[pairId].sequence) + 1}, Got: ${sequence}`);
  //     return;
  //   }
    
  //   this.orderBooks[pairId].sequence = sequence;
    
  //   // Find and update the maker order volume
  //   const makerOrderId = trade.maker_order_id;
  //   let orderFound = false;
    
  //   // Check bids
  //   for (let i = 0; i < this.orderBooks[pairId].bids.length; i++) {
  //     if (this.orderBooks[pairId].bids[i].id === makerOrderId) {
  //       const currentVolume = parseFloat(this.orderBooks[pairId].bids[i].volume);
  //       const tradeVolume = parseFloat(trade.base);
  //       const newVolume = currentVolume - tradeVolume;
        
  //       if (newVolume <= 0) {
  //         // Remove the order if fully filled
  //         this.orderBooks[pairId].bids.splice(i, 1);
  //       } else {
  //         // Update the volume
  //         this.orderBooks[pairId].bids[i].volume = newVolume.toString();
  //       }
  //       orderFound = true;
  //       break;
  //     }
  //   }
    
  //   // Check asks if not found in bids
  //   if (!orderFound) {
  //     for (let i = 0; i < this.orderBooks[pairId].asks.length; i++) {
  //       if (this.orderBooks[pairId].asks[i].id === makerOrderId) {
  //         const currentVolume = parseFloat(this.orderBooks[pairId].asks[i].volume);
  //         const tradeVolume = parseFloat(trade.base);
  //         const newVolume = currentVolume - tradeVolume;
          
  //         if (newVolume <= 0) {
  //           // Remove the order if fully filled
  //           this.orderBooks[pairId].asks.splice(i, 1);
  //         } else {
  //           // Update the volume
  //           this.orderBooks[pairId].asks[i].volume = newVolume.toString();
  //         }
  //         orderFound = true;
  //         break;
  //       }
  //     }
  //   }
    
  //   console.log(`[LUNO] Trade executed for pair ${pairId}: ${trade.base} @ ${parseFloat(trade.counter) / parseFloat(trade.base)}`);
    
  //   // Emit updated best prices
  //   await this.emitBestPrices(pair);
  // }

  // private async handleStatusUpdate(pair: any, data: any) {
  //   const pairId = pair.id;
  //   console.log(`[LUNO] Status update for pair ${pairId}: ${data.status_update.status}`);
  //   // You can emit this status update to clients if needed
  // }

  private async emitBestPrices(pair: any) {
    const pairId = pair.id;
    const orderBook = this.orderBooks[pairId];
    
    if (!orderBook) return;
    
    // Get best bid (highest price)
    if (orderBook.bids.length > 0) {
      const bestBid = orderBook.bids[0];
      const redisBid = JSON.parse(await this.redisClient.get(pairId + ':bid')) || {};
      
      if (!redisBid || Date.now() - redisBid.last_interaction > 1000) {
        const bidData = {
          price: bestBid.price,
          qty: bestBid.volume,
          last_updated: Date.now(),
          last_interaction: Date.now(),
        };
        
        await this.redisClient.set(pairId + ':bid', JSON.stringify(bidData));
        this.gateWay.server.emit(pairId + ':bid', { price: bestBid.price, qty: bestBid.volume });
      }
    }
    
    // Get best ask (lowest price)
    if (orderBook.asks.length > 0) {
      const bestAsk = orderBook.asks[0];
      const redisAsk = JSON.parse(await this.redisClient.get(pairId + ':ask')) || {};
      
      if (!redisAsk || Date.now() - redisAsk.last_interaction > 1000) {
        const askData = {
          price: bestAsk.price,
          qty: bestAsk.volume,
          last_updated: Date.now(),
          last_interaction: Date.now(),
        };
        
        await this.redisClient.set(pairId + ':ask', JSON.stringify(askData));
        this.gateWay.server.emit(pairId + ':ask', { price: bestAsk.price, qty: bestAsk.volume });
      }
    }
  }

  @OnQueueError()
  onError(job: Job) {
    console.log(job);
    console.log(
      `Processing job ${job.id} error grid of type ${job.name} with data ${job.data}...`,
    );
  }

  @OnQueueFailed()
  onFail(job: Job) {
    console.log(job);
    console.log(
      `Processing job ${job.id} fail grid of type ${job.name} with data ${job.data}...`,
    );
  }
}