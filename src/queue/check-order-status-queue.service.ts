import { InjectQueue, OnQueueActive, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import { HataPlatformHelper } from 'common/helper/hata-platform.helper';
import {
  Transaction,
  TransactionStatus,
} from 'src/transactions/entities/transaction.entity';
import { EntityManager } from 'typeorm';
import moment from 'moment-timezone';

@Processor('check-order-status')
export class CheckOrderStatusQueueService {
  constructor(
    private em: EntityManager,
    @InjectQueue('update-order') private updateOrderQueue: Queue,
  ) {}

  @Process('processing')
  async checkMissedOrderUpdates(job: Job) {
    try {
      // console.log('Starting check for missed order updates...');

      // PART 1. Get all pending transactions
      // Extract trading pairs from job data (passed from cron job)
      const tradingPairsWithPrices = job.data?.tradingPairs || [];

      if (!tradingPairsWithPrices.length) {
        console.log('No trading pairs provided to check');
        return { message: 'No trading pairs provided', checkedCount: 0 };
      }

      // Extract symbols for logging and query
      const pairIds = tradingPairsWithPrices.map(tp => tp.pairId);
      const symbols = tradingPairsWithPrices.map(tp => tp.symbol);
      // console.log(`Checking orders for trading pairs: ${symbols.join(', ')}`);
      
      // Get pending transactions for specific trading pairs only
      const pendingTransactions = await this.em
        .createQueryBuilder(Transaction, 't')
        .leftJoin('platform', 'platform', 't.platform_id = platform.id')
        .leftJoin('user_key', 'user_key', 't.user_key_id = user_key.id')
        // .leftJoin('pair', 'p', 't.target_pair_id = p.id')
        .where(`t.status = 'NEW'`)
        .andWhere('t.target_pair_id IN (:...pairIds)', { pairIds }) // Use symbols from tradingPairs,limits the search range for better performance
        // .andWhere('t.created_at >= :cutoffTime', { cutoffTime })
        .select([
          't.id as id',
          't.uuid as uuid',
          't.order_id as order_id',
          't.platform_id as platform_id',
          't.bot_type as bot_type',
          't.status as status',
          't.amount as amount',
          't.exc_amount as exc_amount',
          't.prv_amount as prv_amount',
          't.price as price',
          't.is_buy as is_buy',
          't.created_at as created_at',
          't.target_pair_id as target_pair_id', // Add this to match with tradingPairs data
        ])
        .addSelect(
          `JSON_OBJECT(
            'id', platform.id,
            'name', platform.name
          )`,
          'platform',
        )
        .addSelect(
          `JSON_OBJECT(
            'id', user_key.id,
            'api_key', user_key.api_key,
            'api_secret', user_key.api_secret
          )`,
          'user_key',
        )
        .getRawMany();

      if (!pendingTransactions.length) {
        console.log('No pending transactions found to check');
        return { message: 'No pending transactions found', checkedCount: 0 };
      }

      //console.log(`Found ${pendingTransactions.length} pending transactions to check`);

      // PART 2. Filter transactions based on market price conditions
      const filteredTransactions = this.filterTransactionsByPriceCondition(
        pendingTransactions, 
        tradingPairsWithPrices
      );

      if (!filteredTransactions.length) {
        console.log('No transactions meet the price condition criteria');
        return { message: 'No transactions meet price conditions', checkedCount: 0 };
      }

      //console.log(`${filteredTransactions.length} transactions meet price conditions for checking`);

      let checkedCount = 0;
      const platformHelpers = new Map();

      // Group transactions by platform for efficient API calls
      const transactionsByPlatform = this.groupTransactionsByPlatform(filteredTransactions);

      for (const [platformName, transactions] of transactionsByPlatform) {
        try {
          // Initialize platform helper if not already done
          if (!platformHelpers.has(platformName)) {
            const helper = await this.initializePlatformHelper(
              platformName,
              transactions[0].user_key // Use first transaction's user_key for initialization
            );
            platformHelpers.set(platformName, helper);
          }

          const platformHelper = platformHelpers.get(platformName);
          
          // Check each transaction's order status
          for (const transaction of transactions) {
            try {
              const orderDetail = await this.getOrderDetail(
                platformHelper,
                transaction.order_id,
                platformName,
                transaction // Pass transaction for symbol access
              );

              if (orderDetail) {
                await this.processOrderDetail(transaction, orderDetail);
                checkedCount++;
              }
            } catch (error) {
              console.error(`Error checking order ${transaction.order_id}:`, error.message);
              // Continue with next transaction
            }
          }
        } catch (error) {
          console.error(`Error processing platform ${platformName}:`, error.message);
          // Continue with next platform
        }
      }

      console.log(`Successfully checked ${checkedCount} order statuses`);
      return {
        message: `Successfully checked ${checkedCount} order statuses`,
        checkedCount,
      };
    } catch (error) {
      console.error('Error in checkMissedOrderUpdates:', error);
      throw new Error(`Failed to check missed order updates: ${error.message}`);
    }
  }

  private filterTransactionsByPriceCondition(transactions: any[], tradingPairsWithPrices: any[]): any[] {
    // console.log('Starting price condition filtering...');
    // console.log(`Input: ${transactions.length} transactions to filter`);

    // Debug log the full transaction data
    // console.log('Transaction data:', JSON.stringify(transactions, null, 2));
    // console.log('Trading pairs data:', JSON.stringify(tradingPairsWithPrices, null, 2));

    // Create a map for quick price lookup by symbol
    const priceMap = new Map();
    const symbolMap = new Map();

    //console.log('\nBuilding price and symbol maps from trading pairs...');
    tradingPairsWithPrices.forEach(tp => {
        // Convert pairId to string when setting in map
        priceMap.set(tp.pairId.toString(), parseFloat(tp.currentPrice));
        symbolMap.set(tp.pairId.toString(), tp.symbol);
        //console.log(`Mapped pairId ${tp.pairId} to ${tp.symbol}: Current price = ${tp.currentPrice}`);
    });

    // Debug log the maps
    // console.log('\nPrice Map:', Object.fromEntries(priceMap));
    // console.log('Symbol Map:', Object.fromEntries(symbolMap));

    // filter() returns a NEW ARRAY containing only transactions that return true
    const filteredTransactions = transactions.filter(transaction => {
        //console.log('\nChecking transaction:', transaction.order_id);
        //console.log('Transaction target_pair_id:', transaction.target_pair_id);
        
        // Convert target_pair_id to string when getting from map
        const currentMarketPrice = priceMap.get(transaction.target_pair_id.toString());
        const orderPrice = parseFloat(transaction.price);

        //console.log(`Looking up price for target_pair_id: ${transaction.target_pair_id}`);
        //console.log(`- Market price found: ${currentMarketPrice}`);
        //console.log(`- Order price: ${orderPrice}`);
        //console.log(`- Order type: ${transaction.is_buy ? 'BUY' : 'SELL'}`);

        // Skip if no current market price available
        if (!currentMarketPrice || !orderPrice) {
            console.log(`❌ Skipping: Missing market price (${currentMarketPrice}) or order price (${orderPrice})`);
            return false;
        }

        // Add symbol to transaction for later API calls
        transaction.symbol = symbolMap.get(transaction.target_pair_id.toString());
        // console.log(`- Symbol mapped: ${transaction.symbol}`);

        // For buy orders: only check if current market price is lower than order price
        if (transaction.is_buy) {
            const shouldCheck = currentMarketPrice <= orderPrice;
            //console.log(`- Buy order condition: ${currentMarketPrice} <= ${orderPrice} : ${shouldCheck ? '✅' : '❌'}`);
            return shouldCheck;
        } else {
            const shouldCheck = currentMarketPrice >= orderPrice;
            //console.log(`- Sell order condition: ${currentMarketPrice} >= ${orderPrice} : ${shouldCheck ? '✅' : '❌'}`);
            return shouldCheck;
        }
    });

    //console.log(`\nFiltering complete: ${filteredTransactions.length} transactions meet price conditions`);
    // if (filteredTransactions.length > 0) {
    //     console.log('Filtered transactions:', JSON.stringify(filteredTransactions, null, 2));
    // }
    return filteredTransactions;
}

  private groupTransactionsByPlatform(transactions: any[]): Map<string, any[]> {
    const grouped = new Map();
    
    for (const transaction of transactions) {
      const platformName = transaction.platform.name.toLowerCase();
      if (!grouped.has(platformName)) {
        grouped.set(platformName, []);
      }
      grouped.get(platformName).push(transaction);
    }

    //Example input data
    // transactions = [
    // {
    //   id: 123,
    //   uuid: "gridbot-uuid-abc123",
    //   order_id: "BINANCE_ORDER_456789",
    //   platform_id: 2,
    //   bot_type: "GRIDBOT",
    //   status: "PENDING",
    //   amount: "0.5",
    //   exc_amount: "0.0",
    //   prv_amount: "0.0",
    //   price: "50000.00",
    //   is_buy: true,
    //   created_at: "2025-06-11T10:00:00.000Z",
    //   platform: {
    //     id: 2,
    //     name: "binance"
    //   },
    //   user_key: {
    //     id: 45,
    //     api_key: "user123_binance_key",
    //     api_secret: "user123_binance_secret"
    //   }
    // },

    //Example output
    // transactionsByPlatform = Map {
    //   "binance" => [
    //     {
    //       id: 123,
    //       order_id: "BINANCE_ORDER_456789",
    //       platform: { name: "binance" },
    //       user_key: { api_key: "user123_binance_key", ... },
    //       // ... other fields
    //     },
    //     {
    //       id: 125,
    //       order_id: "BINANCE_ORDER_111222",
    //       platform: { name: "binance" },
    //       user_key: { api_key: "user789_binance_key", ... },
    //       // ... other fields
    //     }
    //   ],
    //   "hata global" => [
    //     {
    //       id: 124,
    //       order_id: "HATA_ORDER_789012",
    //       platform: { name: "hata global" },
    //       user_key: { api_key: "user456_hata_key", ... },
    //       // ... other fields
    //     }
    //   ]
    // }
    
    return grouped;
  }

  private async initializePlatformHelper(platformName: string, userKey: any) {
    try {
      let helper;
      
      //All platform need to initialized with userkey since it is used to access the exchange API
      switch (platformName) {
        case 'binance':
          helper = callPlatformHelper(platformName);
          // Initialize with user credentials if needed
          // if (helper.init) {
          //   helper.init(userKey.api_key, userKey.api_secret);
          // }

          await helper.init(platformName, userKey.api_key, userKey.api_secret);
          break;
          
        case 'hata global':
          // helper = new HataPlatformHelper();
          // helper.init(platformName, userKey.api_key, userKey.api_secret);

          helper = callPlatformHelper(platformName);
          await helper.init(platformName, userKey.api_key, userKey.api_secret);
          break;
          
        case 'hata myr':
          // helper = new HataPlatformHelper();
          // helper.init(platformName, userKey.api_key, userKey.api_secret);
          helper = callPlatformHelper(platformName);
          await helper.init(platformName, userKey.api_key, userKey.api_secret);
          break;

        // case 'tokenize':
        //   helper = callPlatformHelper(platformName);
        //   await helper.init(platformName, userKey.api_key, userKey.api_secret);
        //   break;
          
        default:
          throw new Error(`Unsupported platform: ${platformName}`);
      }

      //Example input data
      // platformName = "binance"
      // userKey = {
      //   id: 45,
      //   api_key: "user123_binance_key",
      //   api_secret: "user123_binance_secret"
      // }
      // OR
      // platformName = "hata global"
      // userKey = {
      //   id: 67,
      //   api_key: "user456_hata_key",
      //   api_secret: "user456_hata_secret"
      // }

      //Example output
      // For Binance
      // binanceHelper = {
      //   init: function(apiKey, apiSecret) { ... },
      //   getOrderDetail: function(orderId) { ... },
      //   getAllTicker: function() { ... },
      //   // ... other methods
      // }

      // // For Hata Global
      // hataGlobalHelper = {
      //   init: function(platform, apiKey, apiSecret) { ... },
      //   getOrderDetail: function(orderId) { ... },
      //   getAllTicker: function() { ... },
      //   // ... other methods
      // }
      
      return helper;
    } catch (error) {
      console.error(`Error initializing platform helper for ${platformName}:`, error);
      throw error;
    }
  }

  private async getOrderDetail(platformHelper: any, orderId: string, platformName: string, transaction?: any) {
    try {
      const cleanOrderId = orderId.trim();
      //console.log(`\n📋 Getting order details for ${platformName}:`);
      //console.log(`- Order ID: ${cleanOrderId}`);
      //console.log(`- Symbol: ${transaction?.symbol || 'N/A'}`);

      let orderDetail;
      
      switch (platformName) {
        case 'binance':
          if (!transaction?.symbol) {
            throw new Error(`Symbol is required for Binance order detail lookup`);
          }
          orderDetail = await platformHelper.getOrderDetails(transaction.symbol, cleanOrderId);
          break;
          
        case 'hata global':
          orderDetail = await platformHelper.getOrderDetails(cleanOrderId, 'hata global');
          break;

        case 'hata myr':
          orderDetail = await platformHelper.getOrderDetails(cleanOrderId, 'hata myr');
          break;
          
        default:
          throw new Error(`Unsupported platform for order detail: ${platformName}`);
      }

      //console.log(`\n📦 Raw order detail response:`, JSON.stringify(orderDetail, null, 2));
      return orderDetail;
      
    } catch (error) {
      console.error(`❌ Error getting order detail for ${orderId} on ${platformName}:`, error);
      throw error;
    }
}

  private async processOrderDetail(transaction: any, orderDetail: any) {
  try {
    //console.log(`\n🔄 Processing order detail for transaction ${transaction.order_id}:`);
    // console.log('- Original transaction:', {
    //   status: transaction.status,
    //   amount: transaction.amount,
    //   exc_amount: transaction.exc_amount
    // });

    // Normalize the order detail
    //console.log('\n🔨 Normalizing order detail...');
    const normalizedOrderDetail = this.normalizeOrderDetail(orderDetail, transaction.platform.name);

    // Get update info
    const updateInfo = this.getUpdateInfo(transaction, normalizedOrderDetail);
    //console.log('- Update info:', updateInfo);

    if (updateInfo.shouldUpdate) {
      //console.log(`\n📤 Preparing to queue update with type: ${updateInfo.type}`);
      
      if (updateInfo.type === 'cancel') {
        //console.log('- Queueing cancel update');
        await this.updateOrderQueue.add('processing', {
          order_id: transaction.order_id,
          type: 'cancel',
        });

      } else if (updateInfo.type === 'trade') {
        // console.log('- Queueing trade update with details:', {
        //   executedQty: normalizedOrderDetail.executedQty,
        //   price: normalizedOrderDetail.price,
        //   time: normalizedOrderDetail.updateTime
        // });
        
        await this.updateOrderQueue.add('processing', {
          order_id: transaction.order_id,
          type: 'trade',
          exec_qty: normalizedOrderDetail.executedQty,
          price: normalizedOrderDetail.price,
          time: normalizedOrderDetail.updateTime,
        });
      }

      //console.log(`✅ Successfully queued update for order ${transaction.order_id}`);
    } 
    // else {
    //   console.log('⏭️ No update needed for this order');
    // }

  } catch (error) {
    console.error(`❌ Error processing order detail for ${transaction.order_id}:`, error);
    throw error;
  }
}
  private normalizeOrderDetail(orderDetail: any, platformName: string) {
    const platform = platformName.toLowerCase();
    //console.log('\n🔍 Normalizing order detail for platform:', platform);
    
    if (platform === 'binance') {
      // Binance available status
      // 'NEW',
      // 'PENDING_NEW',
      // 'PARTIALLY_FILLED',
      // 'FILLED',
      // 'CANCELED',
      // 'PENDING_CANCEL',
      // 'REJECTED',
      // 'EXPIRED',
      // 'EXPIRED_IN_MATCH',
      return {
        status: orderDetail.status,
        executedQty: orderDetail.executedQty,
        price: orderDetail.price,
        updateTime: orderDetail.updateTime, // Use updateTime
      };

    } else if (platform === 'hata global' || platform === 'hata myr') {
      // Hata available status
      // 'active',        
      // 'partially_filled', 
      // 'fulfilled',    
      // 'cancelled', 
      
      // Extract data from nested structure
      const orderData = orderDetail.data;
      //console.log('Raw Hata order data:', orderData);

      // Map Hata status to Binance-like format
      let normalizedStatus = orderData.status;
      switch (orderData.status) {
        case 'fulfilled':
          normalizedStatus = 'FILLED';
          break;
        case 'cancelled':
          normalizedStatus = 'CANCELED';
          break;
        case 'partially_filled':
          normalizedStatus = 'PARTIALLY_FILLED';
          break;
        case 'active':
          normalizedStatus = 'NEW';
          break;
      }
      
      const normalized = {
        status: normalizedStatus,
        executedQty: orderData.exec_qty,
        price: orderData.price,
        updateTime: orderData.time ? orderData.time * 1000 : new Date().getTime(),
      };

      //console.log('Normalized Hata order:', normalized);
      return normalized;
    }
    
    return orderDetail;
  }


  // private shouldUpdateOrder(transaction: any, orderDetail: any): boolean {
  //   // Check if the order detail indicates a status change that wasn't captured
    
  //   // If order is cancelled and our transaction is still pending
  //   if (
  //     (orderDetail.status === 'CANCELED') 
  //   ) {
  //     return true;
  //   }
    
  //   // If order is filled and our transaction doesn't reflect this
  //   // if (
  //   //   (orderDetail.status === 'FILLED' || orderDetail.status === 'COMPLETED') &&
  //   //   transaction.status !== 'COMPLETED'
  //   // ) {
  //   //   return true;
  //   // }
    
  //   // If order's executed amount match the original amount of the order, means this order already fully filled, then updated
  //   if (
  //     orderDetail.executedQty &&
  //     parseFloat(orderDetail.executedQty) == parseFloat(transaction.amount || '0')
  //   ) {
  //     return true;
  //   }

  //   //Example input
  //   // transaction = {
  //   //   status: "PENDING",
  //   //   exc_amount: "0.0",
  //   //   amount: "0.5"
  //   // }

  //   // orderDetail = {
  //   //   status: "FILLED",
  //   //   executedQty: "0.5"
  //   // }

    
  //   return false;
  // }

  // private determineUpdateType(orderDetail: any): string {
  //   if (orderDetail.status === 'CANCELED') {
  //     return 'cancel';
  //   }
    
  //   //no need check status, check the execqty with the execqty in the db the ncan return trade type ady
  //   if (
  //     // orderDetail.status === 'FILLED' ||
  //     // orderDetail.status === 'PARTIALLY_FILLED' ||
  //     // orderDetail.status === 'COMPLETED' ||
  //     (orderDetail.executedQty && parseFloat(orderDetail.executedQty) > 0)
  //   ) {
  //     return 'trade';
  //   }
    
  //   return 'unknown';
  // }

  private getUpdateInfo(transaction: any, orderDetail: any): { shouldUpdate: boolean; type: string } {
    // Check if the order is cancelled
    if (orderDetail.status === 'CANCELED') {
      return { shouldUpdate: true, type: 'cancel' };
    }
    
    // Check if order is fully filled by comparing executed quantity with original amount
    if (
      orderDetail.executedQty &&
      parseFloat(orderDetail.executedQty) === parseFloat(transaction.amount || '0')
    ) {
      return { shouldUpdate: true, type: 'trade' };
    }

    // No update needed
    return { shouldUpdate: false, type: 'none' };
  }

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} checkOrderStatus of type ${job.name}...`,
    );
  }
}