import { OnQueueActive, Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import Decimal from 'decimal.js';
import { SystemBalance } from 'src/balance/entities/system-balance.entity';
import { EntityManager } from 'typeorm';
import { BalanceService } from 'src/balance/balance.service';
import { UserKey } from 'src/user_key/entities/user_key.entity';
import { User } from 'src/user/entities/user.entity';
import { truncateToDecimals } from 'common/util/utils';

export interface BalanceUpdateJobData {
  user_key_id: string;
  symbol: string;
  available_balance_delta: string; // Positive to add, negative to subtract
  freeze_balance_delta: string;   // Positive to add, negative to subtract
  price_dp: number;
  fee?: string;
}

@Processor('balance-update')
export class BalanceUpdateQueueService {
  constructor(
    private em: EntityManager,
    private balanceService: BalanceService,
  ) {}

  @Process('processing')
  async processBalanceUpdate(job: Job<BalanceUpdateJobData>) {
    try {
      const data = job.data;
      console.log('====================================Start processBalanceUpdate===================================');
      
      // Get or create system balance record
      let systemBalance = await this.getOrCreateSystemBalance(data.user_key_id, data.symbol);
      console.log("The system balance get", systemBalance);

      const availableDelta = new Decimal(data.available_balance_delta || 0);
      const freezeDelta = new Decimal(data.freeze_balance_delta || 0);
      
      // Apply deltas directly
      const currentAvailable = new Decimal(systemBalance.avai_balance);
      const currentFreeze = new Decimal(systemBalance.freeze_balance);
      
      const newAvailable = currentAvailable.plus(availableDelta);
      const newFreeze = currentFreeze.plus(freezeDelta);
      
      console.log('[BALANCE UPDATE]');
      console.log(`Available delta: ${availableDelta.toString()}`);
      console.log(`Freeze delta: ${freezeDelta.toString()}`);
      console.log(`Available before: ${currentAvailable.toString()}, after: ${newAvailable.toString()}`);
      console.log(`Freeze before: ${currentFreeze.toString()}, after: ${newFreeze.toString()}`);
      
      // Validate before saving
      if (newAvailable.lt(0)) {
        throw new Error(`Insufficient available balance: would become ${newAvailable}`);
      }
      if (newFreeze.lt(0)) {
        throw new Error(`Insufficient freeze balance: would become ${newFreeze}`);
      }
      
      systemBalance.avai_balance = newAvailable.toString();
      systemBalance.freeze_balance = newFreeze.toString();
      
      this.validateBalanceData(systemBalance);
      await this.em.save(SystemBalance, systemBalance);
      console.log('====================================Done processBalanceUpdate===================================');
    } catch (error) {
      console.error('Balance update error:', error);
      throw error;
    }
  }
  private async getOrCreateSystemBalance(
    userKeyId: string,
    symbol:    string,
  ): Promise<SystemBalance> {
    // 1️⃣ Attempt to load via QueryBuilder
    let systemBalance = await this.em
      .createQueryBuilder(SystemBalance, 'sb')
      .where('sb.user_key_id = :uid AND sb.symbol = :sym', {
        uid: userKeyId,
        sym: symbol,
      })
      .getOne();

    if (!systemBalance) {
      console.log(
        'No existing system balance found. Calling updateSinglePlatformSystemBalance to initialize.'
      );

      // load UserKey
      const userKey = await this.em
        .createQueryBuilder(UserKey, 'uk')
        .where('uk.id = :id', { id: Number(userKeyId) })
        .getOne();
      if (!userKey) throw new Error(`UserKey not found: ${userKeyId}`);

      // load User
      const user = await this.em
        .createQueryBuilder(User, 'u')
        .where('u.id = :id', { id: userKey.user_id })
        .getOne();
      if (!user) throw new Error(`User not found: ${userKey.user_id}`);

      // initialize via your service
      await this.balanceService.updateSinglePlatformSystemBalance(user, {
        user_key_id: userKeyId,
      });

      // re-load; if still not present, create minimal record
      systemBalance = await this.em
        .createQueryBuilder(SystemBalance, 'sb')
        .where('sb.user_key_id = :uid AND sb.symbol = :sym', {
          uid: userKeyId,
          sym: symbol,
        })
        .getOne()
        || new SystemBalance();

      if (!systemBalance.id) {
        console.log('Creating minimal balance record.');
        systemBalance.user_key_id         = userKeyId;
        systemBalance.symbol              = symbol;
        systemBalance.avai_balance        = '0';
        systemBalance.freeze_balance      = '0';
        systemBalance.avai_balance_quote  = '0';
        systemBalance.freeze_balance_quote= '0';
      }
    }

    return systemBalance;
  }

  private validateBalanceData(systemBalance: SystemBalance) {
    const fields = ['avai_balance', 'freeze_balance', 'avai_balance_quote', 'freeze_balance_quote'];
    
    for (const field of fields) {
      const value = new Decimal(systemBalance[field] || '0');
      if (value.isNegative()) {
        throw new Error(`Invalid ${field}: ${systemBalance[field]}. Balance cannot be negative.`);
      }
    }
  }

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing balance update job ${job.id} of type ${job.name} with data:`,
      job.data,
    );
  }
}
