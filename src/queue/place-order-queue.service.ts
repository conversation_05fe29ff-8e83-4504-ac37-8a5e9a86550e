import { InjectQueue, OnQueueActive, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import { decrypted } from 'common/util/utils';
import configuration from 'config/configuration';
import Decimal from 'decimal.js';
import { Gridbot, GridBotStatus } from 'src/gridbot/entities/gridbot.entity';
import {
  Transaction,
  TransactionStatus,
} from 'src/transactions/entities/transaction.entity';
import { EntityManager, In, Not } from 'typeorm';
import { truncateToDecimals } from 'common/util/utils';

@Processor('place-order')
export class PlaceOrderQueueService {
  constructor(
    private em: EntityManager,
    @InjectQueue('balance-update') private balanceUpdateQueue: Queue,
  ) {}

  @Process('processing')
  async readOperationJob3(job: Job) {
    let data = job.data;
    let matched_order;

    let user_key = data.user_key;
    let order = data.order;
    let pair = data.pair;

    try {
      let platform = data.platform;
      let fee_pct = data.fee_pct;
      let platformHelper = await callPlatformHelper(
        platform.name.toLowerCase(),
      );

      // STEP 1: Update balance BEFORE placing order (money going out - security first)
      // Assume this balance always successful, since only enough balance then only come into this queue
      //Let say XRPMYR buy order palced, move MYR(quote) available to freeze

      if (data.is_buy) {
        // Buy order placement
        const buyAmount = truncateToDecimals(
          new Decimal(order.quantity).mul(order.price),
          pair.price_dp,
        ).toString();

        // await this.balanceUpdateQueue.add('processing', {
        //   user_key_id: user_key.id,
        //   symbol: pair.quote,
        //   available_balance_delta: new Decimal(buyAmount).negated().toString(), // Minus available
        //   freeze_balance_delta: buyAmount, // Add freeze
        //   price_dp: pair.price_dp,
        //   fee: user_key.fee,
        // });
      } else {
        // Sell order placement
        // await this.balanceUpdateQueue.add('processing', {
        //   user_key_id: user_key.id,
        //   symbol: pair.base,
        //   available_balance_delta: new Decimal(order.quantity).negated().toString(), // Minus base XRP avai
        //   freeze_balance_delta: order.quantity, // Add base freeze
        //   price_dp: pair.price_dp,
        //   fee: user_key.fee,
        // });
      }

      // STEP 2: Initialize platform helper
      await platformHelper.init(
        platform.name.toLowerCase(),
        user_key.api_key,
        user_key.api_secret,
      );

      // STEP 3: Place order on exchange
      let place_order;
      try {
        place_order = await platformHelper.createOrder(order);
      } catch (error) {
        //Action to unfreeze the balance if order failed to place
        if (data.is_buy) {
          //Let say XRPMYR buy order failed, move MYR(quote) freeze to available
          const buyAmount = truncateToDecimals(
            new Decimal(order.quantity).mul(order.price),
            pair.price_dp,
          ).toString();

          // Order failure (reverse placement)
          // await this.balanceUpdateQueue.add('processing', {
          //   user_key_id: user_key.id,
          //   symbol: pair.quote, // For buy failure
          //   available_balance_delta: buyAmount, //Add back quote avai MYR
          //   freeze_balance_delta: new Decimal(buyAmount).negated().toString(), // Minus quote freeze MYR
          //   fee: user_key.fee,
          //   price_dp: pair.price_dp,
          // });
        } else {
          //Let say XRPMYR buy order failed, move MYR(quote) freeze to available
          // await this.balanceUpdateQueue.add('processing', {
          //   user_key_id: user_key.id,
          //   symbol: pair.base,
          //   available_balance_delta: order.quantity, // Add base avai XRP
          //   freeze_balance_delta: new Decimal(order.quantity).negated().toString(), // Minus base freeze XRP
          //   price_dp: pair.price_dp,
          //   fee: user_key.fee,
          // });
        }

        await this.em
          .createQueryBuilder()
          .update(Transaction)
          .set({ status: TransactionStatus.OPEN })
          .where('id = :id ', { id: data.id })
          .execute();
        return;
      }

      // let place_order = { id: 'test' + Date.now() };
      //matched_order need to be different with order_id
      // STEP 5: Update transaction with order ID
      if (data.matched_order) {
        matched_order = data.matched_order;
      } else {
        let match_order = 'trade' + Date.now();
        matched_order = data.is_buy ? match_order + '-' : '-' + match_order;
      }

      await this.em
        .createQueryBuilder()
        .update(Transaction)
        .set({ order_id: place_order.id, matched_order })
        .where('id = :id ', { id: data.id })
        .execute();
    } catch (error) {
      console.log(error);
      await this.em
        .createQueryBuilder()
        .update(Gridbot)
        .set({ status: GridBotStatus.ERROR })
        .where('uuid = :uuid', { uuid: data.uuid })
        .execute();

      // get uuid
      //check balance
      //market buy
      //place order
      //record order

      // throw error;
    }
  }

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} of type ${job.name} with data ${job.data}...`,
    );
  }
}
