import { InjectQueue, OnQueueActive, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import { decrypted } from 'common/util/utils';
import configuration from 'config/configuration';
import Decimal from 'decimal.js';
import { Gridbot, GridBotStatus } from 'src/gridbot/entities/gridbot.entity';
import { Pair } from 'src/pair/entities/pair.entity';
import {
  Transaction,
  TransactionStatus,
} from 'src/transactions/entities/transaction.entity';
import { EntityManager, In, Not } from 'typeorm';
import { CreateBotEventQueueService } from './create-bot-event-queue.service';
import { truncateToDecimals } from 'common/util/utils';

@Processor('cancel-order')
export class CancelOrderQueueService {
  constructor(
    private em: EntityManager,
    @InjectQueue('create-event') private createEventQueue: Queue,
    @InjectQueue('balance-update') private balanceUpdateQueue: Queue,
  ) {}

  @Process('processing')
  async readOperationJob3(job: Job) {
    let data = job.data;

    try {
      let pair = data.pair;
      let platform = data.platform;
      let platformHelper = await callPlatformHelper(
        platform.name.toLowerCase(),
      );
      let user_key = data.user_key;
      let order = data.order;

      // STEP 1: Cancel order on exchange first
      await platformHelper.init(
        platform.name.toLowerCase(),
        user_key.api_key,
        user_key.api_secret,
      );

      let cancelSuccess = true;
      if (order.order_id) {
        try {
          let cancel_order = await platformHelper.cancelOrder(
            order.order_id,
            pair.symbol,
          );
        } catch (cancelError) {
          console.error('Exchange cancel order failed:', cancelError);
          cancelSuccess = false;
          // Don't throw here, we still want to update our database status
        }
      }

      // STEP 2: Update transaction status
      order.status = TransactionStatus.CANCELLED;
      await this.em.save(Transaction, order);

      // STEP 3: Update balance AFTER successful cancellation (money coming back)
      if (cancelSuccess) {
        try {
          if (order.is_buy) {
            let availableBalanceChange = truncateToDecimals(
              new Decimal(order.amount).mul(order.price),
              pair.price_dp,
            ).toString(); // should calculate how many MYR going to move back to available
            let feezeBalanceChange = truncateToDecimals(
              new Decimal(order.amount).mul(order.price).negated(),
              pair.price_dp,
            ).toString(); // Should be negative

            //Let say XRPMYR buy order failed, move MYR(quote) freeze to available
            await this.balanceUpdateQueue.add('processing', {
              user_key_id: user_key.id,
              symbol: pair.quote,
              available_balance_delta: availableBalanceChange,
              freeze_balance_delta: feezeBalanceChange,
              fee: user_key.fee,
              price_dp: pair.price_dp,
            });
          } else {
            //Let say XRPMYR buy order failed, move MYR(quote) freeze to available
            // await this.balanceUpdateQueue.add('processing', {

            await this.balanceUpdateQueue.add('processing', {
              user_key_id: user_key.id,
              symbol: pair.base,
              available_balance_delta: order.amount,
              freeze_balance_delta: new Decimal(order.amount)
                .negated()
                .toString(),
              fee: user_key.fee,
              price_dp: pair.price_dp,
            });
          }
        } catch (balanceError) {
          console.error(
            'Balance update failed during cancellation:',
            balanceError,
          );
          // Log but don't throw - order is already cancelled on exchange
        }
      }
    } catch (error) {
      await this.em
        .createQueryBuilder()
        .update(Gridbot)
        .set({ status: GridBotStatus.ERROR })
        .where('uuid = :uuid', { uuid: data.uuid })
        .execute();
      await this.createEventQueue.add(
        'processing',
        {
          botType: 'gridbot',
          uuid: data.uuid,
          eventType: CreateBotEventQueueService.EVENT_TYPES.BOT_ERROR,
          params: ['cancel', error.message],
        },
        { removeOnComplete: true },
      );
      console.log(error);
    }
    // get uuid
    //check balance
    //market buy
    //place order
    //record order
  }

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} cancel order of type ${job.name} with data ${job.data}...`,
    );
  }
}
