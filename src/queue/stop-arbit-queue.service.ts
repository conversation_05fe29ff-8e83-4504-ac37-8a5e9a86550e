import { InjectQueue, OnQueueActive, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { Gridbot, GridBotStatus } from 'src/gridbot/entities/gridbot.entity';
import { Transaction } from 'src/transactions/entities/transaction.entity';
import { EntityManager, In, Not } from 'typeorm';
import {removeSocketConnection} from 'common/util/utils';
import { ArbitBotStatus, ArbitrageBot } from 'src/arbitrage_bot/entities/arbitrage_bot.entity';
import { CreateBotEventQueueService } from './create-bot-event-queue.service';


@Processor('stop-arbit')
export class StopArbitQueueService {
  constructor(
    private em: EntityManager,
    @InjectQueue('cancel-order') private cancelOrderQueue: Queue,
    @InjectQueue('create-event') private createEventQueue: Queue,
    
  ) {}

  @Process('end-arbit')
  async readOperationJob3(job: Job) {
    let data = job.data;

    // Check if any other active bots are using the base pair
    const basePairActiveCount = await this.em
      .createQueryBuilder(ArbitrageBot, 'ab')
      .where('ab.status = :status AND (ab.base_pair_id = :pairId OR ab.target_pair_id = :pairId)', {
        status: ArbitBotStatus.ACTIVE,
        pairId: data.base_pair.id,
      })
      .getCount();

    // Check if any other active bots are using the target pair
    const targetPairActiveCount = await this.em
      .createQueryBuilder(ArbitrageBot, 'ab')
      .where('ab.status = :status AND (ab.base_pair_id = :pairId OR ab.target_pair_id = :pairId)', {
        status: ArbitBotStatus.ACTIVE,
        pairId: data.target_pair.id,
      })
      .getCount();

    // Disconnect base pair if no active bots are using it
    if (basePairActiveCount === 0) {
      await removeSocketConnection(data.base_pair.id.toString());
    }

    // Disconnect target pair if no active bots are using it
    if (targetPairActiveCount === 0) {
        await removeSocketConnection(data.target_pair.id.toString());
    }
  }
  

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} stop grid of type ${job.name} with data ${job.data}...`,
    );
  }
}
