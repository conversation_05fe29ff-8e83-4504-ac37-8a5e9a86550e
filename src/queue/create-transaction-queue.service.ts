import { InjectQueue, OnQueueActive, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import { decrypted } from 'common/util/utils';
import configuration from 'config/configuration';
import Decimal from 'decimal.js';
import Redis from 'ioredis';
import { Gridbot, GridBotStatus } from 'src/gridbot/entities/gridbot.entity';
import {
  Transaction,
  TransactionStatus,
} from 'src/transactions/entities/transaction.entity';
import { EntityManager, In, Not } from 'typeorm';

@Processor('create-transaction')
export class CreateTransactionQueueService {
  constructor(private em: EntityManager) {}

  @Process('processing')
  async readOperationJob3(job: Job) {
    try {
      let data = job.data;

      let order = data.order;
      let user_key = data.user_key;
      let record = new Transaction();
      if (data.matched_order) {
        record.matched_order = data.matched_order;
      }
      record.uuid = data.uuid;
      record.fee = new Decimal(order.price)
        .mul(new Decimal(data.amount))
        .mul(new Decimal(data.fee_pct))
        .toString();
      record.amount = data.amount;
      record.bot_type = data.bot_type;
      record.is_buy = data.is_buy;
      record.platform_id = data.platform.id;
      record.price = order.price;
      record.prv_amount = data.prv_amount ? data.prv_amount : '0';
      record.target_pair_id = data.pair.id;
      record.user_key_id = user_key.id;
      if (data.status && data.status === 'NEW') {
        record.status = TransactionStatus.NEW;
      }

      await this.em.save(Transaction, record);
    } catch (error) {
      console.log(error);
    }
  }

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} of type ${job.name} with data ${job.data}...`,
    );
  }
}
