import {
  InjectQueue,
  OnQueueActive,
  OnQueueError,
  OnQueueFailed,
  Process,
  Processor,
} from '@nestjs/bull';
import { BadRequestException } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import {
  calculateConstBaseAndQuote,
  decrypted,
  refreshConnection,
  truncateToDecimals,
} from 'common/util/utils';
import configuration from 'config/configuration';
import Decimal from 'decimal.js';
import Redis from 'ioredis';
import {
  ArbitBotStatus,
  ArbitrageBot,
} from 'src/arbitrage_bot/entities/arbitrage_bot.entity';
import { Gridbot, GridBotStatus } from 'src/gridbot/entities/gridbot.entity';
import {
  Transaction,
  TransactionStatus,
} from 'src/transactions/entities/transaction.entity';
import { EntityManager, In, Not } from 'typeorm';

@Processor('grid-checking')
export class GridCheckingQueueService {
  private redisClient: Redis;
  constructor(
    private em: EntityManager,
    @InjectQueue('place-order') private placeOrderQueue: Queue,
  ) {
    this.redisClient = new Redis({
      host: configuration().redis.host,
      password: configuration().redis.password,
    });
  }

  @Process('check-criterea')
  async readOperationJob3(job: Job) {
    //call stream service
    //subscribe to redis-changes pair1 and pair2
    //keep current state and compare the price everytime price update from redis

    let data = job.data;
    // let [ask, bid] = await Promise.all([
    //   this.redisClient.get(`${data.id}:ask`),
    //   this.redisClient.get(`${data.id}:bid`),
    // ]);
    let ask: any = await this.redisClient.get(data.id + ':ask');
    let bid: any = await this.redisClient.get(data.id + ':bid');

    ask = JSON.parse(ask);
    bid = JSON.parse(bid);
    // ask.last_interaction = Date.now();
    // bid.last_interaction = Date.now();
    // await this.redisClient.set(
    //   data.id + ':ask',
    //   JSON.stringify({
    //     ...ask,
    //   }),
    // );
    // await this.redisClient.set(
    //   data.id + ':bid',
    //   JSON.stringify({
    //     ...bid,
    //   }),
    // );
    // console.log(ask, 'ask');
    // console.log(bid, 'bid');
    // Create independent query builders for sell and buy grid bots
    const sellQueryBuilder = this.createBaseQueryBuilder(data.id);
    const buyQueryBuilder = this.createBaseQueryBuilder(data.id);

    let sellgridBots = await sellQueryBuilder
      .select([
        'transaction.uuid as uuid',
        'transaction.id as id',
        'transaction.matched_order as matched_order',
        'transaction.price as price',
        'transaction.amount as amount',
        'transaction.is_buy as is_buy',
      ])
      .addSelect(
        `JSON_OBJECT(
      'id', platform.id,
      'name', platform.name
    )`,
        'platform',
      )
      .addSelect(
        `JSON_OBJECT(
      'id', user_key.id,
      'api_key', user_key.api_key,
      'api_secret', user_key.api_secret,
      'fee', user_key.maker_fee
    )`,
        'user_key',
      )
      .addSelect(
        `JSON_OBJECT(
      'id', pair.id,
      'symbol', pair.symbol,
      'price', pair.price,
      'min_notional', pair.min_notional,
      'price_dp', pair.price_dp,
      'qty_dp', pair.qty_dp,
      'base', pair.base,
      'quote', pair.quote,
      'tick', CAST(pair.tick AS CHAR),
      'step', CAST(pair.step AS CHAR),
      'symbol', pair.symbol
    )`,
        'pair',
      )
      .andWhere(
        `(transaction.price > :buy_price and transaction.is_buy = false )`,
        {
          buy_price: Number(bid.price),
        },
      )
      .getRawMany();
    let buygridBots = await buyQueryBuilder
      .select([
        'transaction.uuid as uuid',
        'transaction.matched_order as matched_order',
        'transaction.id as id',
        'transaction.price as price',
        'transaction.amount as amount',
        'transaction.is_buy as is_buy',
      ])
      .addSelect(
        `JSON_OBJECT(
      'id', platform.id,
      'name', platform.name
    )`,
        'platform',
      )
      .addSelect(
        `JSON_OBJECT(
      'id', user_key.id,
      'api_key', user_key.api_key,
      'api_secret', user_key.api_secret,
      'fee', user_key.maker_fee
    )`,
        'user_key',
      )
      .addSelect(
        `JSON_OBJECT(
      'id', pair.id,
      'symbol', pair.symbol,
      'price', pair.price,
      'min_notional', pair.min_notional,
      'price_dp', pair.price_dp,
      'qty_dp', pair.qty_dp,
      'base', pair.base,
      'quote', pair.quote,
      'tick', CAST(pair.tick AS CHAR),
      'step', CAST(pair.step AS CHAR),
      'symbol', pair.symbol
    )`,
        'pair',
      )
      .andWhere(
        `transaction.price < :sell_price and transaction.is_buy = true `,
        { sell_price: Number(ask.price) },
      )
      .getRawMany();

    console.log('sellgridBots', sellgridBots.length);
    console.log('buygridBots', buygridBots.length);
    for (const gridBot of sellgridBots) {
      console.log('sellbot', gridBot);
      let order = {
        ...gridBot,
        order: {
          symbol: gridBot.pair.symbol,
          side: gridBot.is_buy ? 'BUY' : 'SELL',
          type: 'LIMIT',
          quantity: gridBot.amount,
          price: gridBot.price,
        },
      };
      console.log(order);
      await this.em
        .createQueryBuilder()
        .update(Transaction)
        .set({ status: TransactionStatus.NEW })
        .where('id = :id ', { id: gridBot.id })
        .execute();

        
      await this.placeOrderQueue.add('processing', order);
    }
    for (const gridBot of buygridBots) {
      console.log('buyBot', gridBot);
      let order = {
        ...gridBot,
        order: {
          symbol: gridBot.pair.symbol,
          side: gridBot.is_buy ? 'BUY' : 'SELL',
          type: 'LIMIT',
          quantity: gridBot.amount,
          price: gridBot.price,
        },
      };
      console.log(order);
      await this.em
        .createQueryBuilder()
        .update(Transaction)
        .set({ status: TransactionStatus.NEW })
        .where('id = :id ', { id: gridBot.id })
        .execute();
      await this.placeOrderQueue.add('processing', order);
    }
  }

  private createBaseQueryBuilder(pairId: string) {
    return this.em
      .createQueryBuilder(Transaction, 'transaction')
      .leftJoin('user_key', 'user_key', 'transaction.user_key_id = user_key.id')
      .leftJoin('user', 'user', 'user_key.user_id = user.id')
      .leftJoin('platform', 'platform', 'transaction.platform_id = platform.id')
      .leftJoin('pair', 'pair', 'transaction.target_pair_id = pair.id ')
      .where("transaction.status = 'OPEN'")
      .andWhere('transaction.target_pair_id = :pair_id', {
        pair_id: pairId,
      });
  }

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} start grid of type ${job.name} with data ${job.data}...`,
    );
  }

  @OnQueueError()
  onError(job: Job) {
    console.log(job);
    console.log(
      `Processing job ${job.id} error grid of type ${job.name} with data ${job.data}...`,
    );
  }

  @OnQueueFailed()
  onFail(job: Job) {
    console.log(job);
    console.log(
      `Processing job ${job.id} fail grid of type ${job.name} with data ${job.data}...`,
    );
  }
}
