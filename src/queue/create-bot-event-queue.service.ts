import { InjectQueue, OnQueueA<PERSON>, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import { decrypted } from 'common/util/utils';
import configuration from 'config/configuration';
import Decimal from 'decimal.js';
import Redis from 'ioredis';
import { BotEvent } from 'src/bot-event/entities/bot-event.entity';
import { Gridbot, GridBotStatus } from 'src/gridbot/entities/gridbot.entity';
import { Transaction } from 'src/transactions/entities/transaction.entity';
import { EntityManager, In, Not } from 'typeorm';

@Processor('create-event')
export class CreateBotEventQueueService {
  static readonly EVENT_TYPES = {
    BOT_CREATED: 'Bot created',
    BOT_STARTED: 'Bot started',
    BOT_EDITED: 'Bot edited',
    PLACING_ORDER:
      'Placing {0} Order({1} out of {2}). Price {3} {4}, Size {5} {6}',
    ORDER_EXECUTED:
      '{0} Order executed. Price {1} {2}, Size {3} {4}, placing new order',
    ORDER_CANCELLED: 'Order cancelled from exchange, placing new order',
    BOT_SHUTDOWN: 'Bot shut down, cancel all orders, status changed to deleted',
    CANCEL_ORDER: 'Cancel order ({0} out of {1})',
    BOT_ERROR: 'Bot hitting error when {0} order, status changed to error. {1}',
    INSUFFICIENT_BALANCE: 'Insufficient balance to place order',
  };
  constructor(private em: EntityManager) {}

  @Process('processing')
  async readOperationJob3(job: Job) {
    try {
      const data = job.data;
      // console.log(data);
      // A more detail console log for debugging
      console.log(`Processing bot event job ${job.id} with data:`, JSON.stringify(data, null, 2));
      const eventData = this.formatEventData(data.eventType, ...data.params);

      // Create and save the bot event in one step
      await this.em.save(BotEvent, {
        uuid: data.uuid,
        bot_type: data.botType,
        event_data: eventData,
      });

      // Log successful event creation
      console.log(
        `Created bot event for ${data.botType} (${data.uuid}): ${data.eventType}`,
      );
    } catch (error) {
      console.error('Failed to create bot event:', error);
    }
  }

  /**
   * Formats event data with parameters
   * @param eventType The type of event (use constants from EVENT_TYPES)
   * @param params Parameters to insert into the event message
   * @returns Formatted event message
   */
  formatEventData(eventType: string, ...params: any[]): string {
    let message =
      CreateBotEventQueueService.EVENT_TYPES[eventType] || eventType;

    // Replace placeholders with parameters
    params.forEach((param, index) => {
      message = message.replace(`{${index}}`, param);
    });

    return message;
  }

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} of type ${job.name} with data ${job.data}...`,
    );
  }
}
