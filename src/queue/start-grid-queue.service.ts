import { InjectQueue, OnQueueActive, Process, Processor } from '@nestjs/bull';
import { BadRequestException } from '@nestjs/common';
import { Job, Queue } from 'bull';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import {
  addGridSocketConnection,
  calculateConstBaseAndQuote,
  refreshConnection,
} from 'common/util/utils';
import Decimal from 'decimal.js';
import { Gridbot, GridBotStatus } from 'src/gridbot/entities/gridbot.entity';
import { EntityManager, In, Not } from 'typeorm';
import Redis from 'ioredis';
import configuration from 'config/configuration';
import { CreateBotEventQueueService } from './create-bot-event-queue.service';

@Processor('start-grid')
export class StartGridQueueService {
  private redisClient: Redis;
  constructor(
    private em: EntityManager,
    @InjectQueue('create-transaction') private createTransactionQueue: Queue,
    @InjectQueue('create-event') private createEventQueue: Queue,
    @InjectQueue('grid-checking') private gridCheckingQueue: Queue,
  ) {
    this.redisClient = new Redis({
      host: configuration().redis.host,
      password: configuration().redis.password,
    });
  }

  @Process('init-grid')
  async readOperationJob3(job: Job) {
    let data = job.data;
    let platform = data.platform;
    let platformHelper = await callPlatformHelper(platform.name.toLowerCase());
    let user_key = data.user_key;
    let pair = data.pair;
    let balance = data.balance;
    let ticker_price = data.ticker_price;

    await platformHelper.init(
      platform.name.toLowerCase(),
      user_key.api_key,
      user_key.api_secret,
    );
    let orderbook = await platformHelper.getOrderBook(pair.symbol);
    let lask = {
      price: orderbook.asks.length != 0 ? orderbook.asks[0].price : null,
      qty: orderbook.asks.length != 0 ? orderbook.asks[0].qty : null,
    };
    let lbid = {
      price: orderbook.bids.length != 0 ? orderbook.bids[0].price : null,
      qty: orderbook.bids.length != 0 ? orderbook.bids[0].qty : null,
    };
    await this.redisClient.set(
      pair.id + ':ask',
      JSON.stringify({
        ...lask,
        last_updated: Date.now(),
        last_interaction: Date.now(),
      }),
    );
    await this.redisClient.set(
      pair.id + ':bid',
      JSON.stringify({
        ...lbid,
        last_updated: Date.now(),
        last_interaction: Date.now(),
      }),
    );

    let {
      base_required,
      quote_required,
      sell_orders,
      buy_orders,
      buy_count,
      sell_count,
    } = await calculateConstBaseAndQuote(data, pair, ticker_price.close);

    let baseBalance = balance.find((item) => item.symbol === pair.base);
    let quoteBalance = balance.find((item) => item.symbol === pair.quote);
    baseBalance = new Decimal(baseBalance.avai_balance);
    quoteBalance = new Decimal(quoteBalance.avai_balance);
    if (
      quoteBalance.lessThan(quote_required.mul(0.04)) ||
      baseBalance.lessThan(base_required.mul(0.04))
    ) {
      throw new BadRequestException(
        CustomErrorMessages.BOT.INSUFFICIENT_FUNDS +
          ': \n' +
          pair.quote +
          ' ' +
          quote_required.mul(0.04) +
          ' is required \n' +
          pair.base +
          ' ' +
          base_required.mul(0.04) +
          ' is required',
      );
    }
    let index = 1;
    for (const sell_order of sell_orders) {
      await this.createTransactionQueue.add('processing', {
        platform,
        user_key,
        order: {
          symbol: pair.symbol,
          side: 'SELL',
          type: 'LIMIT',
          quantity: sell_order.amount,
          price: sell_order.price,
        },
        uuid: data.uuid,
        amount: sell_order.amount,
        bot_type: 'GRIDBOT',
        is_buy: false,
        pair,
        fee_pct: data.user_key.maker_fee,
      });
      await this.createEventQueue.add(
        'processing',
        {
          botType: 'gridbot',
          uuid: data.uuid,
          eventType: CreateBotEventQueueService.EVENT_TYPES.PLACING_ORDER,
          params: [
            'SELL',
            index++,
            buy_orders.length + sell_orders.length,
            sell_order.price,
            pair.quote,
            sell_order.amount,
            pair.base,
          ],
        },
        { removeOnComplete: true },
      );
    }
    for (const buy_order of buy_orders) {
      await this.createTransactionQueue.add('processing', {
        platform,
        user_key,
        order: {
          symbol: pair.symbol,
          side: 'BUY',
          type: 'LIMIT',
          quantity: buy_order.amount,
          price: buy_order.price,
        },
        uuid: data.uuid,
        amount: buy_order.amount,
        bot_type: 'GRIDBOT',
        is_buy: true,
        pair,
        fee_pct: data.user_key.maker_fee,
      });
      await this.createEventQueue.add(
        'processing',
        {
          botType: 'gridbot',
          uuid: data.uuid,
          eventType: CreateBotEventQueueService.EVENT_TYPES.PLACING_ORDER,
          params: [
            'BUY',
            index++,
            buy_orders.length + sell_orders.length,
            buy_order.price,
            pair.quote,
            buy_order.amount,
            pair.base,
          ],
        },
        { removeOnComplete: true },
      );
    }
    await this.em
      .createQueryBuilder()
      .update(Gridbot)
      .set({
        status: GridBotStatus.ACTIVE,
        initial_price: pair.price,
        initial_base:
          sell_count != 0
            ? Decimal(sell_count).mul(sell_orders[0].amount).toString()
            : '0',
        initial_quote:
          buy_count != 0
            ? Decimal(buy_count)
                .mul(buy_orders[0].amount)
                .mul(pair.price)
                .toString()
            : '0',
        current_base:
          sell_count != 0
            ? Decimal(sell_count).mul(sell_orders[0].amount).toString()
            : '0',
        current_quote:
          buy_count != 0
            ? Decimal(buy_count)
                .mul(buy_orders[0].amount)
                .mul(pair.price)
                .toString()
            : '0',
      })
      .where('uuid = :uuid', { uuid: data.uuid })
      .execute();
    const [ask, bid] = await Promise.all([
      this.redisClient.get(`${data.pair.id}:ask`),
      this.redisClient.get(`${data.pair.id}:bid`),
    ]);
    await this.gridCheckingQueue.add(
      'check-criterea',
      {
        id: data.pair.id,
        price: JSON.parse(ask).price,
        side: 'sell',
      },
      { delay: 10000 },
    );
    await this.gridCheckingQueue.add(
      'check-criterea',
      {
        id: data.pair.id,
        price: JSON.parse(bid).price,
        side: 'bid',
      },
      { delay: 10000 },
    );
    await this.createEventQueue.add(
      'processing',
      {
        botType: 'gridbot',
        uuid: data.uuid,
        eventType: CreateBotEventQueueService.EVENT_TYPES.BOT_STARTED,
        params: [],
      },
      { removeOnComplete: true },
    );
    refreshConnection();
    await addGridSocketConnection(pair.id, platform.id);
  }

  @OnQueueActive()
  onActive(job: Job) {
    console.log(
      `Processing job ${job.id} start grid of type ${job.name} with data ${job.data}...`,
    );
  }
}
