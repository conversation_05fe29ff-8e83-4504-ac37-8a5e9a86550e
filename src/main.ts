import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import {
  JsendObjectError,
  JsendObjectSuccess,
} from 'common/interface/jsend-interface';
import { PageDto } from 'common/dto/pageResponse.dto';
import { PageOptionsDto } from 'common/dto/pagination.dto';
import * as cookieParser from 'cookie-parser';
import { HttpExceptionFilter } from '../common/filter/http-exception.filter';
import { GlobalValidationPipes } from '../common/pipe/validation.pipe';
import { ValidationPipe } from '@nestjs/common';
import { ResponseInterceptor } from '../common/interceptor/response.interceptor';
import { ExpressAdapter } from '@bull-board/express';
import configuration from 'config/configuration';
import { createBullBoard } from '@bull-board/api';
import * as compression from 'compression';
import { Logger } from 'nestjs-pino';
const { BullAdapter } = require('@bull-board/api/bullAdapter');
const Queue = require('bull');

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { logger: false });
  const option = new DocumentBuilder()
    .setTitle(`Api Doc`)
    .setVersion('1.0')
    .build();
  app.enableCors({
    credentials: true,
    origin: [
      'https://arcabot.hata.io',
      'http://localhost:5173',
      'https://arcabot.cake5x.com',
      'https://stg-arcabot.cake5x.com',
    ],
  });
  const document = SwaggerModule.createDocument(app, option, {
    extraModels: [
      JsendObjectSuccess,
      JsendObjectError,
      PageDto,
      PageOptionsDto,
    ],
  });
  // Set global no-cache headers
  app.use((req, res, next) => {
    if (req.path.includes('/api')) {
      res.setHeader(
        'Cache-Control',
        'no-store, no-cache, must-revalidate, proxy-revalidate',
      );
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');
    }
    next();
  });
  const serverAdapter = new ExpressAdapter();
  serverAdapter.setBasePath('/admin/queues');
  let redis = {
    redis: configuration().redis,
  };
  const { addQueue, setQueues, replaceQueues } = createBullBoard({
    queues: [
      new BullAdapter(new Queue('cancel-order', redis)),
      new BullAdapter(new Queue('balance-update', redis)),
      new BullAdapter(new Queue('create-transaction', redis)),
      new BullAdapter(new Queue('place-order', redis)),
      new BullAdapter(new Queue('update-order', redis)),
      new BullAdapter(new Queue('arbit-checking', redis)),
      new BullAdapter(new Queue('grid-checking', redis)),
      new BullAdapter(new Queue('start-grid', redis)),
      new BullAdapter(new Queue('stop-grid', redis)),
      new BullAdapter(new Queue('start-arbit', redis)),
      new BullAdapter(new Queue('stop-arbit', redis)),
      new BullAdapter(new Queue('realize-checking', redis)),
      new BullAdapter(new Queue('socket-data', redis)),
      new BullAdapter(new Queue('check-order-status', redis)),
    ],
    serverAdapter,
  });
  app.use(cookieParser());
  app.use('/admin/queues', serverAdapter.getRouter());
  app.useGlobalInterceptors(new ResponseInterceptor());
  app.useGlobalFilters(new HttpExceptionFilter());
  app.useGlobalPipes(
    new GlobalValidationPipes(),
    new ValidationPipe({ transform: true }),
  );
  app.useLogger(app.get(Logger));
  app.use(compression());

  SwaggerModule.setup('api', app, document, {
    swaggerOptions: { defaultModelsExpandDepth: -1 },
  });
  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
