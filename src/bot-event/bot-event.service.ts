import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateBotEventDto } from './dto/create-bot-event.dto';
import { UpdateBotEventDto } from './dto/update-bot-event.dto';
import { GetBotEventDto } from './dto/get-bot-event.dto';
import { User } from 'src/user/entities/user.entity';
import { EntityManager } from 'typeorm';
import { BotEvent } from './entities/bot-event.entity';
import { PageDto, PageMetaDto } from 'common/dto/pageResponse.dto';
import { PaginateByObject } from 'common/dto/pagination.dto';

@Injectable()
export class BotEventService {
  constructor(private em: EntityManager) {}
  async findOne(user: User, getBotEventDto: GetBotEventDto) {
    let uuid = await this.em.query(
      `
      SELECT gb.uuid
      FROM 
        gridbot gb
      WHERE 
        gb.uuid = ? and gb.user_key_id in (SELECT uk.id FROM user_key uk WHERE uk.user_id = ?)
      
      UNION
      
      SELECT ab.uuid
      FROM 
        arbitrage_bot ab
      WHERE 
        ab.uuid = ? and ab.base_key_id in (SELECT uk.id FROM user_key uk WHERE uk.user_id = ?)
    `,
      [getBotEventDto.uuid, user.id, getBotEventDto.uuid, user.id],
    );
    if (uuid.length == 0) {
      throw new BadRequestException('Bot not found');
    }
    let botEvent = await this.em
      .createQueryBuilder(BotEvent, 'be')
      .where('be.uuid = :uuid', { uuid: getBotEventDto.uuid })
      .orderBy('be.created_at', getBotEventDto.order)
      .getMany();
    let itemCount = botEvent.length;
    botEvent = PaginateByObject(
      botEvent,
      getBotEventDto.page,
      getBotEventDto.take,
    );
    const pageMetaDto = new PageMetaDto({
      itemCount,
      pageOptionsDto: getBotEventDto,
    });
    return new PageDto(botEvent, pageMetaDto);
  }
}
