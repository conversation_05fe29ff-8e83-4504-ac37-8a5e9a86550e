import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { BotEventService } from './bot-event.service';
import { CreateBotEventDto } from './dto/create-bot-event.dto';
import { UpdateBotEventDto } from './dto/update-bot-event.dto';
import { GetBotEventDto } from './dto/get-bot-event.dto';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { ApiOperation } from '@nestjs/swagger';

@Controller('bot-event')
export class BotEventController {
  constructor(private readonly botEventService: BotEventService) {}

  @Get('/uuid')
  @ApiOperation({
    summary: 'Find all bot events by uuid',
  })
  @UseGuards(JwtAuthGuard)
  async findOne(@Query() getBotEventDto: GetBotEventDto, @Req() req: any) {
    return await this.botEventService.findOne(req.user, getBotEventDto);
  }
}
