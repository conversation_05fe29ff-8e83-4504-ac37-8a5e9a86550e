import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumberString, IsString } from 'class-validator';

export enum OrderSide {
  BUY = 'BUY',
  SELL = 'SELL',
}

export enum OrderType {
  MARKET = 'MARKET',
  LIMIT = 'LIMIT',
}

export class CreatePlatformOrderDto {
  @ApiProperty({ description: 'User Key Id' })
  @IsNotEmpty()
  @IsString()
  user_key_id: string;

  @ApiProperty({ description: 'Platform Id' })
  @IsNotEmpty()
  @IsString()
  platform_id: string;

  @ApiProperty({ description: 'Pair Id' })
  @IsNotEmpty()
  @IsString()
  pair_id: string;

  @ApiProperty({ description: 'Side of the order (BUY/SELL)' })
  @IsNotEmpty()
  @IsEnum(OrderSide)
  side: OrderSide;

  @ApiProperty({ description: 'Type of the order (MARKET/LIMIT)' })
  @IsNotEmpty()
  @IsEnum(OrderType)
  type: OrderType;

  @ApiProperty({ description: 'Quantity of the order' })
  @IsNotEmpty()
  @IsNumberString()
  quantity: string;

  @ApiProperty({ description: 'Price of the order' })
  @IsNotEmpty()
  @IsNumberString()
  price: string;
}
