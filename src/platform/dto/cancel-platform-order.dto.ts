import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumberString, IsString } from 'class-validator';

export enum OrderSide {
  BUY = 'BUY',
  SELL = 'SELL',
}

export enum OrderType {
  MARKET = 'MARKET',
  LIMIT = 'LIMIT',
}

export class CancelPlatformOrderDto {
  @ApiProperty({ description: 'User Key Id' })
  @IsNotEmpty()
  @IsString()
  user_key_id: string;

  @ApiProperty({ description: 'Order Id' })
  @IsNotEmpty()
  @IsString()
  order_id: string;
}
