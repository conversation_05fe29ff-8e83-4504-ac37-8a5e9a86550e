import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumberString, IsString } from 'class-validator';
import { PageOptionsDto } from 'common/dto/pagination.dto';

export enum OrderSide {
  BUY = 'BUY',
  SELL = 'SELL',
}

export enum OrderType {
  MARKET = 'MARKET',
  LIMIT = 'LIMIT',
}

export class GetOrderDto extends PageOptionsDto {
  @ApiProperty({ description: 'User Key Id' })
  @IsNotEmpty()
  @IsString()
  user_key_id: string;

  @ApiProperty({ description: 'Pair Id' })
  @IsNotEmpty()
  @IsString()
  pair_id: string;
}
