import { BadRequestException, Injectable } from '@nestjs/common';
import { CreatePlatformDto } from './dto/create-platform.dto';
import { UpdatePlatformDto } from './dto/update-platform.dto';
import { Platform } from './entities/platform.entity';
import { EntityManager } from 'typeorm';
import {
  generateSecret,
  truncateToDecimals,
  uploadAndDownloadFile,
} from 'common/util/utils';
import { QueryPlatformDto } from './dto/query-platform.dto';
import { UserKey, UserKeyStatus } from 'src/user_key/entities/user_key.entity';
import { CreatePlatformOrderDto } from './dto/create-platform-order.dto';
import { User } from 'src/user/entities/user.entity';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import { Pair } from 'src/pair/entities/pair.entity';
import Decimal from 'decimal.js';
import {
  Transaction,
  TransactionStatus,
} from 'src/transactions/entities/transaction.entity';
import { GetOrderDto } from './dto/get-order.dto';
import { PaginateByObject } from 'common/dto/pagination.dto';
import { PageDto, PageMetaDto } from 'common/dto/pageResponse.dto';
import { CancelPlatformOrderDto } from './dto/cancel-platform-order.dto';

@Injectable()
export class PlatformService {
  constructor(private em: EntityManager) {}
  async create(createPlatformDto: CreatePlatformDto, file: any) {
    const { name } = createPlatformDto;
    const existingPlatform = await this.em
      .createQueryBuilder(Platform, 'platform')
      .where('platform.name = :name', { name })
      .getOne();

    if (existingPlatform) {
      throw new BadRequestException(
        `Platform with name '${name}' already exists.`,
      );
    }

    let url = await uploadAndDownloadFile(file, 'platform_' + Date.now());
    const platform = this.em.create(Platform, {
      ...createPlatformDto,
      image: url,
    }); // Create a new platform entity
    await this.em.save(platform); // Save it to the database
    return `Platform '${platform.name}' created successfully!`;
  }

  async findAll(query: QueryPlatformDto) {
    const queryBuilder = this.em
      .createQueryBuilder(Platform, 'p')
      .where('p.status = 1');

    if (query.id) {
      queryBuilder.andWhere('p.id = :id', { id: query.id });
    }

    if (query.name) {
      queryBuilder.andWhere('p.name = :name', { name: query.name });
    }

    let platforms = queryBuilder.getMany();
    return platforms;
  }

  async findOne(id: number) {
    const existingPlatform = await this.em
      .createQueryBuilder(Platform, 'p')
      .where('p.id = :id', { id })
      .getOne();
    if (!existingPlatform) {
      throw new BadRequestException(`Platform with ID ${id} not found`);
    }
    return existingPlatform;
  }

  async update(dto: UpdatePlatformDto) {
    const { name } = dto;
    const platform = await this.em
      .createQueryBuilder(Platform, 'p')
      .where('p.id = :id', { id: dto.id })
      .getOne();
    if (!platform) {
      throw new BadRequestException(`Platform with ID ${dto.id} not found`);
    }
    platform.name = name;
    try {
      await this.em.save(Platform, platform);
      return 'Platform name changed successfully';
    } catch (error) {
      throw new BadRequestException(
        `Platform with name '${name}' already exists.`,
      );
    }
  }

  async placeOrder(user: User, createPlatformOrderDto: CreatePlatformOrderDto) {
    let [user_key, pair] = await Promise.all([
      this.em
        .createQueryBuilder(UserKey, 'uk')
        .leftJoin('platform', 'p', 'uk.platform_id = p.id')
        .where(
          'uk.id = :id and uk.status = :status and uk.user_id = :user_id and p.id = :platform_id',
          {
            id: createPlatformOrderDto.user_key_id,
            status: UserKeyStatus.ACTIVE,
            user_id: user.id,
            platform_id: createPlatformOrderDto.platform_id,
          },
        )
        .select([
          'uk.id as user_key_id',
          'uk.api_key as api_key',
          'uk.api_secret as api_secret',
          'uk.taker_fee as taker_fee',
          'uk.maker_fee as maker_fee',
          'p.name as platform_name',
          'p.id as platform_id',
        ])
        .getRawOne(),
      this.em
        .createQueryBuilder(Pair, 'p')
        .where('p.id = :id and p.platform_id = :platform_id', {
          id: createPlatformOrderDto.pair_id,
          platform_id: createPlatformOrderDto.platform_id,
        })
        .getOne(),
    ]);
    
    if (!user_key) {
      throw new BadRequestException('Invalid user key');
    } else if (!pair) {
      throw new BadRequestException('Invalid pair');
    }
    
    let platformHelper = await callPlatformHelper(
      user_key.platform_name.toLowerCase(),
    );
    
    await platformHelper.init(
      user_key.platform_name.toLowerCase(),
      user_key.api_key,
      user_key.api_secret,
    );
    
    let order_detail: any = {
      symbol: pair.symbol,
      side: createPlatformOrderDto.side,
      type: createPlatformOrderDto.type,
      price: createPlatformOrderDto.price,
    };
    
    // Handle different platforms with switch case
    switch (user_key.platform_name.toLowerCase()) {
      case 'luno':
        // Luno-specific order handling
        if (createPlatformOrderDto.type === 'MARKET') {
          if (createPlatformOrderDto.side === 'BUY') {
            // For market buy orders, Luno uses counter_volume (amount in quote currency)
            // The quantity represents the amount in quote currency (e.g., USDT)
            order_detail['quoteOrderQty'] = truncateToDecimals(
              new Decimal(createPlatformOrderDto.quantity),
              pair.price_dp, // Use price_dp for quote currency precision
            );
          } else {
            // For market sell orders, Luno uses base_volume (amount in base currency)
            // The quantity represents the amount in base currency (e.g., BTC)
            order_detail['quantity'] = truncateToDecimals(
              new Decimal(createPlatformOrderDto.quantity),
              pair.qty_dp, // Use qty_dp for base currency precision
            );
          }
        } else {
          // For limit orders, Luno uses standard quantity and price
          order_detail['quantity'] = truncateToDecimals(
            new Decimal(createPlatformOrderDto.quantity),
            pair.qty_dp,
          );
        }
        
        // // Add post_only support for Luno if needed
        // if (createPlatformOrderDto.post_only) {
        //   order_detail['post_only'] = createPlatformOrderDto.post_only;
        // }
        
        // // Add timeInForce support for Luno if needed
        // if (createPlatformOrderDto.timeInForce) {
        //   order_detail['timeInForce'] = createPlatformOrderDto.timeInForce;
        // }
        break;
        
      case 'binance':
        // Binance-specific order handling (existing logic)
        if (createPlatformOrderDto.type === 'MARKET') {
          order_detail['quantity'] = createPlatformOrderDto.quantity;
          order_detail['quoteOrderQty'] = createPlatformOrderDto.quantity;
        } else {
          order_detail['quantity'] = truncateToDecimals(
            new Decimal(createPlatformOrderDto.quantity),
            pair.qty_dp,
          );
        }
        break;
        
      default:
        // Default handling for other platforms (existing logic)
        if (createPlatformOrderDto.type === 'MARKET') {
          order_detail['quantity'] = createPlatformOrderDto.quantity;
          order_detail['quote_qty'] =
            createPlatformOrderDto.side === 'BUY'
              ? truncateToDecimals(
                  new Decimal(createPlatformOrderDto.quantity).mul(pair.price),
                  pair.price_dp,
                )
              : 0;
          order_detail['quantity'] =
            createPlatformOrderDto.side === 'SELL'
              ? truncateToDecimals(
                  new Decimal(createPlatformOrderDto.quantity),
                  pair.qty_dp,
                )
              : 0;
        } else {
          order_detail['quantity'] = truncateToDecimals(
            new Decimal(createPlatformOrderDto.quantity),
            pair.qty_dp,
          );
        }
        break;
    }
    
    let order = await platformHelper.createOrder(order_detail);
    
    let record = new Transaction();
    record.uuid = (await generateSecret()) + Date.now();
    record.order_id = order.id;
    record.fee = new Decimal(order.price)
      .mul(new Decimal(createPlatformOrderDto.quantity))
      .mul(new Decimal(user_key.taker_fee))
      .toString();
    record.amount = createPlatformOrderDto.quantity;
    record.bot_type = 'MANUAL';
    record.is_buy = createPlatformOrderDto.side === 'BUY' ? true : false;
    record.platform_id = user_key.platform_id;
    record.price = order.price;
    record.prv_amount = '0';
    record.status = TransactionStatus.NEW;
    record.target_pair_id = pair.id.toString();
    record.user_key_id = user_key.user_key_id;

    await this.em.save(Transaction, record);

    return order;
  }

  async getOrder(user: User, getOrderDto: GetOrderDto) {
    let queryBuilder = this.em
      .createQueryBuilder(Transaction, 't')
      .leftJoin('user_key', 'user_key', 't.user_key_id = user_key.id')
      .where(
        `user_key.user_id = :user_id and t.bot_type = 'MANUAL' and t.status = 'NEW' and t.target_pair_id = :pair_id and t.user_key_id = :user_key_id`,
        {
          user_id: user.id,
          pair_id: getOrderDto.pair_id,
          user_key_id: getOrderDto.user_key_id,
        },
      )
      .andWhere('t.status = :status', { status: TransactionStatus.NEW });

    let res = await queryBuilder
      .select([
        't.id as id',
        't.price as price',
        't.amount as amount',
        't.exc_amount as exc_amount',
        't.is_buy as is_buy',
        't.order_id as order_id',
        `t.created_at as created_at`,
      ])
      .orderBy('t.created_at', 'DESC')
      .getRawMany();
    let itemCount = res.length;
    if (getOrderDto.take == 0) {
      getOrderDto.take = itemCount;
    }
    res = PaginateByObject(res, getOrderDto.page, getOrderDto.take);
    const pageMetaDto = new PageMetaDto({
      itemCount,
      pageOptionsDto: getOrderDto,
    });
    return new PageDto(res, pageMetaDto);
  }

  async cancelOrder(
    user: User,
    cancelPlatformOrderDto: CancelPlatformOrderDto,
  ) {
    let transaction = await this.em
      .createQueryBuilder(Transaction, 't')
      .leftJoin('user_key', 'user_key', 't.user_key_id = user_key.id')
      .leftJoin('pair', 'pair', 't.target_pair_id = pair.id')
      .leftJoin('platform', 'platform', 'platform.id = t.platform_id')
      .where(
        `user_key.user_id = :user_id and t.order_id = :id and t.status = 'NEW' and t.bot_type = 'MANUAL' and t.user_key_id = :user_key_id`,
        {
          user_id: user.id,
          id: cancelPlatformOrderDto.order_id,
          user_key_id: cancelPlatformOrderDto.user_key_id,
        },
      )
      .select(['t.order_id as order_id'])
      .addSelect(
        `JSON_OBJECT(
        'name', platform.name
      )`,
        'platform',
      )
      .addSelect(
        `JSON_OBJECT(
        'symbol', pair.symbol
      )`,
        'pair',
      )
      .addSelect(
        `JSON_OBJECT(
        'id', user_key.id,
        'api_key', user_key.api_key,
        'api_secret', user_key.api_secret
      )`,
        'user_key',
      )
      .getRawOne();
    console.log(transaction);
    if (!transaction) {
      throw new BadRequestException('Invalid order id');
    }
    let callHelper = await callPlatformHelper(
      transaction.platform.name.toLowerCase(),
    );
    await callHelper.init(
      transaction.platform.name.toLowerCase(),
      transaction.user_key.api_key,
      transaction.user_key.api_secret,
    );
    let cancel_order = await callHelper.cancelOrder(
      transaction.order_id,
      transaction.pair.symbol,
    );
    return transaction;
  }

  async getActiveOrder(
    createPlatformOrderDto: CreatePlatformOrderDto,
    user: User,
  ) {
    let user_key = await this.em
      .createQueryBuilder(UserKey, 'uk')
      .leftJoin('platform', 'p', 'uk.platform_id = p.id')
      .where(
        'uk.id = :id and uk.status = :status and uk.user_id = :user_id and p.id = :platform_id',
        {
          id: createPlatformOrderDto.user_key_id,
          status: UserKeyStatus.ACTIVE,
          user_id: user.id,
          platform_id: createPlatformOrderDto.platform_id,
        },
      )
      .select([
        'uk.id as user_key_id',
        'uk.api_key as api_key',
        'uk.api_secret as api_secret',
        'uk.taker_fee as taker_fee',
        'uk.maker_fee as maker_fee',
        'p.name as platform_name',
        'p.id as platform_id',
      ])
      .getRawOne();
    let platformHelper = await callPlatformHelper(
      user_key.platform_name.toLowerCase(),
    );
    await platformHelper.init(
      user_key.platform_name.toLowerCase(),
      user_key.api_key,
      user_key.api_secret,
    );
    let active_order = await platformHelper.getAllActiveOrders();
    return active_order;
  }
}
