import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  UploadedFile,
  UseInterceptors,
  Query,
  Req,
} from '@nestjs/common';
import { PlatformService } from './platform.service';
import { CreatePlatformDto } from './dto/create-platform.dto';
import { UpdatePlatformDto } from './dto/update-platform.dto';
import { ApiBody, ApiConsumes, ApiOperation } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { FileInterceptor } from '@nestjs/platform-express';
import { DeletePlatformDto } from './dto/delete-user_key.dto';
import { QueryPlatformDto } from './dto/query-platform.dto';
import { CreatePlatformOrderDto } from './dto/create-platform-order.dto';
import { GetOrderDto } from './dto/get-order.dto';
import { CancelPlatformOrderDto } from './dto/cancel-platform-order.dto';

@Controller('platform')
export class PlatformController {
  constructor(private readonly platformService: PlatformService) {}

  @Post('create')
  @ApiOperation({
    summary: 'admin create new platform',
  })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  @ApiBody({
    description: 'File upload',
    required: true,
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string' },
        file: {
          type: 'string',
          format: 'binary',
          nullable: false,
        },
      },
    },
  })
  // @UseGuards(JwtAuthGuard)
  async create(
    @Body() createPlatformDto: CreatePlatformDto,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return await this.platformService.create(createPlatformDto, file);
  }

  @Get()
  @ApiOperation({
    summary: 'find all platform',
  })
  async findAll(@Query() query: QueryPlatformDto) {
    return await this.platformService.findAll(query);
  }

  @Post('/update')
  @ApiOperation({
    summary: 'edit platform by id',
  })
  @UseGuards(JwtAuthGuard)
  async update(@Body() updatePlatformDto: UpdatePlatformDto) {
    return await this.platformService.update(updatePlatformDto);
  }

  @Post('/place-order')
  @ApiOperation({
    summary: 'place order',
  })
  @UseGuards(JwtAuthGuard)
  async placeOrder(
    @Body() createPlatformOrderDto: CreatePlatformOrderDto,
    @Req() req,
  ) {
    return await this.platformService.placeOrder(
      req.user,
      createPlatformOrderDto,
    );
  }

  @Get('/order-list')
  @ApiOperation({
    summary: 'get manual order list',
  })
  @UseGuards(JwtAuthGuard)
  async orderList(@Query() getOrderDto: GetOrderDto, @Req() req) {
    return await this.platformService.getOrder(req.user, getOrderDto);
  }

  @Post('/cancel-order')
  @ApiOperation({
    summary: 'cancel order',
  })
  @UseGuards(JwtAuthGuard)
  async cancelOrder(@Body() body: CancelPlatformOrderDto, @Req() req) {
    return await this.platformService.cancelOrder(req.user, body);
  }

  @Post('test')
  @ApiOperation({
    summary: 'test',
  })
  @UseGuards(JwtAuthGuard)
  async test(@Body() body: CreatePlatformOrderDto, @Req() req) {
    return await this.platformService.getActiveOrder(body, req.user);
  }
}
