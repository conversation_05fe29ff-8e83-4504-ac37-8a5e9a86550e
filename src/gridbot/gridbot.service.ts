import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateGridbotDto } from './dto/create-gridbot.dto';
import { UpdateGridbotDto } from './dto/update-gridbot.dto';
import { EntityManager } from 'typeorm';
import { Gridbot, GridBotStatus } from './entities/gridbot.entity';
import { UserKeyStatus } from 'src/user_key/entities/user_key.entity';

import { Pair, Pairstatus } from 'src/pair/entities/pair.entity';
import {
  calculateConstBaseAndQuote,
  decrypted,
  generateSecret,
  validateNotional,
  validateStep,
} from 'common/util/utils';
import { UserKey } from 'src/user_key/entities/user_key.entity';
import { User } from 'src/user/entities/user.entity';
import Decimal from 'decimal.js';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import { QueryGridbotDto } from './dto/query-gridbot.dto';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { StartGridbotDto } from './dto/start-gridbot.dto';
import { StopGridbotDto } from './dto/stop-gridbot.dto';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import configuration from 'config/configuration';
import { Platform } from 'src/platform/entities/platform.entity';
import { CopyGridbotDto } from './dto/copy-gridbot.dto';
import { Transaction } from 'src/transactions/entities/transaction.entity';
import { DeleteGridbotDto } from './dto/delete-gridbot.dto';
import { UserKeyValidatorHelper } from 'common/helper/user-key-validator.helper';
import { PaginateByObject } from 'common/dto/pagination.dto';
import { PageDto, PageMetaDto } from 'common/dto/pageResponse.dto';
import { Cron, CronExpression } from '@nestjs/schedule';
import { CreateBotEventQueueService } from 'src/queue/create-bot-event-queue.service';

@Injectable()
export class GridbotService {
  constructor(
    private readonly em: EntityManager,
    @InjectQueue('start-grid')
    private startGridQueue: Queue,
    @InjectQueue('stop-grid')
    private stopGridQueue: Queue,
    @InjectQueue('realize-checking')
    private realizeCheckingQueue: Queue,
    @InjectQueue('grid-checking')
    private gridCheckingQueue: Queue,
    @InjectQueue('update-order')
    private updateOrderQueue: Queue,
    @InjectQueue('create-event')
    private createEventQueue: Queue, // Inject the create-event queue
  ) {}

  async create(user: User, createGridbotDto: CreateGridbotDto) {
    let [userKey, bot, pair, platform] = await Promise.all([
      this.em
        .createQueryBuilder(UserKey, 'uk')
        .where(
          'uk.id = :id and uk.user_id = :user_id and uk.platform_id = :platform_id',
          {
            id: createGridbotDto.user_key_id,
            user_id: user.id,
            platform_id: createGridbotDto.platform_id,
          },
        )
        .getOne(),
      this.em
        .createQueryBuilder(Gridbot, 'gb')
        .where('gb.name = :name', { name: createGridbotDto.name })
        .getOne(),
      this.em
        .createQueryBuilder(Pair, 'p')
        .where('p.id = :id and p.platform_id = :platform_id', {
          id: createGridbotDto.base_pair_id,
          platform_id: createGridbotDto.platform_id,
        })
        .getOne(),
      this.em
        .createQueryBuilder(Platform, 'p')
        .where('p.id = :id', { id: createGridbotDto.platform_id })
        .getOne(),
    ]);

    if (!pair) {
      throw new BadRequestException(CustomErrorMessages.PAIR.PAIR_NOT_FOUND);
    } else if (!userKey) {
      throw new BadRequestException(
        CustomErrorMessages.USER_KEY.INVALID_USERKEY,
      );
    }
    await new UserKeyValidatorHelper(this.em).validateUserKey(
      userKey.id,
      'create grid bot',
    );

    await validateNotional(
      createGridbotDto.initial_fund,
      createGridbotDto.step,
      pair.min_notional,
    );
    bot = new Gridbot();
    bot.base_pair_id = pair.id.toString();
    bot.platform_id = pair.platform_id.toString();
    bot.user_key_id = userKey.id.toString();
    bot.name = createGridbotDto.name;
    bot.upper_threshold = createGridbotDto.upper_threshold;
    bot.lower_threshold = createGridbotDto.lower_threshold;
    bot.fee_pct = createGridbotDto.fee_pct;
    bot.step = createGridbotDto.step;
    bot.initial_fund = createGridbotDto.initial_fund;
    bot.sell_at_stop = createGridbotDto.sell_at_stop == 'true' ? true : false;

    bot.uuid = (await generateSecret()) + Date.now();
    try {
      let platformHelper = await callPlatformHelper(
        platform.name.toLowerCase(),
      );
      await platformHelper.init(
        platform.name.toLowerCase() as any,
        userKey.api_key,
        userKey.api_secret,
      );
      let ticker_price = await platformHelper.getTickerBySymbol(pair.symbol);
      let { buy_orders, sell_orders } = await calculateConstBaseAndQuote(
        bot,
        pair,
        ticker_price.close,
      );
      bot.qty_per_step =
        buy_orders.length != 0
          ? buy_orders[0].amount.toString()
          : sell_orders[0].amount.toString();
      await this.em.save(Gridbot, bot);

      // Add this block to create a "BOT_CREATED" event
      await this.createEventQueue.add(
        'processing',
        {
          botType: 'gridbot',
          uuid: bot.uuid,
          eventType: CreateBotEventQueueService.EVENT_TYPES.BOT_CREATED,
          params: [],
        },
        { removeOnComplete: true },
      );

      return bot;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.message);
    }
  }

  async copy(user: User, copyGridbotDto: CopyGridbotDto) {
    let [bot] = await Promise.all([
      this.em
        .createQueryBuilder(Gridbot, 'gb')
        .where('gb.uuid = :uuid', { uuid: copyGridbotDto.uuid })
        .withDeleted()
        .getOne(),
    ]);
    let [userKey, pair, platform] = await Promise.all([
      this.em
        .createQueryBuilder(UserKey, 'uk')
        .where(
          'uk.id = :id and uk.user_id = :user_id and uk.platform_id = :platform_id',
          {
            id: bot.user_key_id,
            user_id: user.id,
            platform_id: bot.platform_id,
          },
        )
        .getOne(),
      this.em
        .createQueryBuilder(Pair, 'p')
        .where('p.id = :id and p.platform_id = :platform_id', {
          id: bot.base_pair_id,
          platform_id: bot.platform_id,
        })
        .getOne(),
      this.em
        .createQueryBuilder(Platform, 'p')
        .where('p.id = :id', { id: bot.platform_id })
        .getOne(),
    ]);
    if (!userKey) {
      throw new BadRequestException(CustomErrorMessages.BOT.INVALID_UUID);
    }

    await new UserKeyValidatorHelper(this.em).validateUserKey(
      userKey.id,
      'copy grid bot',
    );

    let new_bot = new Gridbot();
    new_bot.base_pair_id = bot.base_pair_id;
    new_bot.platform_id = bot.platform_id;
    new_bot.user_key_id = bot.user_key_id;
    new_bot.name = copyGridbotDto.name;
    new_bot.upper_threshold = bot.upper_threshold;
    new_bot.lower_threshold = bot.lower_threshold;
    new_bot.step = bot.step;
    new_bot.initial_fund = bot.initial_fund;
    new_bot.sell_at_stop = bot.sell_at_stop;
    new_bot.uuid = (await generateSecret()) + Date.now();
    let platformHelper = await callPlatformHelper(platform.name.toLowerCase());
    await platformHelper.init(
      platform.name.toLowerCase() as any,
      userKey.api_key,
      userKey.api_secret,
    );
    let ticker_price = await platformHelper.getTickerBySymbol(pair.symbol);
    await calculateConstBaseAndQuote(new_bot, pair, ticker_price.close);
    await this.em.save(Gridbot, new_bot);
    return new_bot;
  }

  async findAll(user: User, query: QueryGridbotDto) {
    let queryBuilder = this.em
      .createQueryBuilder(Gridbot, 'gb')
      .leftJoin('user_key', 'user_key', 'gb.user_key_id = user_key.id')
      .leftJoin('user', 'user', 'user_key.user_id = user.id')
      .leftJoin('platform', 'platform', 'gb.platform_id = platform.id')
      .leftJoin('pair', 'pair', 'gb.base_pair_id = pair.id ')

      .where('user.id = :user_id', { user_id: user.id });

    if (query.platform_id) {
      queryBuilder.andWhere('gb.platform_id = :platform_id', {
        platform_id: query.platform_id,
      });
    }
    if (query.uuid) {
      queryBuilder.andWhere('gb.uuid = :uuid', {
        uuid: query.uuid,
      });
    }
    if (query.symbol) {
      queryBuilder.andWhere('pair.symbol = :symbol', {
        symbol: query.symbol,
      });
    }
    if (query.user_key_id) {
      queryBuilder.andWhere('gb.user_key_id = :user_key_id', {
        user_key_id: query.user_key_id,
      });
    }
    if (query.status) {
      queryBuilder.andWhere('gb.status = :status', { status: query.status });
    }
    let gridBot = await queryBuilder
      .select([
        'gb.id as id',
        'gb.name as name',
        'gb.uuid as uuid',
        'gb.realized_profit as realized_profit',
        'gb.initial_base as initial_base',
        'gb.initial_quote as initial_quote',
        'gb.initial_price as initial_price',
        'gb.qty_per_step as qty_per_step',
        'gb.user_key_id as user_key_id',
        'gb.lower_threshold as lower_threshold',
        'gb.upper_threshold as upper_threshold',
        'gb.step as step',
        'gb.initial_fund as initial_fund',
        'gb.current_base as current_base',
        'gb.current_quote as current_quote',
        'gb.sell_at_stop as sell_at_stop',
        'gb.status as status',
        `gb.created_at as created_at`,
        `gb.updated_at as updated_at`,
        `gb.deleted_at as deleted_at`,
      ])
      .addSelect(
        `JSON_OBJECT(
      'id', user_key.id,
      'name', user_key.name,
      'api_key', user_key.api_key,
      'status', user_key.status,
      'maker_fee', user_key.maker_fee
    )`,
        'user_key',
      )
      .addSelect(
        `JSON_OBJECT(
      'id', platform.id,
      'name', platform.name,
      'image', platform.image
    )`,
        'platform',
      )
      .addSelect(
        `JSON_OBJECT(
      'id', pair.id,
      'symbol', pair.symbol,
      'price', pair.price
    )`,
        'pair',
      )
      .orderBy('gb.updated_at', query.order)
      .withDeleted()
      .getRawMany();
    let itemCount = gridBot.length;
    gridBot = PaginateByObject(gridBot, query.page, query.take);
    const pageMetaDto = new PageMetaDto({
      itemCount,
      pageOptionsDto: query,
    });
    return new PageDto(gridBot, pageMetaDto);
  }

  async start(user: User, body: StartGridbotDto) {
    //check if have existing user have active same pair.
    let queryBuilder = this.em
      .createQueryBuilder(Gridbot, 'gb')
      .leftJoin('user_key', 'user_key', 'gb.user_key_id = user_key.id')
      .leftJoin('user', 'user', 'user_key.user_id = user.id')
      .leftJoin('platform', 'platform', 'gb.platform_id = platform.id')
      .leftJoin('pair', 'pair', 'gb.base_pair_id = pair.id ')
      .where(
        "user.id = :user_id and gb.uuid = :uuid and gb.status = 'INACTIVE'",
        {
          user_id: user.id,
          uuid: body.uuid,
        },
      );

    let gridBot = await queryBuilder
      .select([
        'gb.id as id',
        'gb.name as name',
        'gb.uuid as uuid',
        'gb.lower_threshold as lower_threshold',
        'gb.upper_threshold as upper_threshold',
        'gb.step as step',
        'gb.initial_fund as initial_fund',
        'gb.sell_at_stop as sell_at_stop',
        'gb.status as status',
        'gb.fee_pct as fee_pct',
      ])
      .addSelect(
        `JSON_OBJECT(
      'id', platform.id,
      'name', platform.name
    )`,
        'platform',
      )
      .addSelect(
        `JSON_OBJECT(
      'id', user_key.id,
      'api_key', user_key.api_key,
      'api_secret', user_key.api_secret,
      'maker_fee',user_key.maker_fee
    )`,
        'user_key',
      )
      .addSelect(
        `JSON_OBJECT(
      'id', pair.id,
      'symbol', pair.symbol,
      'price', pair.price,
      'min_notional', pair.min_notional,
      'price_dp', pair.price_dp,
      'qty_dp', pair.qty_dp,
      'base', pair.base,
      'quote', pair.quote,
      'tick', CAST(pair.tick AS CHAR),
      'step', CAST(pair.step AS CHAR),
      'symbol', pair.symbol
    )`,
        'pair',
      )
      .getRawOne();
    if (!gridBot) {
      throw new BadRequestException(CustomErrorMessages.BOT.INVALID_STATUS);
    } else if (
      Decimal(gridBot.pair.price)
        .minus(gridBot.upper_threshold)
        .abs()
        .div(gridBot.pair.price)
        .gt(0.3) ||
      Decimal(gridBot.pair.price)
        .minus(gridBot.lower_threshold)
        .abs()
        .div(gridBot.pair.price)
        .gt(0.3)
    ) {
      throw new BadRequestException(
        'Price threshold is too far from market price',
      );
    }

    await new UserKeyValidatorHelper(this.em).validateUserKey(
      gridBot.user_key.id,
      'start grid bot',
    );

    let platformHelper = await callPlatformHelper(
      gridBot.platform.name.toLowerCase(),
    );
    await platformHelper.init(
      gridBot.platform.name.toLowerCase(),
      gridBot.user_key.api_key,
      gridBot.user_key.api_secret,
    );
    let ticker_price = await platformHelper.getTickerBySymbol(
      gridBot.pair.symbol,
    );
    let {
      base_required,
      quote_required,
      sell_orders,
      buy_orders,
      buy_count,
      sell_count,
    } = await calculateConstBaseAndQuote(
      gridBot,
      gridBot.pair,
      ticker_price.close,
    );
    let balance = await platformHelper.readBalance();
    let baseBalance = balance.find((item) => item.symbol === gridBot.pair.base);
    let quoteBalance = balance.find(
      (item) => item.symbol === gridBot.pair.quote,
    );
    baseBalance = new Decimal(baseBalance.avai_balance);
    quoteBalance = new Decimal(quoteBalance.avai_balance);
    if (
      quoteBalance.lessThan(quote_required) ||
      baseBalance.lessThan(base_required)
    ) {
      throw new BadRequestException(
        CustomErrorMessages.BOT.INSUFFICIENT_FUNDS +
          ': \n' +
          gridBot.pair.quote +
          ' ' +
          quote_required +
          ' is required \n' +
          gridBot.pair.base +
          ' ' +
          base_required +
          ' is required',
      );
    }
    await this.startGridQueue.add('init-grid', {
      ...gridBot,
      balance,
      ticker_price,
    });
    return gridBot;
  }

  async stop(user: User, body: StopGridbotDto) {
    let queryBuilder = this.em
      .createQueryBuilder(Gridbot, 'gb')
      .leftJoin('user_key', 'user_key', 'gb.user_key_id = user_key.id')
      .leftJoin('user', 'user', 'user_key.user_id = user.id')
      .leftJoin('platform', 'platform', 'gb.platform_id = platform.id')
      .leftJoin('pair', 'pair', 'gb.base_pair_id = pair.id ')
      .where(
        "user.id = :user_id and gb.uuid = :uuid and gb.status = 'ACTIVE'",
        {
          user_id: user.id,
          uuid: body.uuid,
        },
      );

    let gridBot = await queryBuilder
      .select([
        'gb.id as id',
        'gb.name as name',
        'gb.uuid as uuid',
        'gb.lower_threshold as lower_threshold',
        'gb.upper_threshold as upper_threshold',
        'gb.step as step',
        'gb.initial_fund as initial_fund',
        'gb.sell_at_stop as sell_at_stop',
        'gb.status as status',
      ])
      .addSelect(
        `JSON_OBJECT(
      'id', platform.id,
      'name', platform.name
    )`,
        'platform',
      )
      .addSelect(
        `JSON_OBJECT(
      'id', user_key.id,
      'api_key', user_key.api_key,
      'api_secret', user_key.api_secret
    )`,
        'user_key',
      )
      .addSelect(
        `JSON_OBJECT(
      'id', pair.id,
      'symbol', pair.symbol,
      'min_notional', pair.min_notional,
      'price_dp', pair.price_dp,
      'qty_dp', pair.qty_dp,
      'base', pair.base,
      'quote', pair.quote,
      'tick', CAST(pair.tick AS CHAR),
      'step', CAST(pair.step AS CHAR),
      'symbol', pair.symbol
    )`,
        'pair',
      )
      .getRawOne();
    if (!gridBot) {
      throw new BadRequestException(CustomErrorMessages.BOT.INVALID_STATUS);
    }
    await new UserKeyValidatorHelper(this.em).validateUserKey(
      gridBot.user_key.id,
      'stop grid bot',
    );

    return await this.stopGridQueue.add('end-grid', gridBot);
  }

  async remove(user: User, deleteGridbotDto: DeleteGridbotDto) {
    let gb = await this.em
      .createQueryBuilder(Gridbot, 'gb')
      .leftJoin('user_key', 'user_key', 'gb.user_key_id = user_key.id')
      .leftJoin('user', 'user', 'user_key.user_id = user.id')
      .leftJoin('platform', 'platform', 'gb.platform_id = platform.id')
      .leftJoin('pair', 'pair', 'gb.base_pair_id = pair.id ')
      .where(
        "user.id = :user_id and gb.uuid = :uuid and gb.status = 'INACTIVE'",
        {
          user_id: user.id,
          uuid: deleteGridbotDto.uuid,
        },
      )
      .getOne();
    if (!gb) {
      throw new BadRequestException(CustomErrorMessages.BOT.INVALID_STATUS);
    }
    await new UserKeyValidatorHelper(this.em).validateUserKey(
      gb.user_key_id,
      'delete grid bot',
    );

    let bot = await this.em
      .createQueryBuilder()
      .update(Gridbot)
      .set({
        status: GridBotStatus.DELETED,
        deleted_at: () => 'CURRENT_TIMESTAMP',
      })
      .where(`uuid = :uuid and status = 'INACTIVE'`, {
        uuid: deleteGridbotDto.uuid,
      })
      .execute();
    return `Delete  #${deleteGridbotDto.uuid} gridbot`;
  }

  async update(user: User, updateGridbotDto: UpdateGridbotDto) {
    console.log('===Start Update GridBot===');

    // Single query to get existing bot with all related entities and status validation
    let result = await this.em
      .createQueryBuilder(Gridbot, 'gb')
      .leftJoin('user_key', 'uk', 'gb.user_key_id = uk.id')
      .leftJoin('user', 'u', 'uk.user_id = u.id')
      .leftJoin('pair', 'p', 'gb.base_pair_id = p.id')
      .leftJoin('platform', 'pl', 'gb.platform_id = pl.id')
      .select([
        'gb.id as gb_id',
        'gb.name as gb_name',
        'gb.uuid as gb_uuid',
        'gb.upper_threshold as gb_upper_threshold',
        'gb.lower_threshold as gb_lower_threshold',
        'gb.step as gb_step',
        'gb.qty_per_step as gb_qty_per_step',
        'gb.fee_pct as gb_fee_pct',
        'gb.initial_fund as gb_initial_fund',
        'gb.initial_base as gb_initial_base',
        'gb.initial_quote as gb_initial_quote',
        'gb.initial_price as gb_initial_price',
        'gb.current_quote as gb_current_quote',
        'gb.current_base as gb_current_base',
        'gb.realized_profit as gb_realized_profit',
        'gb.sell_at_stop as gb_sell_at_stop',
        'gb.status as gb_status',
        'gb.user_key_id as gb_user_key_id',
        'gb.base_pair_id as gb_base_pair_id',
        'gb.platform_id as gb_platform_id',
        'gb.created_at as gb_created_at',
        'gb.updated_at as gb_updated_at',
        'gb.deleted_at as gb_deleted_at',

        'uk.id as uk_id',
        'uk.api_key as uk_api_key',
        'uk.api_secret as uk_api_secret',
        'uk.status as uk_status',

        'p.id as p_id',
        'p.symbol as p_symbol',
        'p.min_notional as p_min_notional',
        'p.base as p_base',
        'p.quote as p_quote',
        'p.price_dp as p_price_dp',
        'p.qty_dp as p_qty_dp',
        'p.status as p_status',

        'pl.id as pl_id',
        'pl.name as pl_name',
        'pl.status as pl_status',
      ])
      .where(
        'u.id = :user_id AND gb.uuid = :uuid AND gb.status = :botStatus AND uk.status = :ukStatus AND p.status = :pairStatus AND pl.status = :platformStatus',
        {
          botStatus: GridBotStatus.INACTIVE,
          user_id: user.id,
          uuid: updateGridbotDto.uuid,
          ukStatus: UserKeyStatus.ACTIVE,
          pairStatus: Pairstatus.ACTIVE,
          platformStatus: true, // Platform uses boolean for status
        },
      )
      .getRawOne();

    if (!result) {
      throw new BadRequestException(CustomErrorMessages.BOT.UNKNOWN_BOT);
    }

    // Create existingBot object from the raw result
    const existingBot = new Gridbot();
    existingBot.id = result.gb_id;
    existingBot.name = result.gb_name;
    existingBot.uuid = result.gb_uuid;
    existingBot.upper_threshold = result.gb_upper_threshold;
    existingBot.lower_threshold = result.gb_lower_threshold;
    existingBot.step = result.gb_step;
    existingBot.qty_per_step = result.gb_qty_per_step;
    existingBot.fee_pct = result.gb_fee_pct;
    existingBot.initial_fund = result.gb_initial_fund;
    existingBot.initial_base = result.gb_initial_base;
    existingBot.initial_quote = result.gb_initial_quote;
    existingBot.initial_price = result.gb_initial_price;
    existingBot.current_quote = result.gb_current_quote;
    existingBot.current_base = result.gb_current_base;
    existingBot.realized_profit = result.gb_realized_profit;
    existingBot.sell_at_stop = result.gb_sell_at_stop;
    existingBot.status = result.gb_status;
    existingBot.user_key_id = result.gb_user_key_id;
    existingBot.base_pair_id = result.gb_base_pair_id;
    existingBot.platform_id = result.gb_platform_id;
    existingBot.created_at = result.gb_created_at;
    existingBot.updated_at = result.gb_updated_at;
    existingBot.deleted_at = result.gb_deleted_at;

    // Extract related entities from the raw result
    const userKey = {
      id: result.uk_id,
      api_key: result.uk_api_key,
      api_secret: result.uk_api_secret,
      status: result.uk_status,
    };

    const pair = {
      id: result.p_id,
      symbol: result.p_symbol,
      min_notional: result.p_min_notional,
      base: result.p_base,
      quote: result.p_quote,
      price_dp: result.p_price_dp,
      qty_dp: result.p_qty_dp,
      status: result.p_status,
    };

    const platform = {
      id: result.pl_id,
      name: result.pl_name,
      status: result.pl_status,
    };

    // Validate user key
    await new UserKeyValidatorHelper(this.em).validateUserKey(
      userKey.id,
      'update grid bot',
    );

    // Validate notional
    await validateNotional(
      updateGridbotDto.initial_fund,
      updateGridbotDto.step,
      pair.min_notional,
    );

    // Update only the allowed fields - preserve base_pair_id, platform_id, user_key_id, and fee_pct
    existingBot.name = updateGridbotDto.name;
    existingBot.upper_threshold = updateGridbotDto.upper_threshold;
    existingBot.lower_threshold = updateGridbotDto.lower_threshold;
    // fee_pct is not updatable
    existingBot.step = updateGridbotDto.step;
    existingBot.initial_fund = updateGridbotDto.initial_fund;
    existingBot.sell_at_stop =
      updateGridbotDto.sell_at_stop == 'true' ? true : false;

    // Keep the original UUID, base_pair_id, platform_id, user_key_id, and fee_pct unchanged

    try {
      let platformHelper = await callPlatformHelper(
        platform.name.toLowerCase(),
      );
      await platformHelper.init(
        platform.name.toLowerCase() as any,
        userKey.api_key,
        userKey.api_secret,
      );
      let ticker_price = await platformHelper.getTickerBySymbol(pair.symbol);
      let { buy_orders, sell_orders } = await calculateConstBaseAndQuote(
        existingBot,
        pair,
        ticker_price.close,
      );
      existingBot.qty_per_step =
        buy_orders.length != 0
          ? buy_orders[0].amount.toString()
          : sell_orders[0].amount.toString();

      await this.em.save(Gridbot, existingBot);
      console.log('===Complete Update GridBot===');

      return existingBot;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.message);
    }
  }

  async patchOrder(body: any) {
    if (body.action === 'update') {
      await this.updateOrderQueue.add('processing', body);
      return 'ok';
    } else if (body.action === 'profit') {
      await this.realizeCheckingQueue.add('processing', body);
      return 'ok';
    } else if (body.action === 'grid_checking') {
      await this.gridCheckingQueue.add('check-criterea', body);
      return 'ok';
    }
  }

  @Cron('*/10 * * * * *', { name: 'grid-checking' })
  async checkGridBot() {
    //get pair id from transaction that is open status then group by and make a for loop

    let gridBots = await this.em
      .createQueryBuilder(Transaction, 'transaction')
      .select('transaction.target_pair_id as id')
      .where("transaction.status = 'OPEN'")
      .groupBy('transaction.target_pair_id')
      .getRawMany();

    for (const gridBot of gridBots) {
      console.log(gridBot);
      await this.gridCheckingQueue.add('check-criterea', gridBot);
    }
  }
}
