import { Modu<PERSON> } from '@nestjs/common';
import { GridbotService } from './gridbot.service';
import { GridbotController } from './gridbot.controller';
import { BullModule, BullModuleOptions } from '@nestjs/bull';
import configuration from 'config/configuration';

const bullConfig: BullModuleOptions = {
  redis: {
    host: configuration().redis.host,
    port: configuration().redis.port,
    password: configuration().redis.password,
  },
};

@Module({
  imports: [
    BullModule.forRoot(bullConfig),

    BullModule.registerQueue(
      { name: 'start-grid' },
      { name: 'stop-grid' },
      { name: 'place-order' },
      { name: 'update-order' },
      { name: 'realize-checking' },
      { name: 'grid-checking' },
      { name: 'create-event' },
    ),
  ],
  controllers: [GridbotController],
  providers: [GridbotService],
})
export class GridbotModule {}
