import { Test, TestingModule } from '@nestjs/testing';
import { GridbotController } from './gridbot.controller';
import { GridbotService } from './gridbot.service';

describe('GridbotController', () => {
  let controller: GridbotController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [GridbotController],
      providers: [GridbotService],
    }).compile();

    controller = module.get<GridbotController>(GridbotController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
