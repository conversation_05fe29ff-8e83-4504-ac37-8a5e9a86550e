import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsBooleanString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateGridbotDto {
  @ApiProperty({ description: 'name of the bot', required: true })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'lower price range of bot', required: true })
  @IsString()
  @IsNotEmpty()
  lower_threshold: string;

  @ApiProperty({ description: 'upper price range of bot', required: true })
  @IsString()
  @IsNotEmpty()
  upper_threshold: string;

  @ApiProperty({ description: 'total step for bot', required: true })
  @IsString()
  @IsNotEmpty()
  step: string;

  @ApiProperty({
    description: 'initial fund require to start the bot',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  initial_fund: string;

  @ApiProperty({
    description: 'whether to sell all coin when stop bot',
    required: false,
  })
  @IsBooleanString()
  sell_at_stop: string;

  @ApiProperty({ description: 'the pair id of running bot', required: true })
  @IsNotEmpty()
  @IsString()
  base_pair_id: string;

  @ApiProperty({
    description: 'the user key id of running bot',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  user_key_id: string;

  @ApiProperty({ description: 'platform id of running bot', required: true })
  @IsNotEmpty()
  @IsString()
  platform_id: string;

  @ApiProperty({
    description: 'fee that will be charge for this account in exchange',
    required: true,
  })
  @IsOptional()
  @IsString()
  fee_pct: string = '0';
}
