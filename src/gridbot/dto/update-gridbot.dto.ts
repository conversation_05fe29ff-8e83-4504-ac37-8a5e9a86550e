import { ApiProperty } from '@nestjs/swagger';
import { IsBooleanString, IsNotEmpty, IsString } from 'class-validator';

export class UpdateGridbotDto {
  @ApiProperty({ description: 'UUID of the bot', required: true })
  @IsNotEmpty()
  @IsString()
  uuid: string;

  @ApiProperty({ description: 'name of the bot', required: true })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'lower price range of bot', required: true })
  @IsString()
  @IsNotEmpty()
  lower_threshold: string;

  @ApiProperty({ description: 'upper price range of bot', required: true })
  @IsString()
  @IsNotEmpty()
  upper_threshold: string;

  @ApiProperty({ description: 'total step for bot', required: true })
  @IsString()
  @IsNotEmpty()
  step: string;

  @ApiProperty({
    description: 'initial fund require to start the bot',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  initial_fund: string;

  @ApiProperty({
    description: 'whether to sell all coin when stop bot',
    required: false,
  })
  @IsBooleanString()
  sell_at_stop: string;
}
