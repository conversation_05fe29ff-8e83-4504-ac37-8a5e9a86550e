import { ApiProperty } from '@nestjs/swagger';
import {
  IsBooleanString,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class TestGridSocketDto {
 

  @ApiProperty({ description: 'the pair id of running bot', required: true })
  @IsNotEmpty()
  @IsString()
  pair_id: string;


  @ApiProperty({ description: 'platform id of running bot', required: true })
  @IsNotEmpty()
  @IsString()
  platform_id: string;

}
