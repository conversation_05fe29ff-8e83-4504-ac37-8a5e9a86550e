import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum } from 'class-validator';
import { GridBotStatus } from '../entities/gridbot.entity';
import { PageOptionsDto } from 'common/dto/pagination.dto';

export class QueryGridbotDto extends PageOptionsDto {
  @ApiProperty({ description: 'Grid Bot UUID', required: false })
  @IsString()
  @IsOptional()
  uuid: string;

  @ApiProperty({ description: 'Platform id', required: false })
  @IsOptional()
  @IsString()
  platform_id: string;

  @ApiProperty({ description: 'Bot symbol', required: false })
  @IsOptional()
  @IsString()
  symbol: string;

  @ApiProperty({ description: 'User key id', required: false })
  @IsOptional()
  @IsString()
  user_key_id: string;

  @ApiProperty({
    description: 'Grid bot status',
    required: false,
    enum: GridBotStatus,
  })
  @IsOptional()
  @IsEnum(GridBotStatus)
  status: GridBotStatus;
}
