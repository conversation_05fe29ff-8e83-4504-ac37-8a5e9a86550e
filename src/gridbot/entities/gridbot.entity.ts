import {
  Column,
  CreateDate<PERSON>olumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum GridBotStatus {
  'ACTIVE' = 'ACTIVE',
  'INACTIVE' = 'INACTIVE',
  'DELETED' = 'DELETED',
  'ERROR' = 'ERROR',
}
@Entity()
export class Gridbot {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  uuid: string;

  @Column()
  upper_threshold: string;

  @Column()
  lower_threshold: string;

  @Column()
  step: string;

  @Column({ nullable: true })
  qty_per_step: string;

  @Column({ default: '0' })
  fee_pct: string;

  @Column()
  initial_fund: string;

  @Column({ nullable: true })
  initial_base: string;

  @Column({ nullable: true })
  initial_quote: string;

  @Column({ nullable: true })
  initial_price: string;

  @Column({ default: '0' })
  current_quote: string;

  @Column({ default: '0' })
  current_base: string;

  @Column({ default: '0' })
  realized_profit: string;

  @Column({ default: false })
  sell_at_stop: boolean;

  @Column({ default: GridBotStatus.INACTIVE })
  status: GridBotStatus;

  @Column()
  user_key_id: string;

  @Column()
  base_pair_id: string;

  @Column()
  platform_id: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}
