import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import { GridbotService } from './gridbot.service';
import { addGridSocketConnection, removeMultiSocketConnection } from 'common/util/utils';
import { TestGridSocketDto } from './dto/test-grid-socket.dto';
import { CreateGridbotDto } from './dto/create-gridbot.dto';
import { UpdateGridbotDto } from './dto/update-gridbot.dto';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { QueryGridbotDto } from './dto/query-gridbot.dto';
import { StartGridbotDto } from './dto/start-gridbot.dto';
import { StopGridbotDto } from './dto/stop-gridbot.dto';
import { CopyGridbotDto } from './dto/copy-gridbot.dto';
import { DeleteGridbotDto } from './dto/delete-gridbot.dto';
import { ApiOperation } from '@nestjs/swagger';

@Controller('grid-bot')
@UseGuards(JwtAuthGuard)
export class GridbotController {
  constructor(private readonly gridbotService: GridbotService) {}

  @Post('create')
  @ApiOperation({
    summary: 'Create a new grid bot',
  })
  async create(@Body() createGridbotDto: CreateGridbotDto, @Req() req) {
    return await this.gridbotService.create(req.user, createGridbotDto);
  }

  @Post('copy')
  @ApiOperation({
    summary: 'Copy an existing grid bot',
  })
  async copy(@Body() copyGridbotDto: CopyGridbotDto, @Req() req) {
    return await this.gridbotService.copy(req.user, copyGridbotDto);
  }

  @Post('start')
  @ApiOperation({
    summary: 'Start a grid bot',
  })
  async start(@Body() startGridbotDto: StartGridbotDto, @Req() req) {
    return await this.gridbotService.start(req.user, startGridbotDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Find all grid bots',
  })
  async findAll(@Query() query: QueryGridbotDto, @Req() req) {
    return await this.gridbotService.findAll(req.user, query);
  }

  @Post('stop')
  @ApiOperation({
    summary: 'Stop a grid bot',
  })
  stop(@Req() req: any, @Body() body: StopGridbotDto) {
    return this.gridbotService.stop(req.user, body);
  }

  @Post('delete')
  @ApiOperation({
    summary: 'Delete a grid bot',
  })
  async remove(@Req() req: any, @Body() deleteGridbotDto: DeleteGridbotDto) {
    return await this.gridbotService.remove(req.user, deleteGridbotDto);
  }

  @Post('update')
  @ApiOperation({
    summary: 'Update a grid bot',
  })
  async update(
    @Req() req: any,
    @Body() updateGridbotDto: UpdateGridbotDto,
  ) {
    return await this.gridbotService.update(req.user, updateGridbotDto);
  }

  @Post('patch-order')
  @ApiOperation({
    summary: 'Patch order',
  })
  async patchOrder(@Body() body: any) {
    return await this.gridbotService.patchOrder(body);
  }

  @Post('test-grid-socket')
  @ApiOperation({
    summary: 'Test addGridSocketConnection utility directly',
  })
  async testGridSocket(@Body() body: TestGridSocketDto) {
    await addGridSocketConnection(body.pair_id, body.platform_id);
    return { message: 'addGridSocketConnection called', pair_id: body.pair_id, platform_id: body.platform_id };
  }

  @Post('test-grid-socket-unsubscribe')
  @ApiOperation({
    summary: 'Test removeMultiSocketConnection utility directly',
  })
  async testGridSocketUnsubscribe(@Body() body: TestGridSocketDto) {
    await removeMultiSocketConnection(body.pair_id, body.platform_id);
    return { message: 'removeMultiSocketConnection called', pair_id: body.pair_id, platform_id: body.platform_id };
  }
}
