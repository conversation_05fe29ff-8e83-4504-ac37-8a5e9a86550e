import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsString, IsOptional, IsBoolean, IsBooleanString } from 'class-validator';

export class QueryPairDto {
  @ApiProperty({ description: 'Pair id', required: false })
  @IsString()
  @IsOptional()
  id: string;

  @ApiProperty({ description: 'Platform id', required: false })
  @IsOptional()
  @IsString()
  platform_id: string;

  @ApiProperty({ description: 'Pair symbol', required: false })
  @IsOptional()
  @IsString()
  symbol: string;

  @ApiProperty({ description: 'Pair quote', required: false })
  @IsOptional()
  @IsString()
  quote: string;

  @ApiProperty({ description: 'Pair base', required: false })
  @IsOptional()
  @IsString()
  base: string;

  @ApiProperty({ description: 'Filter by isArbitrageBot', required: false, type: Boolean })
  @IsOptional()
  @IsBooleanString()
  is_arbit?: string;
}
