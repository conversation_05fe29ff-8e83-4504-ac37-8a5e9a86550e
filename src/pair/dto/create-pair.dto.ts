import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString } from "class-validator";

export class CreatePairDto {

    @ApiProperty({ description: 'Symbol of the trading pair'})
    @IsString()
    symbol: string;

    @ApiProperty({ description: 'Tick size of the pair'})
    @IsString()
    tick: string;

    @ApiProperty({ description: 'Step size of the pair'})
    @IsString()
    step: string;

    @ApiProperty({ description: 'Minimum notional value for trading'})
    @IsString()
    min_notional: string;

    @ApiProperty({ description: 'Maximum notional value for trading'})
    @IsString()
    max_notional: string;

    @ApiProperty({ description: 'Base currency of the pair'})
    @IsString()
    base: string;
  
    @ApiProperty({ description: 'Quote currency of the pair'})
    @IsString()
    quote: string;
  
    @ApiProperty({ description: 'Decimal places for price'})
    @IsNumber()
    price_dp: number;
  
    @ApiProperty({ description: 'Decimal places for quantity'})
    @IsNumber()
    qty_dp: number;

    @ApiProperty({ description: 'Decimal places for price'})
    @IsString()
    price: string;
  
    @ApiProperty({ description: 'Platform ID where the pair is available'})
    @IsNumber()
    platform_id: number;
}
