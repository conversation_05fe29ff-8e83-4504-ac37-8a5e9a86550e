import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsNotEmpty, IsString } from 'class-validator';

export enum Pairstatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

export class UpdatePairDto {
  @ApiProperty({ description: 'Id for the pair', required: true })
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Updated status of the pair',
    enum: Pairstatus,
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(Pairstatus)
  status: Pairstatus;
}
