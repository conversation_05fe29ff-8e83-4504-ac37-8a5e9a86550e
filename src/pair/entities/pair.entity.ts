import { Platform } from 'src/platform/entities/platform.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum Pairstatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

@Entity()
export class Pair {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  symbol: string;

  @Column({ type: 'float' })
  tick: string;

  @Column({ type: 'float' })
  step: string;

  @Column({ type: 'float' })
  min_notional: string;

  @Column({ type: 'float' })
  max_notional: string;

  @Column({ precision: 10 })
  base: string;

  @Column({ precision: 10 })
  quote: string;

  @Column()
  price_dp: number;

  @Column()
  qty_dp: number;

  @Column({})
  price: string;

  @Column()
  platform_id: number;

  @Column({
    type: 'enum',
    enum: Pairstatus,
    default: Pairstatus.ACTIVE,
  })
  status: Pairstatus;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @Column({ type: 'boolean', default: false })
  is_arbit: boolean;

  @Column({ type: 'boolean', default: false })
  is_grid: boolean;
}
