import { BadRequestException, Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ogger } from 'nestjs-pino';
import { CreatePairDto } from './dto/create-pair.dto';
import { UpdatePairDto } from './dto/update-pair.dto';
import { QueryPairDto } from './dto/query-pair.dto';

import { EntityManager, Repository } from 'typeorm';
import { Pair, Pairstatus } from './entities/pair.entity';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { HataPlatformHelper } from 'common/helper/hata-platform.helper';
import { Platform } from 'src/platform/entities/platform.entity';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import { Cron, CronExpression } from '@nestjs/schedule';
import { TokenizePlatformHelper } from 'common/helper/tokenize-platform.helper';
import { LunoPlatformHelper } from 'common/helper/luno-platform.helper';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';

@Injectable()
export class PairService {
  private readonly BINANCE_PLATFORM_ID = 1;
  private readonly HATA_GLOBAL_PLATFORM_ID = 2;
  private readonly HATA_MALAYSIA_PLATFORM_ID = 3;

  constructor(
    private em: EntityManager,
    @InjectRepository(Pair)
    private readonly pairRepository: Repository<Pair>,
    @InjectQueue('check-order-status')
    private checkOrderStatusQueue: Queue, // Add this injection
    private readonly pinoLogger: PinoLogger,
  ) {}

  async create(query: CreatePairDto) {
    await this.em.save(Pair, {
      ...query,
    });

    return `UserKey ${query.base} / ${query.quote} created successfully`;
  }

  async findAll(query: QueryPairDto) {
    this.pinoLogger.info({ query }, 'PairService.findAll called');
    console.log('PairService.findAll called', query );
    
    const queryBuilder = this.em
      .createQueryBuilder(Pair, 'pair')
      .leftJoin('platform', 'platform', 'platform.id = pair.platform_id')
      .where(`platform.status = 1 and pair.status = 'ACTIVE'`);

    // Filter by pair ID if provided
    if (query.id) {
      queryBuilder.andWhere('pair.id = :id', { id: query.id });
    }

    // Filter by platform ID if provided
    if (query.platform_id) {
      queryBuilder.andWhere('pair.platform_id = :platform_id', {
        platform_id: query.platform_id,
      });
    }

    if (query.symbol) {
      queryBuilder.andWhere('pair.symbol = :symbol', {
        symbol: query.symbol,
      });
    }

    if (query.quote) {
      queryBuilder.andWhere('pair.quote = :quote', {
        quote: query.quote,
      });
    }

    if (query.base) {
      queryBuilder.andWhere('pair.base = :base', {
        base: query.base,
      });
    }

    // Handle isArbit parameter - convert string to boolean if needed
    if (query.is_arbit !== undefined) {
      let isArbitValue: boolean;
      
      // Handle both boolean and string values
      if (typeof query.is_arbit === 'string') {
        isArbitValue = query.is_arbit.toLowerCase() === 'true';
      } else {
        isArbitValue = query.is_arbit;
      }
      
      queryBuilder.andWhere('pair.is_arbit = :is_arbit', { is_arbit: isArbitValue });
    }
    // Sort pairs alphabetically by symbol
    queryBuilder.orderBy('pair.symbol', 'ASC');

    // Execute the query and return results
    const result = await queryBuilder.getMany();
    this.pinoLogger.info({ count: result.length }, 'PairService.findAll result count');
    return result;
  }

  async update(body: UpdatePairDto) {
    const { status } = body;

    // Find the pair by ID
    const pair = await this.em
      .createQueryBuilder(Pair, 'pair')
      .where('pair.id = :id', { id: body.id })
      .getOne();

    if (!pair) {
      throw new BadRequestException(CustomErrorMessages.PAIR.PAIR_NOT_FOUND);
    }
    // Update status only if provided
    if (status) {
      await this.em
        .createQueryBuilder()
        .update(Pair)
        .set({ status: body.status })
        .where('id = :id', { id: body.id })
        .execute();
    }
    return 'Pair status updated successfully ';
  }

  async seedFromBinance(): Promise<{ message: string; insertedCount: number }> {
    try {
      const binanceHelper = callPlatformHelper('binance');
      const symbols = await binanceHelper.readActivePairFromExchange();
      const prices = await binanceHelper.getAllTicker();
      let insertedCount = 0;
      let updatedCount = 0;

      let platform = await this.em
        .createQueryBuilder(Platform, 'p')
        .where("p.name = 'binance'")
        .getOne();
      if (!platform) {
        throw new BadRequestException(
          CustomErrorMessages.PLATFORM.NOT_FOUND_BINANCE,
        );
      }

      // Loop through each symbol from Binance.
      for (const symbolData of symbols) {
        // Create a new Pair instance.
        // Check for existing pair
        let pair = await this.em.findOne(Pair, {
          where: {
            symbol: symbolData.symbol,
            platform_id: platform.id,
          },
        });

        // Create or update counter logic
        if (!pair) {
          pair = new Pair();
          insertedCount++;
        } else {
          updatedCount++;
        }

        pair.symbol = symbolData.symbol;
        pair.base = symbolData.baseAsset;
        pair.quote = symbolData.quoteAsset;

        // PRICE_FILTER: For tickSize and price_dp.
        const priceFilter = symbolData.filters.find(
          (filter) => filter.filterType === 'PRICE_FILTER',
        );
        if (priceFilter) {
          pair.tick = priceFilter.tickSize;
          pair.price_dp = priceFilter.tickSize.includes('.')
            ? priceFilter.tickSize.split('.')[1].replace(/0+$/, '').length
            : 0;
        } else {
          pair.tick = '0';
          pair.price_dp = 0;
        }

        // LOT_SIZE: For step and qty_dp.
        const lotSizeFilter = symbolData.filters.find(
          (filter) => filter.filterType === 'LOT_SIZE',
        );
        if (lotSizeFilter) {
          pair.step = lotSizeFilter.stepSize;
          pair.qty_dp = lotSizeFilter.stepSize.includes('.')
            ? lotSizeFilter.stepSize.split('.')[1].replace(/0+$/, '').length
            : 0;
        } else {
          pair.step = '0';
          pair.qty_dp = 0;
        }

        // NOTIONAL: For min_notional and max_notional.
        const notionalFilter = symbolData.filters.find(
          (filter) => filter.filterType === 'NOTIONAL',
        );
        if (notionalFilter) {
          pair.min_notional = notionalFilter.minNotional || '0';
          pair.max_notional = notionalFilter.maxNotional || '0';
        } else {
          pair.min_notional = '0';
          pair.max_notional = '0';
        }

        // Set platform_id to the Binance platform.
        pair.platform_id = platform.id;
        // Optionally, set default status.
        pair.status = Pairstatus.ACTIVE;
        const matchingPair = prices.find(
          (pair) => pair.symbol === symbolData.symbol,
        );
        pair.price = matchingPair ? matchingPair.lastPrice : null;

        // Save the pair record.
        await this.em.save(Pair, pair);
        insertedCount++;
      }

      return {
        message: `Pairs seeded from Binance successfully (${insertedCount} new, ${updatedCount} updated)`,
        insertedCount,
      };
    } catch (error) {
      throw new BadRequestException(
        CustomErrorMessages.PAIR.SEED_FAILED_BINANCE + ': ' + error.message,
      );
    }
  }

  async seedFromHata(
    platformType: 'global' | 'malaysia',
  ): Promise<{ message: string; insertedCount: number }> {
    try {
      let hataHelper = new HataPlatformHelper();
      hataHelper.init(
        platformType == 'global' ? 'hata global' : 'hata myr',
        '',
        '',
      );
      const symbols = await hataHelper.readActivePairFromExchange();
      const prices = await hataHelper.getAllTicker();
      if (!symbols) {
        throw new BadRequestException(CustomErrorMessages.PAIR.NO_SYMBOLS_HATA);
      }
      let insertedCount = 0;
      let updatedCount = 0;

      let platform = await this.em
        .createQueryBuilder(Platform, 'p')
        .where('p.name = :name', {
          name: platformType == 'global' ? 'Hata Global' : 'Hata MYR',
        })
        .getOne();
      if (!platform) {
        throw new BadRequestException(
          CustomErrorMessages.PLATFORM.NOT_FOUND_HATA,
        );
      }
      for (const symbolData of symbols) {
        // Check for existing pair
        let pair = await this.em.findOne(Pair, {
          where: {
            symbol: symbolData.symbol || symbolData.base + symbolData.quote,
            platform_id: platform.id,
          },
        });

        // Create or update counter logic
        if (!pair) {
          pair = new Pair();
          insertedCount++;
        } else {
          updatedCount++;
        }

        pair.symbol = symbolData.symbol || symbolData.base + symbolData.quote;
        pair.base = symbolData.base;
        pair.quote = symbolData.quote;
        pair.tick = symbolData.tick_size || '0.0001';
        pair.step = symbolData.min_step || '0.001';
        pair.min_notional = symbolData.min_notional || '10';
        pair.max_notional = symbolData.max_notional || '99999999';
        pair.price_dp =
          symbolData.disp_price_scale != null
            ? parseInt(symbolData.disp_price_scale, 10)
            : 2;
        pair.qty_dp =
          symbolData.disp_qty_scale != null
            ? parseInt(symbolData.disp_qty_scale, 10)
            : 5;
        pair.platform_id = platform.id;
        const matchingPair = prices.data.find(
          (pair) => pair.symbol === symbolData.symbol,
        );
        pair.price = matchingPair ? matchingPair.price : null;

        pair.status = Pairstatus.ACTIVE;
        await this.em.save(Pair, pair);
      }
      return {
        message: `Pairs seeded from Hata ${platformType} successfully (${insertedCount} new, ${updatedCount} updated)`,
        insertedCount,
      };
    } catch (error) {
      throw new BadRequestException(
        CustomErrorMessages.PAIR.SEED_FAILED_HATA + ': ' + error.message,
      );
    }
  }

  async seedFromTokenize(): Promise<{
    message: string;
    insertedCount: number;
  }> {
    try {
      let tokenizeHelper = new TokenizePlatformHelper();
      const symbols = await tokenizeHelper.readActivePairFromExchange();
      const prices = await tokenizeHelper.getAllTicker();
      if (!symbols) {
        throw new BadRequestException(
          CustomErrorMessages.PAIR.NO_SYMBOLS_TOKENIZE,
        );
      }
      let insertedCount = 0;
      let updatedCount = 0;

      let platform = await this.em
        .createQueryBuilder(Platform, 'p')
        .where('p.name = :name', {
          name: 'tokenize',
        })
        .getOne();
      if (!platform) {
        throw new BadRequestException(
          CustomErrorMessages.PLATFORM.NOT_FOUND_TOKENIZE,
        );
      }
      for (const symbolData of symbols) {
        // Check for existing pair
        let pair = await this.em.findOne(Pair, {
          where: {
            symbol: symbolData.symbol || symbolData.base + symbolData.quote,
            platform_id: platform.id,
          },
        });

        // Create or update counter logic
        if (!pair) {
          pair = new Pair();
          insertedCount++;
        } else {
          updatedCount++;
        }

        pair.symbol = symbolData.symbol || symbolData.base + symbolData.quote;
        pair.base = symbolData.base;
        pair.quote = symbolData.quote;
        pair.tick = symbolData.tick_size || '0.0001';
        pair.step = symbolData.min_step || '0.001';
        pair.min_notional = symbolData.min_notional || '10';
        pair.max_notional = symbolData.max_notional || '99999999';
        pair.price_dp =
          symbolData.disp_price_scale != null
            ? parseInt(symbolData.disp_price_scale, 10)
            : 2;
        pair.qty_dp =
          symbolData.disp_qty_scale != null
            ? parseInt(symbolData.disp_qty_scale, 10)
            : 5;
        pair.platform_id = platform.id;
        const matchingPair = prices.find(
          (pair) => pair.symbol === symbolData.symbol,
        );
        pair.price = matchingPair ? matchingPair.price : null;

        pair.status = Pairstatus.ACTIVE;
        await this.em.save(Pair, pair);
      }
      return {
        message: `Pairs seeded from Tokenize successfully (${insertedCount} new, ${updatedCount} updated)`,
        insertedCount,
      };
    } catch (error) {
      throw new BadRequestException(
        CustomErrorMessages.PAIR.SEED_FAILED_HATA + ': ' + error.message,
      );
    }
  }

  async seedFromLuno(): Promise<{ message: string; insertedCount: number }> {
    try {
      let lunoHelper = new LunoPlatformHelper();
      await lunoHelper.init('luno', '', '');
      const symbols = await lunoHelper.readActivePairFromExchange();
      // const prices = await lunoHelper.getAllTicker();
      if (!symbols) {
        throw new BadRequestException(
          CustomErrorMessages.PAIR.NO_SYMBOLS_LUNO,
        );
      }
      let insertedCount = 0;
      let updatedCount = 0;

      let platform = await this.em
        .createQueryBuilder(Platform, 'p')
        .where('p.name = :name', {
          name: 'luno',
        })
        .getOne();
      if (!platform) {
        throw new BadRequestException(
          CustomErrorMessages.PLATFORM.NOT_FOUND_LUNO,
        );
      }
      // Luno supported currencies
      const lunoCrypto = [
        'AAVE', 'ADA', 'ALGO', 'ATOM', 'AVAX', 'BCH', 'CRV', 'DOT', 'DOGE', 'ETH', 'FTM', 'GRT', 'LINK', 'LTC', 'MKR', 'POL', 'NEAR', 'SAND', 'SNX', 'SOL', 'TRX', 'UNI', 'USDC', 'USDT', 'BTC', 'XLM', 'XRP'
      ];
      const lunoFiat = [
        'AUD', 'EUR', 'GBP', 'IDR', 'MYR', 'NGN', 'UGX', 'ZAR'
      ];
      const activePairs = [];
      const inactivePairs = [];

      for (const symbolData of symbols) {
        // Check for existing pair
        let pair = await this.em.findOne(Pair, {
          where: {
            symbol: symbolData.symbol,
            platform_id: platform.id,
          },
        });

        // Create or update counter logic
        if (!pair) {
          pair = new Pair();
          insertedCount++;
        } else {
          updatedCount++;
        }

        pair.symbol = symbolData.symbol;
        // Luno pairs are like 'XBTZAR', so split base/quote
        pair.base = symbolData.symbol.substring(0, 3);
        pair.quote = symbolData.symbol.substring(3);
        pair.tick = '0.01'; // Luno default, adjust if needed
        pair.step = '0.0001'; // Luno default, adjust if needed
        pair.min_notional = '0';
        pair.max_notional = '99999999';
        pair.price_dp = 2;
        pair.qty_dp = 5;
        pair.platform_id = platform.id;
        // const matchingPair = prices.find(
        //   (pair) => pair.symbol === symbolData.symbol,
        // );
        // pair.price = matchingPair ? matchingPair.lastPrice : null;
        pair.price = "0";

        // Set ACTIVE only if base is crypto and quote is MYR or USDT
        if (lunoCrypto.includes(pair.base) && (pair.quote === 'MYR' || pair.quote === 'USDT')) {
          pair.status = Pairstatus.ACTIVE;
          activePairs.push(pair.symbol);
        } else {
          pair.status = Pairstatus.INACTIVE;
          inactivePairs.push(pair.symbol);
        }
        // console.log('[seedFromLuno] Saving pair to DB:', pair);
        await this.em.save(Pair, pair);
      }
      // Visualization: log or expose active/inactive pairs
      console.log('Luno ACTIVE pairs:', activePairs);
      console.log('Luno INACTIVE pairs:', inactivePairs);
      return {
        message: `Pairs seeded from Luno successfully (${insertedCount} new, ${updatedCount} updated). Active: ${activePairs.length}, Inactive: ${inactivePairs.length}`,
        insertedCount,
      };
    } catch (error) {
      throw new BadRequestException(
        CustomErrorMessages.PAIR.SEED_FAILED_LUNO + ': ' + error.message,
      );
    }
  }

  async seedPairs(
    platform_id: number,
  ): Promise<{ message: string; insertedCount: number }> {
    if (platform_id === 1) {
      return await this.seedFromHata('malaysia');
    } else if (platform_id === 2) {
      return await this.seedFromBinance();
    } else if (platform_id === 3) {
      return await this.seedFromHata('global');
    } else if (platform_id === 4) {
      return await this.seedFromTokenize();
    } else if (platform_id === 5) {
      return await this.seedFromLuno();
    } else {
      throw new BadRequestException(
        CustomErrorMessages.PLATFORM.INVALID_PLATFORM,
      );
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES, { name: 'pair-price-update' })
  async updatePairPrice(): Promise<{ message: string; insertedCount: number }> {
    try {
      // Get active pairs from database
      const activePairs = await this.em
        .createQueryBuilder(Pair, 'p')
        .where(`p.status = 'ACTIVE'`)
        .leftJoin('platform', 'platform', 'p.platform_id = platform.id')
        .select([
          'p.id as id',
          'p.platform_id as platform_id',
          'p.symbol as symbol',
          'p.price as price',
        ])
        .addSelect(
          `JSON_OBJECT(
      'id', platform.id,
      'name', platform.name,
      'image', platform.image
    )`,
          'platform',
        )
        .getRawMany();

      if (!activePairs.length) {
        return { message: 'No active pairs found', insertedCount: 0 };
      }

      let updatedCount = 0;
      let binanceHelper;
      let hataGlobalHelper;
      let hataMalaysiaHelper;
      let binancePrices, hataGlobalPrices, hataMalaysiaPrices;

      // Process each pair based on platform
      for (const existingPair of activePairs) {
        if (
          existingPair.platform.name.toLowerCase() == 'binance' &&
          !binanceHelper
        ) {
          binanceHelper = callPlatformHelper(
            existingPair.platform.name.toLowerCase(),
          );
          binancePrices = await binanceHelper.getAllTicker();
        } else if (
          existingPair.platform.name.toLowerCase() == 'hata global' &&
          !hataGlobalHelper
        ) {
          hataGlobalHelper = new HataPlatformHelper();
          hataGlobalHelper.init(
            existingPair.platform.name.toLowerCase(),
            '',
            '',
          );
          hataGlobalPrices = await hataGlobalHelper.getAllTicker();
        } else if (
          existingPair.platform.name.toLowerCase() == 'hata myr' &&
          !hataMalaysiaHelper
        ) {
          hataMalaysiaHelper = new HataPlatformHelper();
          hataMalaysiaHelper.init(
            existingPair.platform.name.toLowerCase(),
            '',
            '',
          );
          hataMalaysiaPrices = await hataMalaysiaHelper.getAllTicker();
        }
        let pairPrice;

        // Handle different platforms
        switch (existingPair.platform_id) {
          case 1: // Hata Malaysia
            pairPrice = hataMalaysiaPrices.data.find(
              (pair) => pair.symbol === existingPair.symbol,
            );
            pairPrice = pairPrice ? pairPrice.price : null;

            break;

          case 2: // Binance
            pairPrice = binancePrices.find(
              (pair) => pair.symbol === existingPair.symbol,
            );
            pairPrice = pairPrice ? pairPrice.lastPrice : null;

            break;

          case 3: // Hata Global
            pairPrice = hataGlobalPrices.data.find(
              (price) => price.symbol === existingPair.symbol,
            );
            pairPrice = pairPrice ? pairPrice.price : null;

            break;

          default:
            continue; // Skip unsupported platforms
        }

        // Update pair price in database if found
        if (pairPrice) {
          let log = await this.em
            .createQueryBuilder()
            .update(Pair)
            .set({ price: pairPrice })
            .where('id = :id', { id: existingPair.id })
            .execute();

          updatedCount++;
        }
      }
      console.log(`Successfully updated ${updatedCount} pair prices`);

      // Pass only required fields to the job
      const tradingPairsWithPrices = activePairs.map((pair) => ({
        pairId: pair.id, // Use pair ID instead of symbol
        symbol: pair.symbol, // Keep symbol for logging and API calls
        currentPrice: pair.price, // This will be the updated price from the market
        platformId: pair.platform_id,
      }));

      // Add job to check order status queue with trading pairs and their current prices
      await this.checkOrderStatusQueue.add('processing', {
        tradingPairs: tradingPairsWithPrices,
      });

      return {
        message: `Successfully updated ${updatedCount} pair prices`,
        insertedCount: updatedCount,
      };
    } catch (error) {
      throw new Error(`Failed to update pair prices: ${error.message}`);
    }
  }
}
