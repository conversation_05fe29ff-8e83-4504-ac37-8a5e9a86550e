import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { PairService } from './pair.service';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { ApiOperation } from '@nestjs/swagger';
import { CreatePairDto } from './dto/create-pair.dto';
import { UpdatePairDto } from './dto/update-pair.dto';
import { QueryPairDto } from './dto/query-pair.dto';
import { SeedPairSourceDto } from './dto/seed-pair-source.dto';

@Controller('pair')
export class PairController {
  constructor(private readonly pairService: PairService) {}

  @Post('create')
  @ApiOperation({
    summary: 'User create new pair',
  })
  @UseGuards(JwtAuthGuard)
  async create(@Body() body: CreatePairDto) {
    return await this.pairService.create(body);
  }

  @Get()
  @ApiOperation({
    summary: 'Find all pair (with filter)',
  })
  async findAll(@Query() body: QueryPairDto) {
    return await this.pairService.findAll(body);
  }

  @Post('/update')
  @ApiOperation({
    summary: 'Update pair status',
  })
  @UseGuards(JwtAuthGuard)
  async updatePair(@Body() body: UpdatePairDto) {
    return await this.pairService.update(body);
  }

  // @Post('seed')
  // @HttpCode(HttpStatus.OK)
  // @UseGuards(JwtAuthGuard)
  // @ApiOperation({ summary: 'Seed pairs from Binance API' })
  // async seedPairs() {
  //   return await this.pairService.seedFromBinance();
  // }

  @Post('seed/source')
  @ApiOperation({
    summary:
      'Seed pairs from chosen source: 1=Hata Malaysia, 2=Binance, 3=Hata Global, 5=Luno',
  })
  async seedPairs(@Body() body: SeedPairSourceDto) {
    return await this.pairService.seedPairs(body.platform_id);
  }

  @Post('/updatePrice')
  @ApiOperation({
    summary: 'Update pair price',
  })
  @UseGuards(JwtAuthGuard)
  async updatePairPrice() {
    return await this.pairService.updatePairPrice();
  }
}
