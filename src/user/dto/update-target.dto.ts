import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsNotEmpty, IsNumber } from 'class-validator';

export class UpdateTargetDto {
  @ApiProperty({ description: 'bot configuration id' })
  @IsNotEmpty()
  @IsNumber()
  bot_config_id: number;

  @ApiProperty({ description: 'day to edit' })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  day: number[];

  @ApiProperty({ description: 'new target' })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  target_price: number[];
}
