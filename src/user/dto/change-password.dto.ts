import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class UserChangePasswordDto {
  @ApiProperty({ description: 'user existing password' })
  @IsNotEmpty()
  @IsString()
  old_password: string;

  @ApiProperty({ description: 'user new password' })
  @IsNotEmpty()
  @IsString()
  new_password: string;

  @ApiProperty({ description: 'user confirm password', required: true })
  @IsNotEmpty()
  @IsString()
  confirm_password: string;
}
