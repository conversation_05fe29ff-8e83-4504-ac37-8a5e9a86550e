import { ApiProperty } from '@nestjs/swagger';
import { IsE<PERSON>, IsNotEmpty, IsString } from 'class-validator';

export class RegisterUserDto {
  @ApiProperty({ description: 'user email' })
  @IsEmail()
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty({ description: 'user password' })
  @IsNotEmpty()
  @IsString()
  password: string;

  @ApiProperty({ description: 'user invitation code' })
  @IsNotEmpty()
  @IsString()
  invitation_code: string;
}
