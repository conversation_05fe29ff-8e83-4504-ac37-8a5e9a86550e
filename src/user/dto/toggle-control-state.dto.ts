import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber } from 'class-validator';

export enum ControlState {
  PAUSE = 'PAUSE',
  RESUME = 'RESUME',
}

export class ToggleControlState {
  @ApiProperty({ description: 'bot configuration id' })
  @IsNotEmpty()
  @IsNumber()
  bot_config_id: number;

  @ApiProperty({
    enum: ControlState,
    description: 'control bot state',
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(ControlState)
  state: ControlState;
}
