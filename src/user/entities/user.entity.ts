import { User<PERSON><PERSON> } from 'src/user_key/entities/user_key.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  OneToMany,
  ManyToOne,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { ActivityLog } from 'common/entity/activity-log.entity';

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  email: string;

  @Column()
  @Exclude()
  password: string;

  @Column({ nullable: true })
  invitation_code: string;

  @Column({ default: null, nullable: true })
  @Exclude()
  temp_password: string;

  @Column({ nullable: true })
  g2a: string;

  @OneToMany((type) => ActivityLog, (activity_log) => activity_log.user)
  activity_log: ActivityLog[];

  @ManyToOne((type) => User, (user) => user.id)
  invitation_id: User;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  @Exclude()
  deleted_at: Date;
}
