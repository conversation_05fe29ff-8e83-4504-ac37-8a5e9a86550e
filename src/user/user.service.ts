import {
  BadRequestException,
  ConflictException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { plainToClass } from 'class-transformer';
import * as base32 from 'hi-base32';
import configuration from 'config/configuration';
import { EntityManager, Equal } from 'typeorm';
import { UserChangePasswordDto } from './dto/change-password.dto';
import { RegisterUserDto } from './dto/register-user.dto';
import { UserLoginDto } from './dto/user-login.dto';
import { User } from './entities/user.entity';
import { UserToggleBotDto } from './dto/user-toggle-bot.dto';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import { UserKey } from 'src/user_key/entities/user_key.entity';
import { ToggleControlState } from './dto/toggle-control-state.dto';
import { UpdateTargetDto } from './dto/update-target.dto';
import { QueryControlDetailsDto } from './dto/query-control-details.dto';
import { RedisService } from 'src/redis/redis.service';
import {
  decrypted,
  encrypted,
  generateSecret,
  verify2FA,
} from 'common/util/utils';
import { UserBind2faDto } from './dto/bind2fa.dto';
import { UserUnbind2faDto } from './dto/unbind2fa.dto';
import { Login2FADto } from './dto/login2FA.dto';
import { SessionList } from 'src/session_list/entities/session_list.entity';

@Injectable()
export class UserService {
  constructor(
    private em: EntityManager,
    private readonly jwtService: JwtService,
    // private readonly redisService: RedisService,
  ) {}

  async bind2fa(user: User, dto: UserBind2faDto) {
    if (user.g2a) {
      throw new BadRequestException(CustomErrorMessages.USER.GOOGLE_BINDED);
    }
    verify2FA(dto.secret, dto.google_2fa);

    user.g2a = await encrypted(dto.secret, configuration().encryption.secret);
    await this.em.save(user);
    return 'Google 2fa binded';
  }

  async unbind2fa(user: User, dto: UserUnbind2faDto) {
    if (!user.g2a) {
      throw new BadRequestException(CustomErrorMessages.USER.GOOGLE_NOT_BINDED);
    }
    let secret = await decrypted(user.g2a, configuration().encryption.secret);
    verify2FA(secret, dto.google_2fa);

    user.g2a = null;
    await this.em.save(user);
    return 'Google 2fa unbinded';
  }

  async login(userLoginDto: UserLoginDto) {
    const user = await this.em.findOneBy(User, {
      email: userLoginDto.email,
    });

    if (!user) {
      throw new ConflictException(CustomErrorMessages.USER.USER_NOT_FOUND);
    }

    const verify = await bcrypt.compare(userLoginDto.password, user.password);

    if (!verify) {
      throw new BadRequestException(
        CustomErrorMessages.USER.INVALID_CREDENTIAL,
      );
    }
    let access_token, is_2fa;
    if (!user.g2a) {
      access_token = this.jwtService.sign(
        { email: user.email, is_2fa: false },
        {
          secret: configuration().jwtConstant.secret,
          expiresIn: configuration().max_age_2fa / 1000 + 's',
        },
      );
      is_2fa = false;
    } else {
      access_token = this.jwtService.sign(
        { email: user.email, is_2fa: true },
        {
          secret: configuration().jwtConstant.secret,
          expiresIn: configuration().max_age_2fa / 1000 + 's',
        },
      );
      is_2fa = true;
    }

    return {
      email: plainToClass(User, user),
      access_token,
      is_2fa,
    };
  }

  async login2fa(req, login2FADto: Login2FADto) {
    let user_detail = req.user;
    if (user_detail.is_2fa !== true) {
      throw new UnauthorizedException(
        'Please bind 2fa before proceed to 2fa login',
      );
    }
    if (user_detail.email !== login2FADto.email.toLowerCase()) {
      throw new BadRequestException('email is not match with session');
    }
    let user = await this.em.findOneBy(User, {
      email: Equal(login2FADto.email),
    });
    if (!user) {
      throw new BadRequestException('Account not exists');
    }
    verify2FA(
      await decrypted(user.g2a, configuration().encryption.secret),
      login2FADto.google_2fa,
    );
    let access_token = this.jwtService.sign(
      { email: user.email },
      {
        secret: configuration().jwtConstant.secret,
        expiresIn: configuration().max_age / 1000 + 's',
      },
    );
    console.log(req.headers);
    let session = new SessionList();
    session.session_id = access_token;
    session.user_id = user.id.toString();
    session.user_agent = req.headers['user-agent'];
    session.ip_address = req.headers['x-forwarded-for'] || req.ip;
    session.status = true;
    await this.em.save(SessionList, session);

    return { email: plainToClass(User, user), access_token };
  }

  async changePassword(user: User, dto: UserChangePasswordDto) {
    const verify = await bcrypt.compare(dto.old_password, user.password);

    if (!verify) {
      throw new BadRequestException(
        CustomErrorMessages.USER.INVALID_CREDENTIAL,
      );
    } else if (dto.new_password != dto.confirm_password) {
      throw new BadRequestException(CustomErrorMessages.USER.INVALID_PASSWORD);
    }

    const new_password = await bcrypt.hash(dto.new_password, 10);

    user.password = new_password;

    await this.em.save(User, user);

    return 'Password changed successfully';
  }

  async registerUser(dto: RegisterUserDto) {
    let user = await this.em
      .createQueryBuilder(User, 'user')
      .where('user.email = :email', { email: dto.email })
      .getOne();
    let inviter = await this.em
      .createQueryBuilder(User, 'user')
      .where('user.invitation_code = :invitation_code', {
        invitation_code: dto.invitation_code,
      })
      .getOne();
    if (!inviter) {
      throw new BadRequestException('Invitation code is not valid');
    } else if (user) {
      throw new BadRequestException(CustomErrorMessages.USER.USER_REGISTERED);
    }
    inviter.invitation_code = (await generateSecret()) + Date.now();
    await this.em.save(User, inviter);
    const password = await bcrypt.hash(dto.password, 10);
    user = new User();
    user.password = password;
    user.invitation_id = inviter;
    user.invitation_code = (await generateSecret()) + Date.now();
    user.email = dto.email;
    await this.em.save(user);

    const access_token = this.jwtService.sign(
      { email: user.email },
      {
        secret: configuration().jwtConstant.secret,
        expiresIn: configuration().max_age / 1000 + 's',
      },
    );

    return {
      email: plainToClass(User, user),
      access_token,
    };
  }

  async generateInvitationCode(user: User) {
    user.invitation_code = (await generateSecret()) + Date.now();
    await this.em.save(User, user);
    return user.invitation_code;
  }

  async findOne(id: number): Promise<User> {
    let user = await this.em
      .createQueryBuilder(User, 'user')
      .where('user.id = :id', { id })
      .getOne();
    if (!user)
      throw new BadRequestException(CustomErrorMessages.AUTH.INVALID_USER);
    return user;
  }

  async prebind2FA(req, body: UserBind2faDto) {
    if (req.is_2fa == true) {
      throw new UnauthorizedException('This account is binded 2FA before');
    }
    let user = await this.em
      .createQueryBuilder(User, 'user')
      .where('user.email = :email', { email: req.email })
      .getOne();
    if (user.g2a) {
      throw new BadRequestException('2FA is binded before');
    }
    verify2FA(body.secret, body.google_2fa);
    user.g2a = await encrypted(body.secret, configuration().encryption.secret);
    await this.em.save(User, user);
    let access_token = this.jwtService.sign(
      { email: user.email },
      {
        secret: configuration().jwtConstant.secret,
        expiresIn: configuration().max_age / 1000 + 's',
      },
    );

    return { email: plainToClass(User, user), access_token };
    // return 'Bind successful';
  }

  async logout(req) {
    let session = await this.em
      .createQueryBuilder(SessionList, 'session')
      .where('session.session_id = :session_id', {
        session_id: req.cookies['ulog'],
      })
      .getOne();
    console.log(req.cookies);
    // if (!session) {
    //   throw new BadRequestException('Session not found');
    // }
    session.status = false;
    await this.em.save(SessionList, session);
    return 'Logout successful';
  }
}
