import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { UserChangePasswordDto } from './dto/change-password.dto';
import { RegisterUserDto } from './dto/register-user.dto';
import { UserLoginDto } from './dto/user-login.dto';
import { UserService } from './user.service';
import { UserToggleBotDto } from './dto/user-toggle-bot.dto';
import { ToggleControlState } from './dto/toggle-control-state.dto';
import { UpdateTargetDto } from './dto/update-target.dto';
import { QueryControlDetailsDto } from './dto/query-control-details.dto';
import { UserBind2faDto } from './dto/bind2fa.dto';
import { UserUnbind2faDto } from './dto/unbind2fa.dto';
import { plainToClass } from 'class-transformer';
import { User } from './entities/user.entity';
import { Jwt2FAAuthGuard } from 'src/auth/jwt-2fa-auth.guard';
import { Login2FADto } from './dto/login2FA.dto';
import configuration from 'config/configuration';
import { LogActivity } from 'common/decorator/activity-log.decorator';
import { LogEvent } from 'common/entity/activity-log.entity';

@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post('login')
  @LogActivity(LogEvent.LOGIN)
  @ApiOperation({
    summary: 'User login',
  })
  async login(
    @Body() userLoginDto: UserLoginDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const ret = await this.userService.login(userLoginDto);
    response.clearCookie('u-2fa');
    response.cookie('u-2fa', ret.access_token, {
      httpOnly: true,
      sameSite: 'none',
      secure: true,
      maxAge: configuration().max_age_2fa,
    });

    return { is_2fa: ret.is_2fa, email: ret.email };
  }

  @Post('login-2fa')
  @ApiOperation({
    summary: 'user login',
  })
  @UseGuards(Jwt2FAAuthGuard)
  async login2fa(
    @Req() req,
    @Body() body: Login2FADto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const ret = await this.userService.login2fa(req, body);
    response.clearCookie('ulog');
    response.cookie('ulog', ret.access_token, {
      httpOnly: true,
      sameSite: 'none',
      secure: true,
      maxAge: configuration().max_age, // day / hour / minute / second / millisecond
      // maxAge: 5 * 24 * 60 * 60 * 1000, // day / hour / minute / second / millisecond
    });

    return { email: ret.email };
  }

  @Post('change-password')
  @ApiOperation({
    summary: 'user change password',
  })
  @UseGuards(JwtAuthGuard)
  async changePassword(@Req() req: any, @Body() dto: UserChangePasswordDto) {
    return await this.userService.changePassword(req.user, dto);
  }

  @Post('generate-invitation-code')
  @ApiOperation({
    summary: 'generate invitation code',
  })
  @UseGuards(JwtAuthGuard)
  async generateInvitationCode(@Req() req: any) {
    return await this.userService.generateInvitationCode(req.user);
  }

  @Post('logout')
  @ApiOperation({
    summary: 'user logout',
  })
  async logout(@Req() req, @Res({ passthrough: true }) response: Response) {
    await this.userService.logout(req);
    response.clearCookie('ulog');
    return 'User logout successful';
  }

  @Post('register')
  @ApiOperation({
    summary: 'user register',
  })
  async register(
    @Body() dto: RegisterUserDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    let ret = await this.userService.registerUser(dto);
    response.clearCookie('ulog');
    response.cookie('ulog', ret.access_token, {
      httpOnly: true,
      sameSite: 'none',
      secure: true,
      maxAge: configuration().max_age,
    });
    return ret.email;
  }

  @Post('bind-2fa')
  @ApiOperation({
    summary: 'user bind 2fa',
  })
  @UseGuards(JwtAuthGuard)
  async bind2fa(@Body() dto: UserBind2faDto, @Req() req) {
    let ret = await this.userService.bind2fa(req.user, dto);
    return ret;
  }

  @Post('unbind-2fa')
  @ApiOperation({
    summary: 'user unbind 2fa',
  })
  @UseGuards(JwtAuthGuard)
  async unbind2fa(@Body() dto: UserUnbind2faDto, @Req() req) {
    let ret = await this.userService.unbind2fa(req.user, dto);
    return ret;
  }

  @Get('info')
  @ApiOperation({
    summary: 'user info',
  })
  @UseGuards(JwtAuthGuard)
  async userInfo(@Req() req) {
    return plainToClass(User, req.user);
  }

  @Post('prebind-2fa')
  @ApiOperation({
    summary: 'bind user 2fa',
  })
  @UseGuards(Jwt2FAAuthGuard)
  async prebind2fa(
    @Body() dto: UserBind2faDto,
    @Req() req: any,
    @Res({ passthrough: true }) response: Response,
  ) {
    let ret = await this.userService.prebind2FA(req.user, dto);
    response.clearCookie('ulog');
    response.clearCookie('u-2fa');
    response.cookie('ulog', ret.access_token, {
      httpOnly: true,
      sameSite: 'none',
      secure: true,
      maxAge: configuration().max_age,
    });
    return 'Google 2fa binded';
  }
}
