import {
  Column,
  CreateDateColumn,
  Entity,
  <PERSON>inC<PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Transaction } from 'src/transactions/entities/transaction.entity';

@Entity()
export class Trade {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  match_id: string;

  @Column()
  uuid: string; // Bot UUID

  @Column()
  bot_type: string; // GRIDBOT, ARBITRAGEBOT, etc.

  @Column({ nullable: true })
  trade_id: string; // Exchange trade ID

  @Column()
  price: string; // Actual execution price

  @Column()
  amount: string; // Executed quantity

  @Column()
  quote_amount: string; // Price * amount

  @Column()
  is_buy: boolean;

  @Column({ default: '0' })
  fee: string;

  @Column({ nullable: true })
  fee_currency: string;

  @Column()
  platform_id: string;

  @Column()
  pair_id: string;

  @Column({ type: 'timestamp' })
  trade_time: Date; // When the trade actually happened on the exchange

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
