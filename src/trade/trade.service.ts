import { BadRequestException, HttpException, Injectable } from '@nestjs/common';
import { CreateTradeDto } from './dto/create-trade.dto';
import { UpdateTradeDto } from './dto/update-trade.dto';
import { User } from 'src/user/entities/user.entity';
import { QueryTradeDto } from './dto/query-trade.dto';
import { EntityManager } from 'typeorm';
import { Trade } from './entities/trade.entity';
import { PaginateByObject } from 'common/dto/pagination.dto';
import { PageDto, PageMetaDto } from 'common/dto/pageResponse.dto';
import {
  Transaction,
  TransactionStatus,
} from 'src/transactions/entities/transaction.entity';
import { Pair } from 'src/pair/entities/pair.entity';
import Decimal from 'decimal.js';
import { Gridbot } from 'src/gridbot/entities/gridbot.entity';
import { ArbitrageBot } from 'src/arbitrage_bot/entities/arbitrage_bot.entity';

@Injectable()
export class TradeService {
  constructor(private em: EntityManager) {}

  create(createTradeDto: CreateTradeDto) {
    return 'This action adds a new trade';
  }

  async findAll(user: User, query: QueryTradeDto) {
    const matchedTradesQuery = `
    WITH parsed_matches AS (
      SELECT distinct
        transaction.matched_order,
        transaction.bot_type,
        transaction.uuid,
        SUBSTRING_INDEX(transaction.matched_order, '-', 1) AS buy_order_id,
        SUBSTRING_INDEX(transaction.matched_order, '-', -1) AS sell_order_id
        
      FROM 
        transaction
      WHERE 
        transaction.uuid = ?
        AND transaction.matched_order IS NOT NULL
        AND transaction.matched_order LIKE '%-%' 
        
    ),

    buy_trades AS (
      SELECT 
        pm.matched_order,
        t.match_id AS buy_order_id,
        SUM(t.price * t.amount)/SUM(t.amount) AS buy_price,
        SUM(t.amount) AS buy_amount,
        SUM(t.fee * t.price) AS buy_fee
      FROM 
        parsed_matches pm
      left JOIN 
        trade t ON t.match_id = pm.buy_order_id
      WHERE
        t.is_buy = TRUE group by t.match_id , pm.matched_order
    ),

    sell_trades AS (
      SELECT 
        pm.matched_order,
        t.match_id AS sell_order_id,
        SUM(t.price * t.amount)/SUM(t.amount) AS sell_price,
        SUM(t.amount) AS sell_amount,
        SUM(t.fee ) AS sell_fee
      FROM 
        parsed_matches pm
      left JOIN 
        trade t ON t.match_id = pm.sell_order_id 
      WHERE
        t.is_buy = FALSE group by t.match_id , pm.matched_order
    )

    SELECT
      pm.matched_order,
      pm.bot_type,
      pm.uuid,
      bt.buy_order_id,
      st.sell_order_id,
      CAST(bt.buy_price AS CHAR) as buy_price,
      CAST(bt.buy_amount AS CHAR) as buy_amount,
      CAST(bt.buy_fee AS CHAR)  as buy_fee,
      CAST(st.sell_price AS CHAR) as sell_price,
      CAST(st.sell_amount AS CHAR) as sell_amount,
      CAST(st.sell_fee AS CHAR) as sell_fee,
      CAST((st.sell_price * st.sell_amount) - (bt.buy_price * bt.buy_amount) AS CHAR) AS gross_pnl,
      CAST((bt.buy_fee + st.sell_fee) AS CHAR) AS total_fees,
      CAST((st.sell_price * st.sell_amount) - (bt.buy_price * bt.buy_amount) - (bt.buy_fee + st.sell_fee) AS CHAR) AS net_pnl
    FROM
    parsed_matches pm
    LEFT JOIN
        buy_trades bt ON pm.matched_order = bt.matched_order
      LEFT JOIN
        sell_trades st ON pm.matched_order = st.matched_order
      WHERE
        bt.buy_order_id IS NOT NULL
        OR st.sell_order_id IS NOT NULL
      ORDER BY
        pm.matched_order;
    
    `;

    // Execute the raw query with parameters
    let matchedTrades = await this.em.query(matchedTradesQuery, [query.uuid]);
    let itemCount, total_pnl;
    if (matchedTrades.length != 0) {
      //if bot type = grid then go to gridbot table, else go to arbitragebot table
      let bot_detail;
      if (matchedTrades[0].bot_type === 'GRIDBOT') {
        bot_detail = await this.em
          .createQueryBuilder(Gridbot, 'gb')
          .leftJoin('user_key', 'user_key', 'gb.user_key_id = user_key.id')
          .where('gb.uuid = :uuid and user_key.user_id = :user_id', {
            uuid: matchedTrades[0].uuid,
            user_id: user.id,
          })
          .withDeleted()
          .getOne();
      } else if (matchedTrades[0].bot_type === 'ARBITRAGEBOT') {
        bot_detail = await this.em
          .createQueryBuilder(ArbitrageBot, 'ab')
          .leftJoin('user_key', 'user_key', 'ab.user_key_id = user_key.id')
          .where('ab.uuid = :uuid and user_key.user_id = :user_id', {
            uuid: matchedTrades[0].uuid,
            user_id: user.id,
          })
          .withDeleted()
          .getOne();
      }
      if (!bot_detail) {
        throw new BadRequestException('Bot not found');
      }
      total_pnl = matchedTrades.reduce((acc, curr) => {
        curr.buy_price = Number(curr.buy_price).toFixed(4);
        curr.sell_price = Number(curr.sell_price).toFixed(4);
        curr.gross_pnl = Number(curr.gross_pnl).toFixed(4);
        curr.total_fees = Number(curr.total_fees).toFixed(4);
        curr.net_pnl = Number(curr.net_pnl).toFixed(4);
        return acc + Number(curr.net_pnl);
      }, 0);
      itemCount = matchedTrades.length;
      if (query.take == 0) {
        query.take = itemCount;
      }
      matchedTrades = PaginateByObject(matchedTrades, query.page, query.take);
    } else {
      const unionQuery = `
      SELECT 
        gb.uuid,
        gb.realized_profit
      FROM 
        gridbot gb
        LEFT JOIN user_key uk ON gb.user_key_id = uk.id
      WHERE 
        gb.uuid = ?
        AND uk.user_id = ?
      
      UNION
      
      SELECT 
        ab.uuid,
        ab.realized_profit
      FROM 
        arbitrage_bot ab
        LEFT JOIN user_key uk ON ab.base_key_id = uk.id
      WHERE 
        ab.uuid = ? 
        AND uk.user_id = ?
    `;

      // Execute the raw query with parameters
      const results = await this.em.query(unionQuery, [
        query.uuid,
        user.id,
        query.uuid,
        user.id,
      ]);
      if (results.length == 0) {
        throw new BadRequestException('Bot not found');
      }
      total_pnl = Number(results[0].realized_profit);
      itemCount = 0;
      console.log(results, 'results');
    }

    const pageMetaDto = new PageMetaDto({
      itemCount,
      pageOptionsDto: query,
    });
    let trades: any = {
      trades: matchedTrades,
      total_pnl: total_pnl.toFixed(4),
    };
    return new PageDto(trades, pageMetaDto);
  }

  findOne(id: number) {
    return `This action returns a #${id} trade`;
  }

  async update(updateTradeDto: UpdateTradeDto) {
    // create trade order by matching order_id
  }

  remove(id: number) {
    return `This action removes a #${id} trade`;
  }
}
