import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Req,
} from '@nestjs/common';
import { TradeService } from './trade.service';
import { CreateTradeDto } from './dto/create-trade.dto';
import { UpdateTradeDto } from './dto/update-trade.dto';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { QueryTradeDto } from './dto/query-trade.dto';
import { ApiOperation } from '@nestjs/swagger';

@Controller('trade')
@UseGuards(JwtAuthGuard)
export class TradeController {
  constructor(private readonly tradeService: TradeService) {}

  @Get('list')
  @ApiOperation({
    summary: 'Find all trades list',
  })
  async findAll(@Query() query: QueryTradeDto, @Req() req) {
    return await this.tradeService.findAll(req.user, query);
  }
}
