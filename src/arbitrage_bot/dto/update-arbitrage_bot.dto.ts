import { IsEnum, IsN<PERSON>berString, IsOptional, IsString, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ArbitBotType } from '../entities/arbitrage_bot.entity';

export class UpdateArbitrageBotDto {
  @ApiProperty({})
  @IsString()
  uuid: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false, enum: ArbitBotType })
  @IsOptional()
  @IsEnum(ArbitBotType)
  type?: ArbitBotType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumberString()
  @Matches(/^([1-9]\d*(\.\d+)?)$/, {
    message: 'Rate must be a valid number string and cannot be negative'
  })
  rate?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumberString()
  @Matches(/^([1-9]\d*(\.\d+)?)$/, {
    message: 'Percentage must be a valid number string and cannot be negative'
  })
  percentage?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumberString()
  initial_fund?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumberString()
  initial_base?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumberString()
  initial_quote?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumberString()
  initial_price?: string;

  // ADD THESE MISSING FIELDS:
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  combination1?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  combination2?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  combination3?: string;
}