import { IsN<PERSON>ber, IsNumberString, IsString, Matches, IsUUID, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class EditArbitrageBotDto {
    @ApiProperty({})
  @IsString()
  uuid: string;

  @ApiProperty({})
  @IsNumberString()
  @Matches(/^([1-9]\d*(\.\d+)?)$/, {
    message: 'Rate must be a valid number string and cannot be negative'
  })
  rate: string;

  @ApiProperty({})
  @IsNumberString()
  @Matches(/^([1-9]\d*(\.\d+)?)$/, {
    message: 'Percentage must be a valid number string and cannot be negative'
  })
  percentage: string;
}
