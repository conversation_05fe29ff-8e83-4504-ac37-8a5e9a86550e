import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum } from 'class-validator';
import { ArbitBotStatus } from '../entities/arbitrage_bot.entity';
import { PageOptionsDto } from 'common/dto/pagination.dto';

export class QueryArbitragebotDto extends PageOptionsDto {
  //Should remain without spliting base and target?
  @ApiProperty({ description: 'Arbitrage Bot UUID', required: false })
  @IsString()
  @IsOptional()
  uuid: string;

  @ApiProperty({ description: 'Platform id', required: false })
  @IsOptional()
  @IsString()
  platform_id: string;

  @ApiProperty({ description: 'Bot symbol', required: false })
  @IsOptional()
  @IsString()
  symbol: string;

  
  @ApiProperty({ description: 'User key id', required: false })
  @IsOptional()
  @IsString()
  user_key_id: string;

  @ApiProperty({
    description: 'Arbitrage bot status',
    required: false,
    enum: ArbitBotStatus,
  })
  @IsOptional()
  @IsEnum(ArbitBotStatus)
  status: ArbitBotStatus;
}
