import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class PlaceOrderArbitrageBotDto {
  @ApiProperty({ description: 'UUID of the arbitrage bot', required: true })
  @IsNotEmpty()
  @IsString()
  uuid: string;

  @ApiProperty({
    description: 'Pair ID for the target trading pair',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  target_pair_id: string;

  @ApiProperty({
    description: 'Pair ID for the base trading pair',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  base_pair_id: string;
}
