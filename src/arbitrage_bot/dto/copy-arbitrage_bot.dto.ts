import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsBooleanString,
  IsNotEmpty,
  IsNumber,
  IsString,
} from 'class-validator';

export class CopyArbitragebotDto {
  @ApiProperty({ description: 'UUID of the bot', required: true })
  @IsNotEmpty()
  @IsString()
  uuid: string;

  @ApiProperty({ description: 'name of the bot', required: true })
  @IsNotEmpty()
  @IsString()
  name: string;
}
