// update-rate-arbitrage_bot.dto.ts
import { IsNumberString, IsString, Matches, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateRateArbitrageBotDto {
  @ApiProperty({})
  @IsString()
  uuid: string;

  @ApiProperty({
    description: 'Percentage to trigger arbitrage of bot',
    example: '10',
    required: true,
  })
  @IsNumberString()
  @IsNotEmpty()
  @Matches(/^(?!0+(?:\.0+)?$)\d+(?:\.\d+)?$/, {
    message: 'Percentage must be a positive number greater than zero',
  })
  percentage: string;

  @ApiProperty({
    description:
      'Rate for the  base_platform_pair_base/target_platform_pair_base convertion',
    required: true,
  })
  @IsNumberString()
  @IsNotEmpty()
  @Matches(/^(0(\.\d+)?|[1-9]\d*(\.\d+)?)$/, {
    message: 'Rate cannot be negative number',
  })
  rate: string;
}