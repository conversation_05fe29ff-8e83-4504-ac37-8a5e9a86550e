import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsNumberString,
  IsOptional,
  IsString,
  Matches,
} from 'class-validator';
import { ArbitBotType } from '../entities/arbitrage_bot.entity';

export class CreateArbitrageBotDto {
  @ApiProperty({
    description: 'name of the bot',
    example: 'ETH-USDT Hata-Binance Arbitrage v1',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Type of arbitrage strategy the bot will employ',
    enum: ArbitBotType,
    example: ArbitBotType.MAKER,
    required: true,
  })
  @IsEnum(ArbitBotType)
  @IsNotEmpty()
  type: ArbitBotType;

  @ApiProperty({
    description:
      'Initial capital allocated for arbitrage trading in each round of base currency',
    example: '10',
    required: true,
  })
  @IsNotEmpty()
  @IsNumberString()
  initial_fund: string;

  @ApiProperty({
    description: 'Percentage to trigger arbitrage of bot',
    example: '10',
    required: true,
  })
  @IsNumberString()
  @IsNotEmpty()
  @Matches(/^(?!0+(?:\.0+)?$)\d+(?:\.\d+)?$/, {
    message: 'Percentage must be a positive number greater than zero',
  })
  percentage: string;

  @ApiProperty({
    description:
      'Rate for the  base_platform_pair_base/target_platform_pair_base convertion',
    required: true,
  })
  @IsNumberString()
  @IsNotEmpty()
  @Matches(/^(0(\.\d+)?|[1-9]\d*(\.\d+)?)$/, {
    message: 'Rate cannot be negative number',
  })
  rate: string;

  //must varify that the string able to conver to number
  @ApiProperty({
    description: 'API key ID for the base exchange platform',
    required: true,
  })
  @IsNotEmpty()
  @IsNumberString({}, { message: 'base_user_key_id must be a number' })
  base_user_key_id: string;

  @ApiProperty({
    description: 'API key ID for the target exchange platform',
    required: true,
  })
  @IsNotEmpty()
  @IsNumberString({}, { message: 'target_user_key_id must be a number' })
  target_user_key_id: string;

  @ApiProperty({
    description: 'Trading combination 1',
    required: true,
  })
  @IsNotEmpty()
  @IsString({ message: 'combination must be a pair id' })
  combination1: string;

  @ApiProperty({
    description: 'Trading combination 2',
    required: true,
  })
  @IsOptional()
  @IsString({ message: 'combination must be a pair id' })
  combination2: string;

  @ApiProperty({
    description: 'Trading combination 3',
    required: true,
  })
  @IsOptional()
  @IsString({ message: 'combination must be a pair id' })
  combination3: string;

  @ApiProperty({
    description: 'Exchange platform ID for the base trading pair',
    required: true,
  })
  @IsNotEmpty()
  @IsNumberString({}, { message: 'base_platform_id must be a number' })
  base_platform_id: string;

  @ApiProperty({
    description: 'Exchange platform ID for the target trading pair',
    required: true,
  })
  @IsNotEmpty()
  @IsNumberString({}, { message: 'target_platform_id must be a number' })
  target_platform_id: string;
}
