import {
  Column,
  CreateDate<PERSON>olumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
export enum ArbitBotStatus {
  'ACTIVE' = 'ACTIVE',
  'BUY_ACTIVE' = 'BUY_ACTIVE',
  'SELL_ACTIVE' = 'SELL_ACTIVE',
  'INACTIVE' = 'INACTIVE',
  'INSUFFICIENT' = 'INSUFFICIENT',
  'DELETED' = 'DELETED',
  'ERROR' = 'ERROR',
}

export enum ArbitBotType {
  'MAKER' = 'MAKER',
  'TAKER' = 'TAKER',
}

@Entity()
export class ArbitrageBot {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  uuid: string;

  @Column()
  type: ArbitBotType;

  @Column({ precision: 2 })
  percentage: string;

  @Column()
  rate: string;

  @Column({ default: '0' })
  fee_pct: string;

  @Column()
  initial_fund: string;

  @Column({ nullable: true })
  initial_base: string;

  @Column({ nullable: true })
  initial_quote: string;

  @Column({ nullable: true })
  initial_price: string;

  @Column({ default: '0' })
  realized_profit: string;

  @Column({ default: ArbitBotStatus.INACTIVE })
  status: ArbitBotStatus;

  @Column()
  base_key_id: string;

  @Column()
  target_key_id: string;

  @Column({ nullable: true })
  base_pair_id: string;

  @Column({ nullable: true })
  target_pair_id: string;

  @Column()
  base_platform_id: string;

  @Column()
  target_platform_id: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn()
  deleted_at: Date;
}
