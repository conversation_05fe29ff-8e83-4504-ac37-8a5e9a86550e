import { Test, TestingModule } from '@nestjs/testing';
import { ArbitrageBotController } from './arbitrage_bot.controller';
import { ArbitrageBotService } from './arbitrage_bot.service';

describe('ArbitrageBotController', () => {
  let controller: ArbitrageBotController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ArbitrageBotController],
      providers: [ArbitrageBotService],
    }).compile();

    controller = module.get<ArbitrageBotController>(ArbitrageBotController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
