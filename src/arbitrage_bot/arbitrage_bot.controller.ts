import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ArbitrageBotService } from './arbitrage_bot.service';
import { CreateArbitrageBotDto } from './dto/create-arbitrage_bot.dto';
import { UpdateArbitrageBotDto } from './dto/update-arbitrage_bot.dto';
import { CopyArbitragebotDto } from './dto/copy-arbitrage_bot.dto';
import { StartArbitragebotDto } from './dto/start-arbitrage_bot.dto';
import { QueryArbitragebotDto } from './dto/query-arbitrage_bot.dto';
import { StopArbitragebotDto } from './dto/stop-arbitrage_bot.dto';
import { DeleteArbitragebotDto } from './dto/delete-arbitrage_bot.dto';
import { EditArbitrageBotDto } from './dto/edit-arbitrage_bot.dto';
import { PlaceOrderArbitrageBotDto } from './dto/place_order-arbitrage-bot.dto';
import { JwtAuthGuard } from 'src/auth/jwt-auth.guard';
import { ApiOperation } from '@nestjs/swagger';
import { UpdateRateArbitrageBotDto } from './dto/update-rate-arbitrage_bot.dto';


@Controller('arbitrage-bot')
@UseGuards(JwtAuthGuard)
export class ArbitrageBotController {
  constructor(private readonly arbitrageBotService: ArbitrageBotService) {}

  @Post('create')
  @ApiOperation({
    summary: 'Create a new arbitrage bot',
  })
  async create(
    @Body() createArbitrageBotDto: CreateArbitrageBotDto,
    @Req() req,
  ) {
    return await this.arbitrageBotService.create(
      req.user,
      createArbitrageBotDto,
    );
  }

  @Post('copy')
  @ApiOperation({
    summary: 'Copy an existing arbitrage bot',
  })
  async copy(@Body() copyArbitrageBotDto: CopyArbitragebotDto, @Req() req) {
    return await this.arbitrageBotService.copy(req.user, copyArbitrageBotDto);
  }

  @Post('start')
  @ApiOperation({
    summary: 'Start an arbitrage bot',
  })
  async start(@Body() startArbitrageBotDto: StartArbitragebotDto, @Req() req) {
    return await this.arbitrageBotService.start(req.user, startArbitrageBotDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Find all arbitrage bots',
  })
  async findAll(@Query() query: QueryArbitragebotDto, @Req() req) {
    return await this.arbitrageBotService.findAll(req.user, query);
  }

  @Post('stop')
  @ApiOperation({
    summary: 'Stop an arbitrage bot',
  })
  async stop(@Req() req: any, @Body() body: StopArbitragebotDto) {
    return this.arbitrageBotService.stop(req.user, body);
  }

  @Post('edit')
  @ApiOperation({
    summary: 'Edit an arbitrage bot',
  })
  async edit(@Body() dto: EditArbitrageBotDto, @Req() req) {
    return await this.arbitrageBotService.update(req.user, dto);
  }

  @Get('get-price-volume')
  @ApiOperation({
    summary: 'Get price and volume for an arbitrage bot',
  })
  async getPairPriceAndVolume(
    @Query() startArbitrageBotDto: StartArbitragebotDto,
    @Req() req,
  ) {
    return await this.arbitrageBotService.getPairPriceAndVolume(
      req.user,
      startArbitrageBotDto,
    );
  }

  @Post('delete')
  @ApiOperation({
    summary: 'Delete an arbitrage bot',
  })
  async remove(
    @Req() req: any,
    @Body() deleteArbitrageBotDto: DeleteArbitragebotDto,
  ) {
    return await this.arbitrageBotService.remove(
      req.user,
      deleteArbitrageBotDto,
    );
  }

  @Post('update')
  @ApiOperation({
    summary: 'Update an arbitrage bot',
  })
  async update(
    @Req() req: any,
    @Body() updateArbitrageBotDto: UpdateArbitrageBotDto,
  ) {
    return await this.arbitrageBotService.update(
      req.user,
      updateArbitrageBotDto,
    );
  }

  @Post('update-rate')
  @ApiOperation({
    summary: 'Update rate and percentage of an active arbitrage bot',
  })
  async updateRate(
    @Req() req: any,
    @Body() updateRateDto: UpdateRateArbitrageBotDto,
  ) {
    return await this.arbitrageBotService.updateActiveBotRate(
      req.user,
      updateRateDto,
    );
  }

  @Post('place-order')
  @ApiOperation({
    summary: 'Place an order for arbitrage bot',
  })
  async placeOrder(
    @Req() req: any,
    @Body() placeOrderArbitrageBotDto: PlaceOrderArbitrageBotDto,
  ) {
    return await this.arbitrageBotService.checkArbitrageBot(
      req.user,
      placeOrderArbitrageBotDto,
    );
  }

  @Get('check-balance')
  @ApiOperation({
    summary: 'Check balance for arbitrage bot',
  })
  async checkBalance(
    @Req() req: any,
    @Query() startArbitrageBotDto: StartArbitragebotDto,
  ) {
    return await this.arbitrageBotService.checkArbitrageBalance(
      req.user,
      startArbitrageBotDto,
    );
  }
}
