import { BadRequestException, Injectable, OnModuleInit } from '@nestjs/common';
import { CreateArbitrageBotDto } from './dto/create-arbitrage_bot.dto';
import { UpdateArbitrageBotDto } from './dto/update-arbitrage_bot.dto';
import { UserKey } from 'src/user_key/entities/user_key.entity';
import { User } from 'src/user/entities/user.entity';
import { EntityManager } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import {
  ArbitBotStatus,
  ArbitrageBot,
  ArbitBotType,
} from './entities/arbitrage_bot.entity';
import { Pair } from 'src/pair/entities/pair.entity';
import { Platform } from 'src/platform/entities/platform.entity';
import {
  calculateConstBaseAndQuote,
  decrypted,
  generateSecret,
  validateNotional,
} from 'common/util/utils';
import { callPlatformHelper } from 'common/helper/platform-base.helper';
import configuration from 'config/configuration';
import { CopyArbitragebotDto } from './dto/copy-arbitrage_bot.dto';
import { CustomErrorMessages } from 'common/helper/errorMessage';
import { QueryArbitragebotDto } from './dto/query-arbitrage_bot.dto';
import { Transaction } from 'src/transactions/entities/transaction.entity';
import { StartArbitragebotDto } from './dto/start-arbitrage_bot.dto';
import Decimal from 'decimal.js';
import { StopArbitragebotDto } from './dto/stop-arbitrage_bot.dto';
import { DeleteArbitragebotDto } from './dto/delete-arbitrage_bot.dto';
import { EditArbitrageBotDto } from './dto/edit-arbitrage_bot.dto';
import { UserKeyValidatorHelper } from 'common/helper/user-key-validator.helper';
import { PaginateByObject } from 'common/dto/pagination.dto';
import { PageDto, PageMetaDto } from 'common/dto/pageResponse.dto';
import { Cron } from '@nestjs/schedule';
import { CreateBotEventQueueService } from 'src/queue/create-bot-event-queue.service';
import { PlaceOrderArbitrageBotDto } from './dto/place_order-arbitrage-bot.dto';
import Redis from 'ioredis';
import { ArbitragePair } from './entities/arbitrage_pair.entity';
import { UpdateRateArbitrageBotDto } from './dto/update-rate-arbitrage_bot.dto';
import { PinoLogger } from 'nestjs-pino';

@Injectable()
export class ArbitrageBotService implements OnModuleInit {
  private redisClient: Redis;
  constructor(
    private readonly em: EntityManager,
    @InjectQueue('start-arbit')
    private startArbitrageQueue: Queue,
    @InjectQueue('stop-arbit')
    private stopArbitrageQueue: Queue,
    @InjectQueue('arbit-checking')
    private checkingArbitrageQueue: Queue,
    @InjectQueue('create-event')
    private createEventQueue: Queue,
    private readonly pinoLogger: PinoLogger,
  ) {
    this.redisClient = new Redis({
      host: configuration().redis.host,
      password: configuration().redis.password,
    });
  }

  onModuleInit() {
    this.pinoLogger.info('Starting background arbitrage service...');
  }

  async create(user: User, createArbitragebotDto: CreateArbitrageBotDto) {
    let { base, target } = await this.validatePair(
      createArbitragebotDto.combination1,
      createArbitragebotDto.base_platform_id,
      createArbitragebotDto.target_platform_id,
    );
    // the third bot seems like not use, overrided in later
    // why 7 pormise but 5 variable
    let [base_user_key, target_user_key, bot, base_platform, target_platform] =
      await Promise.all([
        this.em
          .createQueryBuilder(UserKey, 'uk')
          .where(
            'uk.id = :id and uk.user_id = :user_id and uk.platform_id = :platform_id',
            {
              id: createArbitragebotDto.base_user_key_id,
              user_id: user.id,
              platform_id: createArbitragebotDto.base_platform_id,
            },
          )
          .getOne(),
        this.em
          .createQueryBuilder(UserKey, 'uk')
          .where(
            'uk.id = :id and uk.user_id = :user_id and uk.platform_id = :platform_id',
            {
              id: createArbitragebotDto.target_user_key_id,
              user_id: user.id,
              platform_id: createArbitragebotDto.target_platform_id,
            },
          )
          .getOne(),
        this.em
          .createQueryBuilder(ArbitrageBot, 'ab')
          .where('ab.name = :name', { name: createArbitragebotDto.name })
          .getOne(),
        this.em
          .createQueryBuilder(Pair, 'p')
          .where('p.id = :id and p.platform_id = :platform_id', {
            id: base.id,
            platform_id: createArbitragebotDto.base_platform_id,
          })
          .getOne(),
        this.em
          .createQueryBuilder(Pair, 'p')
          .where('p.id = :id and p.platform_id = :platform_id', {
            id: target.id,
            platform_id: createArbitragebotDto.target_platform_id,
          })
          .getOne(),
        this.em
          .createQueryBuilder(Platform, 'p')
          .where('p.id = :id', { id: createArbitragebotDto.base_platform_id })
          .getOne(),
        this.em
          .createQueryBuilder(Platform, 'p')
          .where('p.id = :id', { id: createArbitragebotDto.target_platform_id })
          .getOne(),
      ]);

    if (!base_user_key || !target_user_key) {
      this.pinoLogger.error(
        { base_user_key, target_user_key },
        'Invalid user keys in create',
      );
      throw new BadRequestException(
        CustomErrorMessages.USER_KEY.INVALID_USERKEY,
      );
    }

    await new UserKeyValidatorHelper(this.em).validateMultipleUserKeys(
      [base_user_key.id, target_user_key.id],
      'create arbitrage bot',
    );

    if (base.id === target.id) {
      this.pinoLogger.error({ base, target }, 'Same pair used in create');
      throw new BadRequestException(CustomErrorMessages.BOT.SAME_PAIR_USED);
    }
    if (base.base !== target.base) {
      this.pinoLogger.error({ base, target }, 'Base asset mismatch in create');
      throw new BadRequestException(
        CustomErrorMessages.BOT.BASE_ASSET_MISMATCH,
      );
    }

    // Prevent same pair used across bots
    // const activeSamePairBot = await this.em
    //   .createQueryBuilder(ArbitrageBot, 'ab')
    //   .where(
    //     'ab.status = :status AND (ab.base_pair_id = :bp OR ab.target_pair_id = :tp)',
    //     {
    //       status: ArbitBotStatus.ACTIVE,
    //       bp: base_pair.id,
    //       tp: target_pair.id,
    //     },
    //   )
    //   .getOne();
    // if (activeSamePairBot) {
    //   throw new BadRequestException(
    //     CustomErrorMessages.BOT.PAIR_ALREADY_ACTIVE,
    //   );
    // }

    bot = new ArbitrageBot();
    bot.name = createArbitragebotDto.name;
    bot.uuid = (await generateSecret()) + Date.now();
    // bot.base_pair_id = base_pair.id.toString();
    bot.base_platform_id = base.platform_id.toString();
    bot.base_key_id = base_user_key.id.toString();
    // bot.target_pair_id = target_pair.id.toString();
    bot.target_platform_id = target.platform_id.toString();
    bot.target_key_id = target_user_key.id.toString();
    bot.type = createArbitragebotDto.type;
    bot.rate = createArbitragebotDto.rate;
    bot.percentage = createArbitragebotDto.percentage; //validate not less than 0
    bot.initial_fund = createArbitragebotDto.initial_fund;
    try {
      await this.em.save(ArbitrageBot, bot);
      await this.createArbitragePair(
        bot.uuid,
        base.id.toString(),
        target.id.toString(),
      );
      if (createArbitragebotDto.combination2) {
        let { base, target } = await this.validatePair(
          createArbitragebotDto.combination2,
          createArbitragebotDto.base_platform_id,
          createArbitragebotDto.target_platform_id,
        );
        await this.createArbitragePair(
          bot.uuid,
          base.id.toString(),
          target.id.toString(),
        );
      }
      if (createArbitragebotDto.combination3) {
        let { base, target } = await this.validatePair(
          createArbitragebotDto.combination3,
          createArbitragebotDto.base_platform_id,
          createArbitragebotDto.target_platform_id,
        );
        await this.createArbitragePair(
          bot.uuid,
          base.id.toString(),
          target.id.toString(),
        );
      }
      await this.createEventQueue.add('processing', {
        botType: 'arbitragebot',
        uuid: bot.uuid,
        eventType: CreateBotEventQueueService.EVENT_TYPES.BOT_CREATED,
        params: [],
      });

      return bot;
    } catch (error) {
      this.pinoLogger.error({ error }, 'Error in create');
      console.log(error);
      throw new BadRequestException(error.message);
    }
  }

  async copy(user: User, copyArbitragebotDto: CopyArbitragebotDto) {
    const originalBot = await this.em
      .createQueryBuilder(ArbitrageBot, 'ab')
      .leftJoin(
        'user_key',
        'base_user_key',
        'ab.base_key_id = base_user_key.id',
      )
      .leftJoin('user', 'user', 'base_user_key.user_id = user.id')
      .where('ab.uuid = :uuid and user.id = :user_id', {
        uuid: copyArbitragebotDto.uuid,
        user_id: user.id,
      })
      .withDeleted()
      .getOne();
    if (!originalBot) {
      this.pinoLogger.error({ copyArbitragebotDto }, 'Invalid UUID in copy');
      throw new BadRequestException(CustomErrorMessages.BOT.INVALID_UUID);
    }

    await new UserKeyValidatorHelper(this.em).validateMultipleUserKeys(
      [originalBot.base_key_id, originalBot.target_key_id],
      'copy arbitrage bot',
    );

    const newBot = this.em.create(ArbitrageBot, {
      name: copyArbitragebotDto.name,
      uuid: (await generateSecret()) + Date.now(),
      base_pair_id: originalBot.base_pair_id,
      base_platform_id: originalBot.base_platform_id,
      base_key_id: originalBot.base_key_id,
      target_pair_id: originalBot.target_pair_id,
      target_platform_id: originalBot.target_platform_id,
      target_key_id: originalBot.target_key_id,
      rate: originalBot.rate,
      percentage: originalBot.percentage,
      type: originalBot.type,
      initial_fund: originalBot.initial_fund,
      status: ArbitBotStatus.INACTIVE,
    });
    try {
      return await this.em.save(ArbitrageBot, newBot);
    } catch (error) {
      this.pinoLogger.error(
        { error, copyArbitragebotDto },
        'Error saving new bot in copy',
      );
      throw new BadRequestException(error.message);
    }
  }

  async findAll(user: User, query: QueryArbitragebotDto) {
    // let queryBuilder = this.em
    //   .createQueryBuilder(ArbitrageBot, 'ab')
    //   .leftJoin(
    //     'user_key',
    //     'base_user_key',
    //     'ab.base_key_id = base_user_key.id',
    //   )
    //   .leftJoin('user', 'base_user', 'base_user_key.user_id = base_user.id')
    //   .leftJoin(
    //     'platform',
    //     'base_platform',
    //     'ab.base_platform_id = base_platform.id',
    //   )

    //   .leftJoin(
    //     'user_key',
    //     'target_user_key',
    //     'ab.target_key_id = target_user_key.id',
    //   )
    //   .leftJoin(
    //     'user',
    //     'target_user',
    //     'target_user_key.user_id = target_user.id',
    //   )
    //   .leftJoin(
    //     'platform',
    //     'target_platform',
    //     'ab.target_platform_id = target_platform.id',
    //   )

    //   .where('base_user.id = :user_id', { user_id: user.id });

    // if (query.platform_id) {
    //   queryBuilder.andWhere(
    //     'ab.base_platform_id = :platform_id or ab.target_platform_id = :platform_id',
    //     {
    //       platform_id: query.platform_id,
    //     },
    //   );
    // }
    // if (query.uuid) {
    //   queryBuilder.andWhere('ab.uuid = :uuid', {
    //     uuid: query.uuid,
    //   });
    // }
    // if (query.symbol) {
    //   queryBuilder.andWhere(
    //     'base_pair.symbol = :symbol or target_pair.symbol = :symbol',
    //     { symbol: query.symbol },
    //   );
    // }
    // if (query.user_key_id) {
    //   queryBuilder.andWhere(
    //     'ab.base_key_id = :user_key_id or ab.target_key_id = :user_key_id ',
    //     {
    //       user_key_id: query.user_key_id,
    //     },
    //   );
    // }
    // if (query.status) {
    //   query.status =
    //     query.status.includes('ACTIVE') && !query.status.includes('INACTIVE')
    //       ? ArbitBotStatus.ACTIVE
    //       : query.status;
    //   queryBuilder.andWhere('ab.status in (:...status)', {
    //     status:
    //       query.status === 'ACTIVE'
    //         ? ['ACTIVE']
    //         : // ? ['ACTIVE', 'BUY_ACTIVE', 'SELL_ACTIVE']
    //           [query.status],
    //   });
    // }
    // let arbitrageBot = await queryBuilder
    //   .select([
    //     'ab.id as id',
    //     'ab.name as name',
    //     'ab.uuid as uuid',
    //     'ab.realized_profit as realized_profit',
    //     'ab.base_key_id as base_key_id',
    //     'ab.initial_base as initial_base',
    //     'ab.initial_quote as initial_quote',
    //     'ab.initial_price as initial_price',
    //     'ab.fee_pct as fee_pct',
    //     'ab.target_key_id as target_key_id',
    //     'ab.percentage as percentage',
    //     'ab.rate as rate',
    //     'ab.type as type',
    //     'ab.initial_fund as initial_fund',
    //     `CASE
    //       WHEN (ab.status LIKE '%ACTIVE%' AND ab.status  != 'INACTIVE') THEN 'ACTIVE'
    //       ELSE ab.status
    //     END AS status`,
    //     `ab.created_at as created_at`,
    //     `ab.updated_at as updated_at`,
    //     `ab.deleted_at as deleted_at`,
    //   ])
    //   .addSelect(
    //     `JSON_OBJECT(
    //     'id', base_platform.id,
    //     'name', base_platform.name,
    //     'image', base_platform.image
    //   )`,
    //     'base_platform',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //     'id', target_platform.id,
    //     'name', target_platform.name,
    //     'image', target_platform.image
    //   )`,
    //     'target_platform',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //   'id', base_user_key.id,
    //   'name', base_user_key.name,
    //   'api_key', base_user_key.api_key,
    //   'status', base_user_key.status
    // )`,
    //     'base_user_key',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //   'id', target_user_key.id,
    //   'name', target_user_key.name,
    //   'api_key', target_user_key.api_key,
    //   'status', target_user_key.status
    // )`,
    //     'target_user_key',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //     'id', base_pair.id,
    //     'symbol', base_pair.symbol,
    //     'price', base_pair.price
    //   )`,
    //     'base_pair',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //     'id', target_pair.id,
    //     'symbol', target_pair.symbol,
    //     'price', target_pair.price
    //   )`,
    //     'target_pair',
    //   )
    //   .orderBy('ab.updated_at', query.order)
    //   .withDeleted()
    //   .getRawMany();
    // let itemCount = arbitrageBot.length;
    // arbitrageBot = PaginateByObject(arbitrageBot, query.page, query.take);
    // const pageMetaDto = new PageMetaDto({
    //   itemCount,
    //   pageOptionsDto: query,
    // });
    // return new PageDto(arbitrageBot, pageMetaDto);
    let queryBuilder = this.em
      .createQueryBuilder(ArbitrageBot, 'ab')
      .leftJoin(
        'arbitrage_pair',
        'ap',
        'CONVERT(ap.uuid USING utf8mb4) COLLATE utf8mb4_general_ci = CONVERT(ab.uuid USING utf8mb4) COLLATE utf8mb4_general_ci',
      )
      .leftJoin('pair', 'base_pair', 'base_pair.id = ap.base_id')
      .leftJoin('pair', 'target_pair', 'target_pair.id = ap.target_id')
      .leftJoin(
        'user_key',
        'base_user_key',
        'ab.base_key_id = base_user_key.id',
      )
      .leftJoin('user', 'base_user', 'base_user_key.user_id = base_user.id')
      .leftJoin(
        'platform',
        'base_platform',
        'ab.base_platform_id = base_platform.id',
      )
      .leftJoin(
        'user_key',
        'target_user_key',
        'ab.target_key_id = target_user_key.id',
      )
      .leftJoin(
        'user',
        'target_user',
        'target_user_key.user_id = target_user.id',
      )
      .leftJoin(
        'platform',
        'target_platform',
        'ab.target_platform_id = target_platform.id',
      )
      .where('base_user.id = :user_id', { user_id: user.id })
      // .andWhere('base_pair.is_arbit = 1 AND target_pair.is_arbit = 1');

    if (query.platform_id) {
      queryBuilder.andWhere(
        'ab.base_platform_id = :platform_id or ab.target_platform_id = :platform_id',
        { platform_id: query.platform_id },
      );
    }
    if (query.uuid) {
      queryBuilder.andWhere('ab.uuid = :uuid', { uuid: query.uuid });
    }
    if (query.symbol) {
      queryBuilder.andWhere(
        'base_pair.symbol = :symbol or target_pair.symbol = :symbol',
        { symbol: query.symbol },
      );
    }
    if (query.user_key_id) {
      queryBuilder.andWhere(
        'ab.base_key_id = :user_key_id or ab.target_key_id = :user_key_id',
        { user_key_id: query.user_key_id },
      );
    }
    if (query.status) {
      query.status =
        query.status.includes('ACTIVE') && !query.status.includes('INACTIVE')
          ? ArbitBotStatus.ACTIVE
          : query.status;
      queryBuilder.andWhere('ab.status in (:...status)', {
        status: query.status === 'ACTIVE' ? ['ACTIVE'] : [query.status],
      });
    }
    
    const rawData = await queryBuilder
      .select([
        'ab.id as ab_id',
        'ab.name as ab_name',
        'ab.uuid as ab_uuid',
        'ab.realized_profit as ab_realized_profit',
        'ab.base_key_id as ab_base_key_id',
        'ab.initial_base as ab_initial_base',
        'ab.initial_quote as ab_initial_quote',
        'ab.initial_price as ab_initial_price',
        'ab.target_key_id as ab_target_key_id',
        'ab.percentage as ab_percentage',
        'ab.rate as ab_rate',
        'ab.type as ab_type',
        'ab.initial_fund as ab_initial_fund',
        `CASE 
      WHEN (ab.status LIKE '%ACTIVE%' AND ab.status != 'INACTIVE') THEN 'ACTIVE'
      ELSE ab.status
    END AS status`,
        'ab.created_at as ab_created_at',
        'ab.updated_at as ab_updated_at',
        'ab.deleted_at as ab_deleted_at',
      ])
      .addSelect(
        `JSON_OBJECT('id', base_platform.id, 'name', base_platform.name, 'image', base_platform.image)`,
        'base_platform',
      )
      .addSelect(
        `JSON_OBJECT('id', target_platform.id, 'name', target_platform.name, 'image', target_platform.image)`,
        'target_platform',
      )
      .addSelect(
        `JSON_OBJECT('id', base_user_key.id, 'name', base_user_key.name, 'api_key', base_user_key.api_key, 'status', base_user_key.status,'taker_fee',base_user_key.taker_fee,'maker_fee',base_user_key.maker_fee)`,
        'base_user_key',
      )
      .addSelect(
        `JSON_OBJECT('id', target_user_key.id, 'name', target_user_key.name, 'api_key', target_user_key.api_key, 'status', target_user_key.status,'taker_fee',target_user_key.taker_fee,'maker_fee',target_user_key.maker_fee)`,
        'target_user_key',
      )
      .addSelect('ap.id as ap_id')
      .addSelect('base_pair.id as base_pair_id')
      .addSelect('base_pair.symbol as base_pair_symbol')
      .addSelect('base_pair.price as base_pair_price')
      .addSelect('base_pair.platform_id as base_pair_platform_id')
      .addSelect('target_pair.id as target_pair_id')
      .addSelect('target_pair.symbol as target_pair_symbol')
      .addSelect('target_pair.price as target_pair_price')
      .addSelect('target_pair.platform_id as target_pair_platform_id')
      .orderBy('ab.updated_at', query.order)
      .withDeleted()
      .getRawMany();

    // Grouping post-process
    const botMap = {};
    rawData.forEach((row) => {
      if (!botMap[row.ab_uuid]) {
        botMap[row.ab_uuid] = {
          id: row.ab_id,
          name: row.ab_name,
          uuid: row.ab_uuid,
          realized_profit: row.ab_realized_profit,
          base_key_id: row.ab_base_key_id,
          target_key_id: row.ab_target_key_id,
          percentage: row.ab_percentage,
          rate: row.ab_rate,
          type: row.ab_type,
          status: row.status,
          initial_base: row.ab_initial_base,
          initial_quote: row.ab_initial_quote,
          initial_price: row.ab_initial_price,
          initial_fund: row.ab_initial_fund,
          created_at: row.ab_created_at,
          updated_at: row.ab_updated_at,
          deleted_at: row.ab_deleted_at,
          base_platform: row.base_platform,
          target_platform: row.target_platform,
          base_user_key: row.base_user_key,
          target_user_key: row.target_user_key,
          combinations: [],
        };
      }

      if (row.ap_id) {
        botMap[row.ab_uuid].combinations.push({
          id: row.ap_id,
          base_pair: {
            pair_id: row.base_pair_id,
            symbol: row.base_pair_symbol,
            price: row.base_pair_price,
            platform_id: row.base_pair_platform_id,
          },
          target_pair: {
            pair_id: row.target_pair_id,
            symbol: row.target_pair_symbol,
            price: row.target_pair_price,
            platform_id: row.target_pair_platform_id,
          },
        });
      }
    });

    const arbitrageBot = Object.values(botMap);

    let itemCount = arbitrageBot.length;
    const paginated = PaginateByObject(arbitrageBot, query.page, query.take);
    const pageMetaDto = new PageMetaDto({
      itemCount,
      pageOptionsDto: query,
    });
    try {
      return new PageDto(paginated, pageMetaDto);
    } catch (error) {
      this.pinoLogger.error({ error, user, query }, 'Error in findAll');
      throw new BadRequestException(error.message);
    }
  }

  async start(user: User, body: StartArbitragebotDto) {
    //check if have existing user have active same pair.

    // let queryBuilder = this.em
    //   .createQueryBuilder(ArbitrageBot, 'ab')
    //   .leftJoin(
    //     'user_key',
    //     'base_user_key',
    //     'ab.base_key_id = base_user_key.id',
    //   )
    //   .leftJoin(
    //     'user_key',
    //     'target_user_key',
    //     'ab.target_key_id = target_user_key.id',
    //   )
    //   .leftJoin('user', 'user', 'base_user_key.user_id = user.id')
    //   .leftJoin(
    //     'platform',
    //     'base_platform',
    //     'ab.base_platform_id = base_platform.id',
    //   )
    //   .leftJoin(
    //     'platform',
    //     'target_platform',
    //     'ab.target_platform_id = target_platform.id',
    //   )
    //   .leftJoin('pair', 'base_pair', 'ab.base_pair_id = base_pair.id ')
    //   .leftJoin('pair', 'target_pair', 'ab.target_pair_id = target_pair.id ')
    //   .where(
    //     "user.id = :user_id and ab.uuid = :uuid and ab.status = 'INACTIVE'",
    //     {
    //       user_id: user.id,
    //       uuid: body.uuid,
    //     },
    //   );

    // let arbitrageBot = await queryBuilder
    //   .select([
    //     'ab.id as id',
    //     'ab.name as name',
    //     'ab.uuid as uuid',
    //     'ab.percentage as percentage',
    //     'ab.rate as rate',
    //     'ab.type as type',
    //     'ab.initial_fund as initial_fund',
    //     'ab.status as status',
    //   ])
    //   .addSelect(
    //     `JSON_OBJECT(
    //   'id', base_platform.id,
    //   'name', base_platform.name
    // )`,
    //     'base_platform',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //   'id', target_platform.id,
    //   'name', target_platform.name
    // )`,
    //     'target_platform',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //   'id', base_user_key.id,
    //   'api_key', base_user_key.api_key,
    //   'api_secret', base_user_key.api_secret,
    //   'taker_fee', base_user_key.taker_fee,
    //   'maker_fee', base_user_key.maker_fee
    // )`,
    //     'base_user_key',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //   'id', target_user_key.id,
    //   'api_key', target_user_key.api_key,
    //   'api_secret', target_user_key.api_secret,
    //   'taker_fee', target_user_key.taker_fee,
    //   'maker_fee', target_user_key.maker_fee
    // )`,
    //     'target_user_key',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //   'id', base_pair.id,
    //   'symbol', base_pair.symbol,
    //   'min_notional', base_pair.min_notional,
    //   'price_dp', base_pair.price_dp,
    //   'qty_dp', base_pair.qty_dp,
    //   'base', base_pair.base,
    //   'quote', base_pair.quote,
    //   'tick', CAST(base_pair.tick AS CHAR),
    //   'step', CAST(base_pair.step AS CHAR),
    //   'symbol', base_pair.symbol
    // )`,
    //     'base_pair',
    //   )
    //   .addSelect(
    //     `JSON_OBJECT(
    //   'id', target_pair.id,
    //   'symbol', target_pair.symbol,
    //   'min_notional', target_pair.min_notional,
    //   'price_dp', target_pair.price_dp,
    //   'qty_dp', target_pair.qty_dp,
    //   'base', target_pair.base,
    //   'quote', target_pair.quote,
    //   'tick', CAST(target_pair.tick AS CHAR),
    //   'step', CAST(target_pair.step AS CHAR),
    //   'symbol', target_pair.symbol
    // )`,
    //     'target_pair',
    //   )
    //   .getRawOne();
    // if (!arbitrageBot) {
    //   throw new BadRequestException(CustomErrorMessages.BOT.INVALID_STATUS);
    // }
    // await new UserKeyValidatorHelper(this.em).validateMultipleUserKeys(
    //   [arbitrageBot.base_user_key.id, arbitrageBot.target_user_key.id],
    //   'start arbitrage bot',
    // );

    // if (arbitrageBot.type === 'MAKER') {
    //   if (
    //     new Decimal(arbitrageBot.target_user_key.maker_fee)
    //       .add(arbitrageBot.base_user_key.maker_fee)
    //       .greaterThan(arbitrageBot.percentage)
    //   ) {
    //     throw new BadRequestException(
    //       'Profit percentage is insufficient to cover fee',
    //     );
    //   }
    //   throw new BadRequestException(
    //     'Maker arbitrage is not ready at the moment, please use taker bot instead',
    //   );
    // }
    // if (
    //   new Decimal(arbitrageBot.target_user_key.taker_fee)
    //     .add(arbitrageBot.base_user_key.taker_fee)
    //     .greaterThan(arbitrageBot.percentage)
    // ) {
    //   throw new BadRequestException(
    //     'Profit percentage is insufficient to cover fee',
    //   );
    // }
    // await this.startArbitrageQueue.add('init-arbit', arbitrageBot);
    const rawData = await this.em
      .createQueryBuilder(ArbitrageBot, 'ab')
      .leftJoin(
        'arbitrage_pair',
        'ap',
        'CONVERT(ap.uuid USING utf8mb4) COLLATE utf8mb4_general_ci = CONVERT(ab.uuid USING utf8mb4) COLLATE utf8mb4_general_ci',
      )
      .leftJoin('pair', 'base_pair', 'base_pair.id = ap.base_id')
      .leftJoin('pair', 'target_pair', 'target_pair.id = ap.target_id')
      .leftJoin(
        'user_key',
        'base_user_key',
        'ab.base_key_id = base_user_key.id',
      )
      .leftJoin(
        'user_key',
        'target_user_key',
        'ab.target_key_id = target_user_key.id',
      )
      .leftJoin('user', 'user', 'base_user_key.user_id = user.id')
      .leftJoin(
        'platform',
        'base_platform',
        'ab.base_platform_id = base_platform.id',
      )
      .leftJoin(
        'platform',
        'target_platform',
        'ab.target_platform_id = target_platform.id',
      )
      .where(
        'user.id = :user_id and ab.uuid = :uuid and ab.status = "INACTIVE"',
        {
          user_id: user.id,
          uuid: body.uuid,
        },
      )
      .select([
        'ab.id as ab_id',
        'ab.name as ab_name',
        'ab.uuid as ab_uuid',
        'ab.percentage as ab_percentage',
        'ab.rate as ab_rate',
        'ab.type as ab_type',
        'ab.initial_fund as ab_initial_fund',
        'ab.status as ab_status',
      ])
      .addSelect(
        `JSON_OBJECT('id', base_platform.id, 'name', base_platform.name)`,
        'base_platform',
      )
      .addSelect(
        `JSON_OBJECT('id', target_platform.id, 'name', target_platform.name)`,
        'target_platform',
      )
      .addSelect(
        `JSON_OBJECT('id', base_user_key.id, 'api_key', base_user_key.api_key, 'api_secret', base_user_key.api_secret, 'taker_fee', base_user_key.taker_fee, 'maker_fee', base_user_key.maker_fee)`,
        'base_user_key',
      )
      .addSelect(
        `JSON_OBJECT('id', target_user_key.id, 'api_key', target_user_key.api_key, 'api_secret', target_user_key.api_secret, 'taker_fee', target_user_key.taker_fee, 'maker_fee', target_user_key.maker_fee)`,
        'target_user_key',
      )
      .addSelect('ap.id as ap_id')
      .addSelect('base_pair.id as base_pair_id')
      .addSelect('base_pair.symbol as base_pair_symbol')
      .addSelect('base_pair.min_notional as base_pair_min_notional')
      .addSelect('base_pair.price_dp as base_pair_price_dp')
      .addSelect('base_pair.qty_dp as base_pair_qty_dp')
      .addSelect('base_pair.base as base_pair_base')
      .addSelect('base_pair.quote as base_pair_quote')
      .addSelect('base_pair.tick as base_pair_tick')
      .addSelect('base_pair.step as base_pair_step')
      .addSelect('target_pair.id as target_pair_id')
      .addSelect('target_pair.symbol as target_pair_symbol')
      .addSelect('target_pair.min_notional as target_pair_min_notional')
      .addSelect('target_pair.price_dp as target_pair_price_dp')
      .addSelect('target_pair.qty_dp as target_pair_qty_dp')
      .addSelect('target_pair.base as target_pair_base')
      .addSelect('target_pair.quote as target_pair_quote')
      .addSelect('target_pair.tick as target_pair_tick')
      .addSelect('target_pair.step as target_pair_step')
      .getRawMany();

    if (rawData.length === 0) {
      this.pinoLogger.error({ user, body }, 'Invalid status in start');
      throw new BadRequestException(CustomErrorMessages.BOT.INVALID_STATUS);
    }

    // rawData is joining table and get array, first array is arbitrage bot info
    const botRow = rawData[0];

    const arbitrageBot = {
      uuid: botRow.ab_uuid,
      base_platform:
        typeof botRow.base_platform === 'string'
          ? JSON.parse(botRow.base_platform)
          : botRow.base_platform,
      target_platform:
        typeof botRow.target_platform === 'string'
          ? JSON.parse(botRow.target_platform)
          : botRow.target_platform,
      base_user_key:
        typeof botRow.base_user_key === 'string'
          ? JSON.parse(botRow.base_user_key)
          : botRow.base_user_key,
      target_user_key:
        typeof botRow.target_user_key === 'string'
          ? JSON.parse(botRow.target_user_key)
          : botRow.target_user_key,
      type: botRow.ab_type,
      percentage: botRow.ab_percentage,
      combinations: [],
    };

    // Group combinations
    rawData.forEach((row) => {
      if (row.ap_id) {
        arbitrageBot.combinations.push({
          id: row.ap_id,
          base_pair: {
            id: row.base_pair_id,
            symbol: row.base_pair_symbol,
            min_notional: row.base_pair_min_notional,
            price_dp: row.base_pair_price_dp,
            qty_dp: row.base_pair_qty_dp,
            base: row.base_pair_base,
            quote: row.base_pair_quote,
            tick: row.base_pair_tick,
            step: row.base_pair_step,
          },
          target_pair: {
            id: row.target_pair_id,
            symbol: row.target_pair_symbol,
            min_notional: row.target_pair_min_notional,
            price_dp: row.target_pair_price_dp,
            qty_dp: row.target_pair_qty_dp,
            base: row.target_pair_base,
            quote: row.target_pair_quote,
            tick: row.target_pair_tick,
            step: row.target_pair_step,
          },
        });
      }
    });

    // Validate user keys
    await new UserKeyValidatorHelper(this.em).validateMultipleUserKeys(
      [arbitrageBot.base_user_key.id, arbitrageBot.target_user_key.id],
      'start arbitrage bot',
    );

    // Fee checks
    if (arbitrageBot.type === 'MAKER') {
      if (
        new Decimal(arbitrageBot.target_user_key.maker_fee)
          .add(arbitrageBot.base_user_key.maker_fee)
          .greaterThan(arbitrageBot.percentage)
      ) {
        this.pinoLogger.error(
          { arbitrageBot },
          'Profit percentage insufficient for maker fee in start',
        );
        throw new BadRequestException(
          'Profit percentage is insufficient to cover fee',
        );
      }
      this.pinoLogger.error(
        { arbitrageBot },
        'Maker arbitrage not ready in start',
      );
      throw new BadRequestException(
        'Maker arbitrage is not ready at the moment, please use taker bot instead',
      );
    }

    if (
      new Decimal(arbitrageBot.target_user_key.taker_fee)
        .add(arbitrageBot.base_user_key.taker_fee)
        .greaterThan(arbitrageBot.percentage)
    ) {
      throw new BadRequestException(
        'Profit percentage is insufficient to cover fee',
      );
    }

    // Prepare response object for queue
    const responseForQueue = {
      uuid: arbitrageBot.uuid,
      base_platform: arbitrageBot.base_platform,
      target_platform: arbitrageBot.target_platform,
    };

    // use back kraken's method 
    arbitrageBot.combinations.forEach(async (combination, index) => {
      if (index === 0) {
        responseForQueue['combination1'] = combination;
        await this.startArbitrageQueue.add('init-arbit', {
          ...responseForQueue,
          base_pair: combination.base_pair,
          target_pair: combination.target_pair,
        });
      }
      if (index === 1) {
        responseForQueue['combination2'] = combination;
        await this.startArbitrageQueue.add('init-arbit', {
          ...responseForQueue,
          base_pair: combination.base_pair,
          target_pair: combination.target_pair,
          });
      }
      if (index === 2) {
        responseForQueue['combination3'] = combination;
        await this.startArbitrageQueue.add('init-arbit', {
          ...responseForQueue,
          base_pair: combination.base_pair,
          target_pair: combination.target_pair,
        });
         }
    });

    // const { base_platform, base_pair, target_platform, target_pair, uuid }
    // Send to queue
    // await this.startArbitrageQueue.add('init-arbit', responseForQueue);


    return arbitrageBot;
  }

  async getPairPriceAndVolume(user: User, body: StartArbitragebotDto) {
    try {
      const rawData = await this.em
        .createQueryBuilder(ArbitrageBot, 'ab')
        .leftJoin(
          'arbitrage_pair',
          'ap',
          'CONVERT(ap.uuid USING utf8mb4) COLLATE utf8mb4_general_ci = CONVERT(ab.uuid USING utf8mb4) COLLATE utf8mb4_general_ci',
        )
        .leftJoin('pair', 'base_pair', 'base_pair.id = ap.base_id')
        .leftJoin('pair', 'target_pair', 'target_pair.id = ap.target_id')
        .leftJoin(
          'user_key',
          'base_user_key',
          'ab.base_key_id = base_user_key.id',
        )
        .leftJoin(
          'user_key',
          'target_user_key',
          'ab.target_key_id = target_user_key.id',
        )
        .leftJoin('user', 'user', 'base_user_key.user_id = user.id')
        .leftJoin(
          'platform',
          'base_platform',
          'ab.base_platform_id = base_platform.id',
        )
        .leftJoin(
          'platform',
          'target_platform',
          'ab.target_platform_id = target_platform.id',
        )
        .where(
          "user.id = :user_id and ab.uuid = :uuid and ab.status in ('ACTIVE')",
          {
            user_id: user.id,
            uuid: body.uuid,
          },
        )
        .select([
          'ab.id as ab_id',
          'ab.name as ab_name',
          'ab.uuid as ab_uuid',
          'ab.percentage as ab_percentage',
          'ab.rate as ab_rate',
          'ab.type as ab_type',
          'ab.initial_fund as ab_initial_fund',
          'ab.status as ab_status',
        ])
        .addSelect(
          `JSON_OBJECT('id', base_platform.id, 'name', base_platform.name)`,
          'base_platform',
        )
        .addSelect(
          `JSON_OBJECT('id', target_platform.id, 'name', target_platform.name)`,
          'target_platform',
        )
        .addSelect('ap.id as ap_id')
        .addSelect('base_pair.id as base_pair_id')
        .addSelect('base_pair.symbol as base_pair_symbol')
        .addSelect('base_pair.min_notional as base_pair_min_notional')
        .addSelect('base_pair.price_dp as base_pair_price_dp')
        .addSelect('base_pair.qty_dp as base_pair_qty_dp')
        .addSelect('base_pair.base as base_pair_base')
        .addSelect('base_pair.quote as base_pair_quote')
        .addSelect('base_pair.tick as base_pair_tick')
        .addSelect('base_pair.step as base_pair_step')
        .addSelect('target_pair.id as target_pair_id')
        .addSelect('target_pair.symbol as target_pair_symbol')
        .addSelect('target_pair.min_notional as target_pair_min_notional')
        .addSelect('target_pair.price_dp as target_pair_price_dp')
        .addSelect('target_pair.qty_dp as target_pair_qty_dp')
        .addSelect('target_pair.base as target_pair_base')
        .addSelect('target_pair.quote as target_pair_quote')
        .addSelect('target_pair.tick as target_pair_tick')
        .addSelect('target_pair.step as target_pair_step')
        .getRawMany();

      if (rawData.length === 0) {
        this.pinoLogger.error(
          { user, body },
          'Invalid status in getPairPriceAndVolume',
        );
        throw new BadRequestException(CustomErrorMessages.BOT.INVALID_STATUS);
      }

      const botRow = rawData[0];

      // Build combinations
      const combinations = [];
      rawData.forEach((row) => {
        if (row.ap_id) {
          combinations.push({
            id: row.ap_id,
            base_pair: {
              id: row.base_pair_id,
              symbol: row.base_pair_symbol,
              min_notional: row.base_pair_min_notional,
              price_dp: row.base_pair_price_dp,
              qty_dp: row.base_pair_qty_dp,
              base: row.base_pair_base,
              quote: row.base_pair_quote,
              tick: row.base_pair_tick,
              step: row.base_pair_step,
            },
            target_pair: {
              id: row.target_pair_id,
              symbol: row.target_pair_symbol,
              min_notional: row.target_pair_min_notional,
              price_dp: row.target_pair_price_dp,
              qty_dp: row.target_pair_qty_dp,
              base: row.target_pair_base,
              quote: row.target_pair_quote,
              tick: row.target_pair_tick,
              step: row.target_pair_step,
            },
          });
        }
      });

      // Limit to first 3 combinations
      const limitedCombinations = combinations.slice(0, 3);

      // Fetch Redis prices for each combination
      const redisPromises = [];
      limitedCombinations.forEach((combo) => {
        redisPromises.push(
          this.redisClient.get(`${combo.base_pair.id}:bid`),
          this.redisClient.get(`${combo.base_pair.id}:ask`),
          this.redisClient.get(`${combo.target_pair.id}:bid`),
          this.redisClient.get(`${combo.target_pair.id}:ask`),
        );
      });

      const redisResults = await Promise.all(redisPromises);

      // Map results back to combinations
      const response = [];
      for (let i = 0; i < limitedCombinations.length; i++) {
        const comboIndex = i * 4;
        response.push({
          base_platform: {
            pair: combinations[i].base_pair,
            bid: JSON.parse(redisResults[comboIndex]),
            ask: JSON.parse(redisResults[comboIndex + 1]),
          },
          target_platform: {
            pair: combinations[i].target_pair,
            bid: JSON.parse(redisResults[comboIndex + 2]),
            ask: JSON.parse(redisResults[comboIndex + 3]),
          },
        });
      }

      return response;
    } catch (error) {
      this.pinoLogger.error(
        { error, user, body },
        'Error in getPairPriceAndVolume',
      );
      throw error instanceof BadRequestException
        ? error
        : new BadRequestException(error.message);
    }
  }

  async stop(user: User, body: StopArbitragebotDto) {
    let queryBuilder = this.em
      .createQueryBuilder(ArbitrageBot, 'ab')
      .leftJoin(
        'user_key',
        'base_user_key',
        'ab.base_key_id = base_user_key.id',
      )
      .leftJoin(
        'user_key',
        'target_user_key',
        'ab.target_key_id = target_user_key.id',
      )
      .leftJoin('user', 'user', 'base_user_key.user_id = user.id')
      .leftJoin(
        'platform',
        'base_platform',
        'ab.base_platform_id = base_platform.id',
      )
      .leftJoin(
        'platform',
        'target_platform',
        'ab.target_platform_id = target_platform.id',
      )
      .where(
        "user.id = :user_id and ab.uuid = :uuid and ab.status in ('ACTIVE', 'INSUFFICIENT') ",
        // "user.id = :user_id and ab.uuid = :uuid and ab.status in ('ACTIVE','BUY_ACTIVE', 'SELL_ACTIVE', 'INSUFFICIENT') ",
        {
          user_id: user.id,
          uuid: body.uuid,
        },
      );

    let arbitrageBot = await queryBuilder
      .select([
        'ab.id as id',
        'ab.name as name',
        'ab.uuid as uuid',
        'ab.percentage as percentage',
        'ab.rate as rate',
        'ab.type as type',
        'ab.initial_fund as initial_fund',
        'ab.status as status',
      ])
      .addSelect(
        `JSON_OBJECT(
      'id', base_platform.id,
      'name', base_platform.name
    )`,
        'base_platform',
      )
      .addSelect(
        `JSON_OBJECT(
      'id', target_platform.id,
      'name', target_platform.name
    )`,
        'target_platform',
      )
      .addSelect(
        `JSON_OBJECT(
      'id', base_user_key.id,
      'api_key', base_user_key.api_key,
      'api_secret', base_user_key.api_secret
    )`,
        'base_user_key',
      )
      .addSelect(
        `JSON_OBJECT(
      'id', target_user_key.id,
      'api_key', target_user_key.api_key,
      'api_secret', target_user_key.api_secret
    )`,
        'target_user_key',
      )
      .getRawOne();
    if (!arbitrageBot) {
      this.pinoLogger.error({ user, body }, 'Invalid status in stop');
      throw new BadRequestException(CustomErrorMessages.BOT.INVALID_STATUS);
    }

    await this.em
      .createQueryBuilder()
      .update(ArbitrageBot)
      .set({
        status: ArbitBotStatus.DELETED,
        deleted_at: () => 'CURRENT_TIMESTAMP',
      })
      .where('uuid = :uuid', { uuid: arbitrageBot.uuid })
      .execute();
    await this.createEventQueue.add(
      'processing',
      {
        botType: 'arbitragebot',
        uuid: arbitrageBot.uuid,
        eventType: CreateBotEventQueueService.EVENT_TYPES.BOT_SHUTDOWN,
        params: [],
      },
      { removeOnComplete: true },
    );
    return `Stop  #${arbitrageBot.uuid} Arbitragebot`;
    // return await this.stopArbitrageQueue.add('end-arbit', arbitrageBot);
  }

  async remove(user: User, deleteArbitragebotDto: DeleteArbitragebotDto) {
    let ab = await this.em
      .createQueryBuilder(ArbitrageBot, 'ab')
      .leftJoin('user_key', 'user_key', 'ab.base_key_id = user_key.id')
      .leftJoin('user', 'user', 'user_key.user_id = user.id')
      .where(
        "user.id = :user_id and ab.uuid = :uuid and ab.status = 'INACTIVE'",
        {
          user_id: user.id,
          uuid: deleteArbitragebotDto.uuid,
        },
      )
      .getOne();
    if (!ab) {
      this.pinoLogger.error(
        { user, deleteArbitragebotDto },
        'Invalid status in remove',
      );
      throw new BadRequestException(CustomErrorMessages.BOT.INVALID_STATUS);
    }

    let bot = await this.em
      .createQueryBuilder()
      .update(ArbitrageBot)
      .set({
        status: ArbitBotStatus.DELETED,
        deleted_at: () => 'CURRENT_TIMESTAMP',
      })
      .where(`uuid = :uuid and status = 'INACTIVE'`, {
        uuid: deleteArbitragebotDto.uuid,
      })
      .execute();
    return `Delete  #${deleteArbitragebotDto.uuid} Arbitragebot`;
  }

  async updateActiveBotRate(
    user: User,
    updateRateDto: UpdateRateArbitrageBotDto,
  ) {
    console.log('updateActiveBotRate() called with:', { user, updateRateDto });
    let ab = await this.em
      .createQueryBuilder(ArbitrageBot, 'ab')
      .leftJoin('user_key', 'user_key', 'ab.base_key_id = user_key.id')
      .leftJoin('user', 'user', 'user_key.user_id = user.id')
      .where(
        'user.id = :user_id and ab.uuid = :uuid and ab.status != :inactiveStatus',
        {
          inactiveStatus: ArbitBotStatus.INACTIVE,
          user_id: user.id,
          uuid: updateRateDto.uuid,
        },
      )
      .getOne();
    console.log('Fetched bot:', ab);

    if (!ab) {
      this.pinoLogger.error(
        { user, updateRateDto },
        'Bot not found or not in active status in updateActiveBotRate',
      );
      console.error('Bot not found or not in active status');
      throw new BadRequestException('Bot not found or not in active status');
    }

    await new UserKeyValidatorHelper(this.em).validateMultipleUserKeys(
      [ab.base_key_id, ab.target_key_id],
      'update arbitrage bot rate',
    );
    console.log('User keys validated for updateActiveBotRate');

    // Execute update
    try {
      console.log('Updating bot rate and percentage with:', {
        rate: updateRateDto.rate,
        percentage: updateRateDto.percentage,
      });
      let bot = await this.em
        .createQueryBuilder()
        .update(ArbitrageBot)
        .set({
          rate: updateRateDto.rate,
          percentage: updateRateDto.percentage,
        })
        .where(`uuid = :uuid`, { uuid: updateRateDto.uuid })
        .execute();
      console.log('Update result:', bot);

      if (!bot) {
        this.pinoLogger.error(
          { user, updateRateDto },
          'Update failed in updateActiveBotRate',
        );
        console.error('Update failed');
        throw new BadRequestException('Update failed');
      }

      console.log(
        `Updated rate and percentage for Arbitragebot #${updateRateDto.uuid}`,
      );
      return `Updated rate and percentage for Arbitragebot #${updateRateDto.uuid}`;
    } catch (error) {
      this.pinoLogger.error(
        { error, user, updateRateDto },
        'Error updating rate and percentage in updateActiveBotRate',
      );
      console.error('Error updating rate and percentage:', error);
      throw new BadRequestException(error.message);
    }
  }

  async update(user: User, updateDto: UpdateArbitrageBotDto) {
    console.log('update() called with:', { user, updateDto });
    // Fetch bot with ownership validation
    const bot = await this.em
      .createQueryBuilder(ArbitrageBot, 'ab')
      .where('ab.uuid = :uuid AND ab.status = :status', {
        uuid: updateDto.uuid,
        status: ArbitBotStatus.INACTIVE,
      })
      .andWhere(
        'ab.base_key_id IN (SELECT uk.id FROM user_key uk WHERE uk.user_id = :userId)',
        { userId: user.id },
      )
      .getOne();
    console.log('Fetched bot:', bot);

    if (!bot) {
      this.pinoLogger.error(
        { user, updateDto },
        'Bot not found, not inactive, or not owned by user in update',
      );
      console.error('Bot not found, not inactive, or not owned by user');
      throw new BadRequestException(
        'Bot not found, not inactive, or not owned by user',
      );
    }

    // Validate user keys ownership and status (same as create function)
    let [base_user_key, target_user_key] = await Promise.all([
      this.em
        .createQueryBuilder(UserKey, 'uk')
        .where(
          'uk.id = :id and uk.user_id = :user_id and uk.platform_id = :platform_id',
          {
            id: bot.base_key_id,
            user_id: user.id,
            platform_id: bot.base_platform_id,
          },
        )
        .getOne(),
      this.em
        .createQueryBuilder(UserKey, 'uk')
        .where(
          'uk.id = :id and uk.user_id = :user_id and uk.platform_id = :platform_id',
          {
            id: bot.target_key_id,
            user_id: user.id,
            platform_id: bot.target_platform_id,
          },
        )
        .getOne(),
    ]);
    console.log('Base user key:', base_user_key);
    console.log('Target user key:', target_user_key);

    if (!base_user_key || !target_user_key) {
      this.pinoLogger.error({ user, updateDto }, 'Invalid user keys in update');
      console.error('Invalid user keys');
      throw new BadRequestException(
        CustomErrorMessages.USER_KEY.INVALID_USERKEY,
      );
    }

    // Use the same validator helper as create function
    await new UserKeyValidatorHelper(this.em).validateMultipleUserKeys(
      [base_user_key.id, target_user_key.id],
      'update arbitrage bot',
    );
    console.log('User keys validated');

    // // Name uniqueness check (only if name is being updated)
    // if (updateDto.name && updateDto.name !== bot.name) {
    //   const existingBot = await this.em
    //     .createQueryBuilder(ArbitrageBot, 'ab')
    //     .where('ab.name = :name AND ab.id != :id', {
    //       name: updateDto.name,
    //       id: bot.id
    //     })
    //     .getOne();

    //   if (existingBot) {
    //     throw new BadRequestException('Bot name already exists');
    //   }
    // }

    // // Fee validation if type or percentage changes (like create function)
    // if (updateDto.type || updateDto.percentage) {
    //   const percentage = updateDto.percentage || bot.percentage;
    //   const type = updateDto.type || bot.type;

    //   if (type === ArbitBotType.MAKER) {
    //     const totalFee = new Decimal(base_user_key.maker_fee).plus(target_user_key.maker_fee);
    //     if (totalFee.greaterThan(percentage)) {
    //       throw new BadRequestException('Profit percentage insufficient to cover maker fees');
    //     }
    //   } else {
    //     const totalFee = new Decimal(base_user_key.taker_fee).plus(target_user_key.taker_fee);
    //     if (totalFee.greaterThan(percentage)) {
    //       throw new BadRequestException('Profit percentage insufficient to cover taker fees');
    //     }
    //   }
    // }

    // Handle combination updates (if provided)
    if (updateDto.combination1) {
      console.log(
        'Updating combinations, deleting existing pairs for uuid:',
        bot.uuid,
      );
      // Delete existing pairs
      await this.em
        .createQueryBuilder()
        .delete()
        .from(ArbitragePair)
        .where('uuid = :uuid', { uuid: bot.uuid })
        .execute();
      console.log('Existing pairs deleted');

      // Validate and create new pairs (same validation as create function)
      const combinations = [
        updateDto.combination1,
        updateDto.combination2,
        updateDto.combination3,
      ].filter(Boolean);
      console.log('Combinations to add:', combinations);

      for (const combination of combinations) {
        console.log('Validating combination:', combination);
        let { base, target } = await this.validatePair(
          combination,
          bot.base_platform_id,
          bot.target_platform_id,
        );
        console.log('Validated base:', base, 'target:', target);

        // Same validations as create function
        if (base.id === target.id) {
          this.pinoLogger.error({ combination }, 'Same pair used in update');
          console.error('Same pair used in combination:', combination);
          throw new BadRequestException(CustomErrorMessages.BOT.SAME_PAIR_USED);
        }

        if (base.base !== target.base) {
          this.pinoLogger.error(
            { combination },
            'Base asset mismatch in update',
          );
          console.error('Base asset mismatch in combination:', combination);
          throw new BadRequestException(
            CustomErrorMessages.BOT.BASE_ASSET_MISMATCH,
          );
        }

        // Check if pairs are used in other bots including active and inactive bots (including current bot so prevent one bot have same pair)
        const activeBotWithPair = await this.em
          .createQueryBuilder(ArbitrageBot, 'ab')
          .innerJoin(
            'arbitrage_pair',
            'ap',
            'CONVERT(ap.uuid USING utf8mb4) COLLATE utf8mb4_general_ci = CONVERT(ab.uuid USING utf8mb4) COLLATE utf8mb4_general_ci',
          )
          .where('ab.status IN (:...activeStatuses)', {
            activeStatuses: [ArbitBotStatus.ACTIVE],
          })
          // .andWhere('ab.id != :botId', { botId: bot.id })
          .andWhere('(ap.base_id = :baseId AND ap.target_id = :targetId)', {
            baseId: base.id,
            targetId: target.id,
          })
          .getOne();
        console.log('Active bot with pair:', activeBotWithPair);

        if (activeBotWithPair) {
          this.pinoLogger.error(
            { combination },
            'Pair already active in another bot in update',
          );
          console.error('Pair already active in another bot:', combination);
          throw new BadRequestException(
            CustomErrorMessages.BOT.PAIR_ALREADY_ACTIVE,
          );
        }

        // Create new pair
        await this.createArbitragePair(
          bot.uuid,
          base.id.toString(),
          target.id.toString(),
        );
        console.log('Created arbitrage pair for combination:', combination);
      }
    }

    // Update bot fields
    const updateData: Partial<ArbitrageBot> = {};
    const fields = [
      'name',
      'type',
      'rate',
      'percentage',
      'initial_fund',
      'initial_base',
      'initial_quote',
      'initial_price',
    ];
    // console.log('Preparing updateData for fields:', fields);

    fields.forEach((field) => {
      if (updateDto[field] !== undefined) {
        updateData[field] = updateDto[field];
      }
    });
    console.log('updateData to be set:', updateData);

    try {
      const updateResult = await this.em
        .createQueryBuilder()
        .update(ArbitrageBot)
        .set(updateData)
        .where('id = :id', { id: bot.id })
        .execute();
      console.log('Update result:', updateResult);

      console.log(`Updated Arbitragebot #${bot.uuid} successfully`);
      return `Updated Arbitragebot #${bot.uuid} successfully`;
    } catch (error) {
      this.pinoLogger.error(
        { error, user, updateDto },
        'Error updating Arbitragebot in update',
      );
      console.error('Error updating Arbitragebot:', error);
      throw new BadRequestException(error.message);
    }
  }

  // @Cron('*/10 * * * * *', { name: 'arbit-checking' })
  // async checkArbitrageBot() {
  //   let arbitrageBots = await this.em
  //     .createQueryBuilder(ArbitrageBot, 'ab')
  //     .where(" ab.status in ('ACTIVE', 'BUY_ACTIVE', 'SELL_ACTIVE')")
  //     .select([
  //       'ab.base_pair_id as  base_pair_id',
  //       'ab.target_pair_id as target_pair_id',
  //     ])
  //     .getRawMany();
  //   const pairIds = arbitrageBots.flatMap(
  //     ({ target_pair_id, base_pair_id }) => [target_pair_id, base_pair_id],
  //   );
  //   const uniquePairs = [...new Set(pairIds)];
  //   for (const pair of uniquePairs) {
  //     await this.checkingArbitrageQueue.add('check-criterea', {
  //       id: pair,
  //       side: 'buy',
  //     });
  //     await this.checkingArbitrageQueue.add('check-criterea', {
  //       id: pair,
  //       side: 'sell',
  //     });
  //   }
  //   return;
  // }

  async checkArbitrageBot(
    user: User,
    placeOrderArbitrageBotDto: PlaceOrderArbitrageBotDto,
  ) {
    const rawData = await this.em
      .createQueryBuilder(ArbitrageBot, 'ab')
      .leftJoin(
        'arbitrage_pair',
        'ap',
        'CONVERT(ap.uuid USING utf8mb4) COLLATE utf8mb4_general_ci = CONVERT(ab.uuid USING utf8mb4) COLLATE utf8mb4_general_ci',
      )
      .leftJoin('pair', 'base_pair', 'base_pair.id = ap.base_id ')
      .leftJoin('pair', 'target_pair', 'target_pair.id = ap.target_id ')
      .leftJoin(
        'user_key',
        'base_user_key',
        'ab.base_key_id = base_user_key.id',
      )
      .leftJoin(
        'user_key',
        'target_user_key',
        'ab.target_key_id = target_user_key.id',
      )
      .leftJoin('user', 'user', 'base_user_key.user_id = user.id')
      .leftJoin(
        'platform',
        'base_platform',
        'ab.base_platform_id = base_platform.id',
      )
      .leftJoin(
        'platform',
        'target_platform',
        'ab.target_platform_id = target_platform.id',
      )
      .where(
        "user.id = :user_id and ab.uuid = :uuid and ab.status in ('ACTIVE') and base_pair.id = :base_pair_id and target_pair.id = :target_pair_id",
        {
          user_id: user.id,
          uuid: placeOrderArbitrageBotDto.uuid,
          base_pair_id: placeOrderArbitrageBotDto.base_pair_id,
          target_pair_id: placeOrderArbitrageBotDto.target_pair_id,
        },
      )
      .select([
        'ab.id as id',
        'ab.name as name',
        'ab.uuid as uuid',
        'ab.percentage as percentage',
        'ab.rate as rate',
        'ab.type as type',
        'ab.initial_fund as initial_fund',
        'ab.status as status',
      ])
      .addSelect(
        `JSON_OBJECT('id', base_platform.id, 'name', base_platform.name)`,
        'base_platform',
      )
      .addSelect(
        `JSON_OBJECT('id', target_platform.id, 'name', target_platform.name)`,
        'target_platform',
      )
      .addSelect(
        `JSON_OBJECT('id', base_user_key.id, 'name', base_user_key.name, 'api_key', base_user_key.api_key,'api_secret', base_user_key.api_secret, 'status', base_user_key.status,'taker_fee',base_user_key.taker_fee,'maker_fee',base_user_key.maker_fee)`,
        'base_user_key',
      )
      .addSelect(
        `JSON_OBJECT('id', target_user_key.id, 'name', target_user_key.name, 'api_key', target_user_key.api_key, 'api_secret', target_user_key.api_secret, 'status', target_user_key.status,'taker_fee',target_user_key.taker_fee,'maker_fee',target_user_key.maker_fee)`,
        'target_user_key',
      )
      .addSelect('ap.id as ap_id')
      .addSelect('base_pair.id as base_pair_id')
      .addSelect('base_pair.symbol as base_pair_symbol')
      .addSelect('base_pair.min_notional as base_pair_min_notional')
      .addSelect('base_pair.price_dp as base_pair_price_dp')
      .addSelect('base_pair.qty_dp as base_pair_qty_dp')
      .addSelect('base_pair.base as base_pair_base')
      .addSelect('base_pair.quote as base_pair_quote')
      .addSelect('base_pair.tick as base_pair_tick')
      .addSelect('base_pair.step as base_pair_step')
      .addSelect('target_pair.id as target_pair_id')
      .addSelect('target_pair.symbol as target_pair_symbol')
      .addSelect('target_pair.min_notional as target_pair_min_notional')
      .addSelect('target_pair.price_dp as target_pair_price_dp')
      .addSelect('target_pair.qty_dp as target_pair_qty_dp')
      .addSelect('target_pair.base as target_pair_base')
      .addSelect('target_pair.quote as target_pair_quote')
      .addSelect('target_pair.tick as target_pair_tick')
      .addSelect('target_pair.step as target_pair_step')
      .getRawMany();

    if (rawData.length === 0) {
      this.pinoLogger.error(
        { user, placeOrderArbitrageBotDto },
        'Invalid status in checkArbitrageBot',
      );
      throw new BadRequestException(CustomErrorMessages.BOT.INVALID_STATUS);
    }
    // let queryBuilder = this.em
    //   .createQueryBuilder(ArbitrageBot, 'ab')
    //   .leftJoin(
    //     'user_key',
    //     'base_user_key',
    //     'ab.base_key_id = base_user_key.id',
    //   ) // No user_id for arbit bot, use userkey to get user id checking
    //   .leftJoin('user', 'user', 'base_user_key.user_id = user.id')
    //   .where(" ab.status in ('ACTIVE')")
    //   // .where(" ab.status in ('ACTIVE', 'BUY_ACTIVE', 'SELL_ACTIVE')")
    //   .andWhere('ab.uuid = :uuid AND user.id = :user_id', {
    //     uuid: placeOrderArbitrageBotDto.uuid,
    //     user_id: user.id,
    //   });

    // let arbitrageBots = await queryBuilder
    //   .select([
    //     'ab.base_pair_id as  base_pair_id',
    //     'ab.target_pair_id as target_pair_id',
    //   ])
    //   .getRawMany();

    if (!rawData || rawData.length === 0) {
      this.pinoLogger.error(
        { user, placeOrderArbitrageBotDto },
        'Invalid UUID in checkArbitrageBot',
      );
      throw new BadRequestException(CustomErrorMessages.BOT.INVALID_UUID);
    }
    const pairIds = rawData.flatMap(({ target_pair_id, base_pair_id }) => [
      target_pair_id,
      base_pair_id,
    ]);
    console.log(pairIds);
    const uniquePairs = [...new Set(pairIds)];
    for (const pair of uniquePairs) {
      await this.checkingArbitrageQueue.add('check-criterea', {
        id: pair,
        side: 'buy',
        uuid: placeOrderArbitrageBotDto.uuid,
        rawData,
      });
      break;
    }
    return 'Arbitrage order check and queueing completed';
  }

  async checkArbitrageBalance(user: User, body: StartArbitragebotDto) {
    const rawData = await this.em
      .createQueryBuilder(ArbitrageBot, 'ab')
      .leftJoin(
        'arbitrage_pair',
        'ap',
        'CONVERT(ap.uuid USING utf8mb4) COLLATE utf8mb4_general_ci = CONVERT(ab.uuid USING utf8mb4) COLLATE utf8mb4_general_ci',
      )
      .leftJoin('pair', 'base_pair', 'base_pair.id = ap.base_id')
      .leftJoin('pair', 'target_pair', 'target_pair.id = ap.target_id')
      .leftJoin(
        'user_key',
        'base_user_key',
        'ab.base_key_id = base_user_key.id',
      )
      .leftJoin(
        'user_key',
        'target_user_key',
        'ab.target_key_id = target_user_key.id',
      )
      .leftJoin('user', 'user', 'base_user_key.user_id = user.id')
      .leftJoin(
        'platform',
        'base_platform',
        'ab.base_platform_id = base_platform.id',
      )
      .leftJoin(
        'platform',
        'target_platform',
        'ab.target_platform_id = target_platform.id',
      )
      .where(
        "user.id = :user_id and ab.uuid = :uuid and ab.status in ('ACTIVE')",
        {
          user_id: user.id,
          uuid: body.uuid,
        },
      )
      .select([
        'ab.id as ab_id',
        'ab.name as ab_name',
        'ab.uuid as ab_uuid',
        'ab.percentage as ab_percentage',
        'ab.rate as ab_rate',
        'ab.type as ab_type',
        'ab.initial_fund as ab_initial_fund',
        'ab.status as ab_status',
      ])
      .addSelect(
        `JSON_OBJECT('id', base_platform.id, 'name', base_platform.name)`,
        'base_platform',
      )
      .addSelect(
        `JSON_OBJECT('id', target_platform.id, 'name', target_platform.name)`,
        'target_platform',
      )
      .addSelect(
        `JSON_OBJECT('id', base_user_key.id, 'api_key', base_user_key.api_key, 'api_secret', base_user_key.api_secret, 'taker_fee', base_user_key.taker_fee, 'maker_fee', base_user_key.maker_fee)`,
        'base_user_key',
      )
      .addSelect(
        `JSON_OBJECT('id', target_user_key.id, 'api_key', target_user_key.api_key, 'api_secret', target_user_key.api_secret, 'taker_fee', target_user_key.taker_fee, 'maker_fee', target_user_key.maker_fee)`,
        'target_user_key',
      )
      .addSelect('base_pair.base as base_pair_base')
      .addSelect('base_pair.quote as base_pair_quote')
      .addSelect('target_pair.base as target_pair_base')
      .addSelect('target_pair.quote as target_pair_quote')
      .getRawMany();

    if (rawData.length === 0) {
      this.pinoLogger.error(
        { user, body },
        'Invalid status in checkArbitrageBalance',
      );
      throw new BadRequestException(CustomErrorMessages.BOT.INVALID_STATUS);
    }

    // Parse the first row for bot info
    const botRow = rawData[0];
    const arbitrageBot = {
      base_platform:
        typeof botRow.base_platform === 'string'
          ? JSON.parse(botRow.base_platform)
          : botRow.base_platform,
      target_platform:
        typeof botRow.target_platform === 'string'
          ? JSON.parse(botRow.target_platform)
          : botRow.target_platform,
      base_user_key:
        typeof botRow.base_user_key === 'string'
          ? JSON.parse(botRow.base_user_key)
          : botRow.base_user_key,
      target_user_key:
        typeof botRow.target_user_key === 'string'
          ? JSON.parse(botRow.target_user_key)
          : botRow.target_user_key,
    };

    // Collect all unique symbols from all pairs
    const requiredSymbols = new Set<string>();
    rawData.forEach((row) => {
      if (row.base_pair_base) requiredSymbols.add(row.base_pair_base);
      if (row.base_pair_quote) requiredSymbols.add(row.base_pair_quote);
      if (row.target_pair_base) requiredSymbols.add(row.target_pair_base);
      if (row.target_pair_quote) requiredSymbols.add(row.target_pair_quote);
    });

    // Convert to array for easier handling
    const requiredSymbolsArray = Array.from(requiredSymbols);

    // Initialize platforms
    let base_platform = callPlatformHelper(
      arbitrageBot.base_platform.name.toLowerCase(),
    );
    await base_platform.init(
      arbitrageBot.base_platform.name.toLowerCase(),
      arbitrageBot.base_user_key.api_key,
      arbitrageBot.base_user_key.api_secret,
    );

    let target_platform = callPlatformHelper(
      arbitrageBot.target_platform.name.toLowerCase(),
    );
    await target_platform.init(
      arbitrageBot.target_platform.name.toLowerCase(),
      arbitrageBot.target_user_key.api_key,
      arbitrageBot.target_user_key.api_secret,
    );

    // Get balances
    let base_balances = await base_platform.readNonZeroBalance();
    let target_balances = await target_platform.readNonZeroBalance();

    // Filter balances based on required symbols
    let return_base = [];
    let return_target = [];

    for (const balance of base_balances) {
      if (requiredSymbols.has(balance.symbol)) {
        return_base.push({
          symbol: balance.symbol,
          avai_balance: balance.avai_balance,
          freeze_balance: balance.freeze_balance,
        });
      }
    }

    for (const balance of target_balances) {
      if (requiredSymbols.has(balance.symbol)) {
        return_target.push({
          symbol: balance.symbol,
          avai_balance: balance.avai_balance,
          freeze_balance: balance.freeze_balance,
        });
      }
    }

    // Ensure all required symbols are present (add with 0 balance if missing)
    return_target = this.ensureSymbols(return_target, requiredSymbolsArray);
    return_base = this.ensureSymbols(return_base, requiredSymbolsArray);

    return { base_balances: return_base, target_balances: return_target };
  }

  ensureSymbols(balances, requiredSymbols) {
    // Create a set of existing symbols for fast lookup
    const existingSymbols = new Set(balances.map((item) => item.symbol));

    // For each required symbol, check presence
    for (const sym of requiredSymbols) {
      if (!existingSymbols.has(sym)) {
        // Add default object for missing symbol
        balances.push({
          symbol: sym,
          avai_balance: '0',
          freeze_balance: '0',
        });
      }
    }

    return balances;
  }

  async validatePair(
    input: any,
    platform_id: string,
    target_platform_id: string,
  ) {
    const [base, target] = input.split('-');
    if (!base || !target) {
      this.pinoLogger.error(
        { input, platform_id, target_platform_id },
        'Invalid combination in validatePair',
      );
      throw new BadRequestException('Invalid combination');
    }
    let [base_pair, target_pair] = await Promise.all([
      this.em
        .createQueryBuilder(Pair, 'p')
        .where('p.id = :id and p.platform_id = :platform_id', {
          id: base,
          platform_id: platform_id,
        })
        .getOne(),
      this.em
        .createQueryBuilder(Pair, 'p')
        .where('p.id = :id and p.platform_id = :platform_id', {
          id: target,
          platform_id: target_platform_id,
        })
        .getOne(),
    ]);
    if (!base_pair || !target_pair) {
      this.pinoLogger.error(
        { input, platform_id, target_platform_id },
        'Pair not found in validatePair',
      );
      throw new BadRequestException(CustomErrorMessages.PAIR.PAIR_NOT_FOUND);
    }
    // Check isArbitrageBot for base and target pairs
    if (!base_pair.is_arbit || !target_pair.is_arbit) {
      throw new BadRequestException('Both base and target pairs must active for the Arbitrage Bot');
    }
    return { base: base_pair, target: target_pair };
  }

  async createArbitragePair(uuid: string, base_id: string, target_id: string) {
    let arbitrage_pair = new ArbitragePair();
    arbitrage_pair.uuid = uuid;
    arbitrage_pair.base_id = base_id;
    arbitrage_pair.target_id = target_id;
    await this.em.save(ArbitragePair, arbitrage_pair);
  }
}
