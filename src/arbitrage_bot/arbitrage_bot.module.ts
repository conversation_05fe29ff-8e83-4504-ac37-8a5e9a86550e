import { Modu<PERSON> } from '@nestjs/common';
import { BullModule, BullModuleOptions } from '@nestjs/bull';
import configuration from 'config/configuration';

import { ArbitrageBotService } from './arbitrage_bot.service';
import { ArbitrageBotController } from './arbitrage_bot.controller';

// 1. Shared Redis config
const bullConfig: BullModuleOptions = {
  redis: {
    host: configuration().redis.host,
    port: configuration().redis.port,
    password: configuration().redis.password,
  },
};

@Module({
  imports: [
    BullModule.forRoot(bullConfig),

    BullModule.registerQueue(
      { name: 'start-arbit' },
      { name: 'stop-arbit' },
      { name: 'arbit-checking' },
      { name: 'create-event' },
    ),
  ],
  controllers: [ArbitrageBotController],
  providers: [ArbitrageBotService],
})
export class ArbitrageBotModule {}
