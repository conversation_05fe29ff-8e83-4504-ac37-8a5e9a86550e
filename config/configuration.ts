require('dotenv').config();
export default () => ({
  environment: process.env.ENVIRONMENT,
  port: parseInt(process.env.PORT, 10) || 3000,
  redis: {
    host: process.env.REDIS_HOST,
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD,
  },
  database: {
    type: 'mysql',
    host: process.env.DATABASE_HOST,
    port: parseInt(process.env.DATABASE_PORT),
    username: process.env.DATABASE_USERNAME,
    password: process.env.DATABASE_PASSWORD,
    database: process.env.DATABASE_NAME,
    synchronize: process.env.SYNC,
  },
  decodo: {
    username: process.env.DECODO_USERNAME,
    password: process.env.DECODO_PASSWORD,
    url: process.env.DECODO_URL,
  },
  jwtConstant: {
    secret: process.env.AUTH_SECRET,
  },
  encryption: {
    secret: process.env.ENCRYPTION_SECRET,
  },
  oss: {
    region: process.env.REGION, // Specify the region in which the bucket resides. Example: 'oss-cn-hangzhou'.
    accessKeyId: process.env.ACCESS_KEY, // Make sure that the OSS_ACCESS_KEY_ID environment variable is configured.
    accessKeySecret: process.env.ACCESS_SECRET, // Make sure that the OSS_ACCESS_KEY_SECRET environment variable is configured.
    bucket: process.env.BUCKET, // Specify the name of the bucket. Example: 'my-bucket-name'.
  },
  exchanges: {
    binance: {
      apiUrl: process.env.BINANCE_API_URL || 'https://api.binance.com',
    },
    hata: {
      globalApiUrl: process.env.HATA_GLOBAL_API_URL || 'https://api.hata.io',
      myrApiUrl: process.env.HATA_MYR_API_URL || 'https://my-api.hata.io',
    },
    tokenize: {
      apiUrl:
        process.env.TOKENIZE_API_URL ||
        'https://api.tokenizemalaysia.com/public/v1',
    },
    luno: {
      apiUrl: 
        process.env.LUNO_API_URL || 'https://api.luno.com',
    },
  },
  stream_url: process.env.STREAM_URL,
  max_age: parseInt(process.env.MAX_AGE),
  max_age_2fa: parseInt(process.env.MAX_AGE_2FA),
  taillog:{
    token: process.env.TAILLOG_TOKEN,
    url: process.env.TAILLOG_URL,
  },
});
