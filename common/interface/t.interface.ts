import { applyDecorators, Type } from '@nestjs/common';
import {
  ApiExtraModels,
  ApiOkResponse,
  ApiResponse,
  getSchemaPath,
} from '@nestjs/swagger';
import { PageDto } from 'common/dto/pageResponse.dto';
import { JsendObjectError, JsendObjectSuccess } from './jsend-interface';

export const ApiGlobalDtoSuccess = <TModel extends Type<any>>(
  model: TModel,
  code: number,
) => {
  return applyDecorators(
    ApiResponse({
      status: code,
      schema: {
        allOf: [
          { $ref: getSchemaPath(JsendObjectSuccess) },
          {
            properties: {
              message: {
                $ref: getSchemaPath(model),
              },
            },
          },
        ],
      },
    }),
  );
};

export const ApiGlobalDtoError = (code: number) => {
  return applyDecorators(
    ApiResponse({
      status: code,
      schema: {
        allOf: [
          { $ref: getSchemaPath(JsendObjectError) },
          {
            properties: {
              message: {
                type: 'string',
              },
            },
          },
        ],
      },
    }),
  );
};

export const ApiPaginatedResponse = <TModel extends Type<any>>(
  model: TModel,
  code: number,
) => {
  return applyDecorators(
    ApiExtraModels(PageDto),
    ApiOkResponse({
      description: 'Successfully received model list',
      // status: code,
      schema: {
        allOf: [
          { $ref: getSchemaPath(JsendObjectSuccess) },
          {
            properties: {
              message: {
                $ref: getSchemaPath(PageDto),
                properties: {
                  data: {
                    type: 'array',
                    items: { $ref: getSchemaPath(model) },
                  },
                },
              },
            },
          },
        ],
      },
    }),
  );
};
