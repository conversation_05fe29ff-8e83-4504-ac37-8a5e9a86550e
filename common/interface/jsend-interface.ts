import { ApiProperty } from '@nestjs/swagger';

export class JsendObjectSuccess<TData> {
  @ApiProperty({ description: 'api status', type: String, example: 'success' })
  status: string;
  @ApiProperty({})
  message: TData | TData[];
}

export class JsendObjectError<TError> {
  @ApiProperty({ description: 'api status', type: String, example: 'fail' })
  status: string;
  @ApiProperty({})
  message?: TError[];
}
