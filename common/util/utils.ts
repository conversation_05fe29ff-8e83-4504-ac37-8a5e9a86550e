import { BadRequestException, HttpException } from '@nestjs/common';
import configuration from 'config/configuration';
import * as crypto from 'crypto';
import * as base32 from 'hi-base32';
import * as OSS from 'ali-oss';
import Decimal from 'decimal.js';
import axios from 'axios';

export async function encrypted(data: any, random: any): Promise<any> {
  return new Promise(async (resolve, reject) => {
    const algorithm = 'aes-128-cbc';
    const password = configuration().jwtConstant.secret;
    crypto.scrypt(password, 'salt', 16, (err, key) => {
      if (err) throw err;
      // Then, we'll generate a random initialization vector
      const iv = Buffer.alloc(16, random); // Initialization vector.
      if (err) throw err;
      const cipher = crypto.createCipheriv(algorithm, key, iv);

      let encrypted = cipher.update(data, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      resolve(encrypted);
    });
  });
}

export async function decrypted(data: any, random: any): Promise<any> {
  return new Promise(async (resolve, reject) => {
    const algorithm = 'aes-128-cbc';
    const password = configuration().jwtConstant.secret;
    const key = crypto.scrypt(password, 'salt', 16, (err, key) => {
      // The IV is usually passed along with the ciphertext.
      const iv = Buffer.alloc(16, random); // Initialization vector.

      const decipher = crypto.createDecipheriv(algorithm, key, iv);

      // Encrypted using same algorithm, key and iv.
      const encrypted = data;
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      resolve(decrypted);
    });
  });
}

export async function generateSecret(length = 20) {
  const randomBuffer = crypto.randomBytes(length);
  let r = base32.encode(randomBuffer).replace(/=/g, '');
  return r;
}
function generateHOTP(secret, counter) {
  const decodedSecret = base32.decode.asBytes(secret);
  const buffer = Buffer.alloc(8);
  for (let i = 0; i < 8; i++) {
    buffer[7 - i] = counter & 0xff;
    counter = counter >> 8;
  }

  // Step 1: Generate an HMAC-SHA-1 value
  const hmac = crypto.createHmac('sha1', Buffer.from(decodedSecret));
  hmac.update(buffer);
  const hmacResult = hmac.digest();

  // Step 2: Generate a 4-byte string (Dynamic Truncation)
  const code = dynamicTruncationFn(hmacResult);

  // Step 3: Compute an HOTP value
  return code % 10 ** 6;
}

function dynamicTruncationFn(hmacValue) {
  const offset = hmacValue[hmacValue.length - 1] & 0xf;

  return (
    ((hmacValue[offset] & 0x7f) << 24) |
    ((hmacValue[offset + 1] & 0xff) << 16) |
    ((hmacValue[offset + 2] & 0xff) << 8) |
    (hmacValue[offset + 3] & 0xff)
  );
}
export function generateTOTP(secret, window = 0) {
  const counter = Math.floor(Date.now() / 30000);
  return generateHOTP(secret, counter + window)
    .toString()
    .padStart(6, '0');
}

export function verify2FA(secret, OTP: string) {
  let systemOTP = generateTOTP(secret).toString().padStart(6, '0');
  if (systemOTP == OTP) {
    return true;
  }
  throw new HttpException('Invalid 2FA', 400);
}

/**
 * Helper function to sort and stringify query parameters
 * @param {Record<string, any>} params - A record of parameters
 * @returns {string} - A URL string
 */
export function sortParams(params: Record<string, any>): string {
  return Object.keys(params)
    .sort()
    .map((key) => `${key}=${encodeURIComponent(params[key])}`)
    .join('&');
}

/**
 * Helper function to sort and stringify JSON object
 * @param {Record<string, any>} json - JSON object
 * @returns {string} - A JSON string
 */
export function sortJson(json: Record<string, any>): string {
  const sortedJson = Object.keys(json)
    .sort()
    .reduce(
      (acc, key) => {
        acc[key] = json[key];
        return acc;
      },
      {} as Record<string, any>,
    );
  return JSON.stringify(sortedJson);
}

/**
 * Helper function to remove null or undefined values from an object
 * @param {Record<string, any>} obj - JSON object
 * @returns {string} - A JSON string
 */
export function cleanObject(obj: Record<string, any>): Record<string, any> {
  return Object.keys(obj).reduce(
    (acc, key) => {
      if (obj[key] !== null && obj[key] !== undefined) {
        acc[key] = obj[key];
      }
      return acc;
    },
    {} as Record<string, any>,
  );
}

/**
 * Helper function to count decimal of a given number
 * @param {number} num - Given number
 * @returns {number} - Number of decimals
 */
export function countDecimals(num: string): number {
  return (num.split('.')[1] || []).length;
}

export async function uploadAndDownloadFile(file, image_name): Promise<string> {
  // Initialize the OSS client. Replace the following parameters with your actual configuration information.
  const client = new OSS(configuration().oss);
  try {
    // const fileStream = fs.createReadStream(file.path);
    // Upload an object to OSS. Replace 'object' with the name that you want the object to have in OSS. Replace 'localfile' with the path of the local file that you want to upload.
    const uploadResult = await client.put(image_name + Date.now(), file.buffer);
    uploadResult.url = uploadResult.url.replace('http', 'https');
    return uploadResult.url;
  } catch (error) {
    console.error('Error:', error);
    // Write your error handling code here.
  }
}

export async function validateStep(
  initialFund: string,
  stepSize: string,
  lowerPrice: string,
  upperPrice: string,
  minNotional: string,
) {
  const initialFundDecimal = new Decimal(initialFund);
  const stepSizeDecimal = new Decimal(stepSize);
  const lowerPriceDecimal = new Decimal(lowerPrice);
  const upperPriceDecimal = new Decimal(upperPrice);
  const minNotionalDecimal = new Decimal(minNotional);
  const numberOfSteps = upperPriceDecimal
    .minus(lowerPriceDecimal)
    .dividedBy(stepSizeDecimal)
    .floor();
  const amountPerStep = initialFundDecimal.dividedBy(numberOfSteps);

  for (let i = 0; i < numberOfSteps.toNumber(); i++) {
    const priceAtStep = lowerPriceDecimal.plus(stepSizeDecimal.times(i));
    const realAmountPerStep = amountPerStep.dividedBy(priceAtStep);

    if (amountPerStep.lessThan(minNotionalDecimal)) {
      console.log(
        `Step ${i + 1}: Adjusting strategy: Amount per step is below the minimum notional.`,
      );
      throw new BadRequestException(
        `Adjust strategy: Increase initial fund, decrease number of steps`,
      );
      // Adjust strategy: Increase initial fund, decrease number of steps, or increase step size
    } else {
      console.log(`Step ${i + 1}:`);
      console.log(`  Price at Step: ${priceAtStep.toFixed(2)}`);
      console.log(`  Amount per Step: ${amountPerStep.toFixed(2)}`);
      console.log(
        `  Real Amount per Step in Base Currency: ${realAmountPerStep.toFixed(6)}`,
      );
    }
  }
}

export async function validateNotional(
  initialFund: string,
  stepSize: string,
  minNotional: string,
) {
  const initialFundDecimal = new Decimal(initialFund);
  const stepSizeDecimal = new Decimal(stepSize);
  const minNotionalDecimal = new Decimal(minNotional);
  const amountPerStep = initialFundDecimal.dividedBy(stepSizeDecimal);

  if (amountPerStep.lessThan(minNotionalDecimal)) {
    throw new BadRequestException(
      `Adjust strategy: Increase initial fund to : ${minNotionalDecimal.mul(stepSizeDecimal)} or decrease number of steps to : ${initialFundDecimal.div(minNotionalDecimal)}`,
    );
    // Adjust strategy: Increase initial fund, decrease number of steps, or increase step size
  }
  return amountPerStep;
}

export function truncateToDecimals(amount, decimal) {
  return new Decimal(amount.mul(10 ** decimal).floor()).div(10 ** decimal);
}

export function roundToDecimals(amount, decimal) {
  const factor = new Decimal(10).pow(decimal);
  return amount.mul(factor).ceil().div(factor);
}

export async function calculateConstBaseAndQuote(
  data: any,
  pair: any,
  currentMarketPrice: string,
) {
  const lowerThreshold = new Decimal(data.lower_threshold);
  const upperThreshold = new Decimal(data.upper_threshold);
  const stepSize = new Decimal(data.step);
  const initialFund = new Decimal(data.initial_fund);
  const minNotional = new Decimal(pair.min_notional);
  const marketPrice = new Decimal(currentMarketPrice);

  const numberOfSteps = upperThreshold
    .minus(lowerThreshold)
    .dividedBy(stepSize);
  // Calculate fee (0.5% of initial fund)
  const fee = initialFund.mul(0);

  // Calculate average price
  // const avgPrice = lowerThreshold.plus(upperThreshold).dividedBy(2);
  // New formula: (initialFund - fee) / avgPrice / step

  
  // Use upperThreshold instead of average price
  // New NEW formula: (initialFund - fee) / upperThreshold / step
  // const avgPrice = new Decimal(upperThreshold);

  let amountPerStep = initialFund
    .minus(fee)
    .dividedBy(upperThreshold) //Replace avgPrice with upperThreshold
    .dividedBy(stepSize);
  amountPerStep = truncateToDecimals(amountPerStep, pair.qty_dp);
  let totalBase = new Decimal(0);
  let totalQuote = new Decimal(0);
  let buyCount = 0;
  let sellCount = 0;
  let buyOrder = [];
  let sellOrder = [];
  let minSize = new Decimal(0);

  for (let i = 0; i < stepSize.toNumber(); i++) {
    let priceAtStep = truncateToDecimals(
      lowerThreshold.plus(numberOfSteps.times(i)),
      pair.price_dp,
    );

    if (amountPerStep.mul(lowerThreshold).greaterThanOrEqualTo(minNotional)) {
      if (priceAtStep.lessThan(marketPrice)) {
        // Place a buy order
        totalQuote = totalQuote.plus(amountPerStep.mul(priceAtStep));
        buyOrder.push({ price: priceAtStep, amount: amountPerStep });
        buyCount++;
      } else {
        priceAtStep = lowerThreshold.plus(numberOfSteps.times(i + 1));
        priceAtStep = truncateToDecimals(priceAtStep, pair.price_dp);
        // Place a sell order
        totalBase = totalBase.plus(amountPerStep);
        sellOrder.push({ price: priceAtStep, amount: amountPerStep });
        sellCount++;
      }
    } else {
      minSize = minNotional.div(lowerThreshold);
      console.log(
        `Step ${i + 1}: Amount per step is below the minimum notional.`,
      );
    }
  }

  console.log(`Total Base Required: ${totalBase} ${pair.base}`);
  console.log(`Total Quote Required: ${totalQuote} ${pair.quote}`);
  console.log(`Buy Orders: ${buyCount}`);
  console.log(`Sell Orders: ${sellCount}`);
  if (!minSize.eq(0)) {
    throw new BadRequestException(
      `Minimum size per require: ${roundToDecimals(minSize, pair.qty_dp)}, current setting size : ${amountPerStep} `,
    );
  }
  return {
    base_required: totalBase,
    quote_required: totalQuote,
    buy_count: buyCount,
    sell_count: sellCount,
    buy_orders: buyOrder,
    sell_orders: sellOrder,
  };
}

export async function refreshConnection() {
  try {
    const response = await axios.put(configuration().stream_url, null);
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

export async function addSocketConnection(pair: string, platform: string) {
  try {
    const body: Record<string, any> = {
      pair: pair,
      platform,
      bot_type: 'ARBITRAGEBOT',
    };
    const response = await axios.post(
      configuration().stream_url + 'exchange/multi-price',
      body,
      null,
    );
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

export async function addGridSocketConnection(pair: string, platform: string) {
  try {
    const body: Record<string, any> = {
      pair: pair,
      platform,
      bot_type: 'GRIDBOT',
    };
    const response = await axios.post(
      configuration().stream_url + 'exchange/multi-price',
      body,
      null,
    );
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

export async function removeSocketConnection(pairId: string): Promise<void> {
  const base = configuration().stream_url.replace(/\/$/, ''); // strip trailing slash
  const url = `${base}/exchange/price/unsubscribe`;

  try {
    const res = await axios.post(url, { pair_id: pairId }, { timeout: 10000 });
  } catch (err) {
    if (err.response) {
      console.error(`Status: ${err.response.status}, Data:`, err.response.data);
    } else if (err.request) {
      console.error(`No response received. Request was sent to: ${url}`);
    } else {
      console.error(`Error message: ${err.message}`);
    }
    throw err;
  }
}

export async function removeGridSocketConnection(
  pairId: string,
): Promise<void> {
  const base = configuration().stream_url.replace(/\/$/, ''); // strip trailing slash
  const url = `${base}/exchange/grid-price/unsubscribe`;

  try {
    const res = await axios.post(url, { pair_id: pairId }, { timeout: 10000 });
  } catch (err) {
    if (err.response) {
      console.error(`Status: ${err.response.status}, Data:`, err.response.data);
    } else if (err.request) {
      console.error(`No response received. Request was sent to: ${url}`);
    } else {
      console.error(`Error message: ${err.message}`);
    }
    throw err;
  }
}

export async function removeMultiSocketConnection(pair: string, platform: string): Promise<void> {
  const base = configuration().stream_url.replace(/\/$/, ''); // strip trailing slash
  const url = `${base}/exchange/multi-price/unsubscribe`;
  const body: Record<string, any> = {
    pair_id: pair,     
    platform_id: platform,
    bot_type: null,
  };


  try {
    const res = await axios.post(url, body, { timeout: 10000 });
    console.log('Unsubscribe response:', res.data);
  } catch (err) {
    if (err.response) {
      console.error(`Status: ${err.response.status}, Data:`, err.response.data);
    } else if (err.request) {
      console.error(`No response received. Request was sent to: ${url}`);
    } else {
      console.error(`Error message: ${err.message}`);
    }
    throw err;
  }
}
