import { User } from 'src/user/entities/user.entity';
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

export enum LogEvent {
  LOGIN = 'login',
  LOGIN_2FA = 'login-2fa',
  REGISTER = 'register',
  RESET_PASSWORD = 'reset-password',
  CHANGE_PASSWORD = 'change-password',
  LOGS = 'logs',
  LOGOUT = 'logout',
  BIND_2FA = 'bind-2fa',
  PREBIND_2FA = 'prebind-2fa',
  UNBIND_2FA = 'unbind-2fa',
}

export enum EventStatus {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
}

@Entity()
export class ActivityLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'enum', enum: LogEvent })
  event: LogEvent;

  @Column('longtext', { nullable: true })
  old_values: string;

  @Column('longtext', { nullable: true })
  new_values: string;

  @Column({ nullable: true })
  endpoint: string;

  @Column({ nullable: true })
  ip_address: string;

  @Column({ nullable: true })
  user_agent: string;

  @Column({ type: 'enum', enum: EventStatus })
  status: EventStatus;

  @Column('longtext', { nullable: true })
  error_message: string;

  @ManyToOne((type) => User, (user) => user.activity_log)
  user: User;

  @CreateDateColumn()
  created_at: string;
}
