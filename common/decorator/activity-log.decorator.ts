import {
  createParamDecorator,
  ExecutionContext,
  SetMetadata,
} from '@nestjs/common';
import { LogEvent } from '../entity/activity-log.entity';

export const LOG_ACTION_KEY = 'log_action';

export const LogActivity = (event: LogEvent) =>
  SetMetadata(LOG_ACTION_KEY, event);

export const ActivityLogMeta = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.activityLogMetadata || {};
  },
);

export const CurrentAdmin = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  },
);
