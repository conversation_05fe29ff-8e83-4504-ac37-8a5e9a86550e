import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Inject,
  Optional,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, throwError } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Equal, EntityManager } from 'typeorm';
import { Request } from 'express';
import {
  ActivityLog,
  EventStatus,
  LogEvent,
} from '../entity/activity-log.entity';
import { LOG_ACTION_KEY } from 'common/decorator/activity-log.decorator';
import { User } from 'src/user/entities/user.entity';

@Injectable()
export class ActivityLogInterceptor implements NestInterceptor {
  constructor(
    private reflector: Reflector,
    @Optional()
    @InjectRepository(ActivityLog)
    private logRepository?: Repository<ActivityLog>,
    private dataSource?: EntityManager,
  ) {}

  private objectToText(obj: any): string {
    if (!obj) return;
    try {
      return JSON.stringify(obj);
    } catch (error) {
      console.error('Failed to convert object to text:', error);
      return '{}';
    }
  }

  private getClientIp(request: Request): string {
    const xff = request.headers['x-forwarded-for'];
    // X-Forwarded-For can be a comma-separated list; take the first IP (client's IP)
    const clientIp = Array.isArray(xff)
      ? xff[0]
      : xff
        ? xff.split(',')[0].trim()
        : request.ip;
    return clientIp || 'Unknown IP';
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const payload = (request as any).user;
    const log_event = this.reflector.get<LogEvent>(
      LOG_ACTION_KEY,
      context.getHandler(),
    );

    if (!log_event) {
      return next.handle();
    }

    const old_values = request.body.originalEntity;

    return next.handle().pipe(
      tap(async (data) => {
        const metadata = (request as any).activityLogMetadata || {};
        const ip_address = await this.getClientIp(request);

        let em;
        if (this.logRepository) {
          em = this.logRepository.manager;
        } else if (this.dataSource) {
          em = this.dataSource;
        } else {
          console.error(
            'No repository, connection or datasource available for activity logging',
          );
          return;
        }

        let user;
        if (log_event === LogEvent.LOGIN) {
          user = await em.findOneBy(User, {
            email: Equal(request.body.email),
          });
        } else if (payload && 'email' in payload) {
          user = await em.findOneBy(User, {
            email: Equal((payload as any).email),
          });
        }

        try {
          const log_data: Partial<ActivityLog> = {
            event: log_event,
            user: user,
            endpoint: request ? `${request.method} ${request.url}` : undefined,
            status: EventStatus.SUCCESS,
            ip_address: ip_address,
            user_agent: request?.headers?.['user-agent'],
          };

          if (request.method !== 'GET') {
            log_data.old_values = this.objectToText(old_values);
            log_data.new_values = this.objectToText({ ...data, ...metadata });
          }

          await em
            .createQueryBuilder()
            .insert()
            .into(ActivityLog)
            .values(log_data)
            .execute();
        } catch (logError) {
          console.error('Failed to log activity:', logError);
        }
      }),
      catchError(async (error) => {
        const metadata = (request as any).activityLogMetadata || {};
        const ip_address = await this.getClientIp(request);

        let em;
        if (this.logRepository) {
          em = this.logRepository.manager;
        } else if (this.dataSource) {
          em = this.dataSource;
        } else {
          console.error(
            'No repository, connection or datasource available for activity logging',
          );
          return throwError(() => error);
        }

        let user;
        if (log_event === LogEvent.LOGIN) {
          user = await em.findOneBy(User, {
            email: Equal(request.body.email),
          });
        } else if (payload && 'email' in payload) {
          user = await em.findOneBy(User, {
            email: Equal((payload as any).email),
          });
        }

        try {
          const is_contract_error =
            error instanceof Error &&
            (error.message.includes('Contract execution failed:') ||
              error.message.includes(
                'An unexpected error occurred during contract interaction',
              ) ||
              error.message.includes('Failed to set ') ||
              error.message.includes('BadRequestException: Contract') ||
              error.message.includes('waitForTransactionReceipt') ||
              error.message.includes('simulateContract') ||
              error.message.includes('writeContract'));

          const err_message = is_contract_error
            ? `Contract error - ${context.getHandler().name || request.path.split('/').pop()}`
            : error instanceof Error
              ? error.message
              : String(error);

          const log_data: Partial<ActivityLog> = {
            event: log_event,
            user: user,
            endpoint: request ? `${request.method} ${request.url}` : undefined,
            status: EventStatus.FAILED,
            error_message: err_message,
            ip_address: ip_address,
            user_agent: request?.headers?.['user-agent'],
          };

          if (request.method !== 'GET') {
            log_data.old_values = this.objectToText(old_values);
            log_data.new_values = this.objectToText(metadata);
          }

          await em
            .createQueryBuilder()
            .insert()
            .into(ActivityLog)
            .values(log_data)
            .execute();
        } catch (logError) {
          console.error('Failed to log activity:', logError);
        }

        if (error instanceof Error) {
          throw error;
        } else {
          throw new Error(String(error));
        }
      }),
    );
  }
}
