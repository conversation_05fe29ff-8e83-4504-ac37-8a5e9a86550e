import {
  Catch,
  HttpException,
  ExceptionFilter,
  ArgumentsHost,
  BadRequestException,
  HttpStatus,
} from '@nestjs/common';
import { Response, Request } from 'express';
import { QueryFailedError } from 'typeorm';
//catch the exception name HttpException
@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    console.log('in here');
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    console.log(exception);
    if (exception instanceof HttpException) {
      let status = exception.getStatus();
      if (
        exception.message ==
        'duplicate key value violates unique constraint "unique_index"'
      ) {
        exception.message = 'duplicate request_id';
      } else if (
        exception.message.search('admin_email') != -1 ||
        exception.message.search('duplicate_email') != -1
      ) {
        exception.message = 'Email had been taken';
      } else if (exception.message.search('duplicate_symbol') != -1) {
        exception.message = 'Symbol already support in system';
      } else if (exception.message.search('duplicate_name') != -1) {
        exception.message = 'Name had been taken';
      } else if (exception.message.search('duplicate_currency') != -1) {
        exception.message = 'Currency already support in system';
      } else if (exception.message.search('Too Many Requests') != -1) {
        exception.message =
          'Too Many Requests , Only send email every 5 minutes';
      } else if (exception.message.search('Forbidden resource') != -1) {
        exception.message = 'Invalid permission';
        status = 400;
      }
      console.log(exception.message);
      //this is the return format of the httpException
      return response.status(status).json({
        status: 'fail',
        message: exception.message,
      });
    } else if (exception instanceof QueryFailedError) {
      console.log('Database error: ', exception.message);
      return response.status(400).json({
        status: 'fail',
        message:
          'Unexpected error happened.Please contact admin or try again later',
      });
    }
    //this is the return format of the httpException
  }
}

@Catch()
export class ValidationExceptionFilter implements ExceptionFilter {
  public catch(exception, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    return (
      response
        .status(status)
        // you can manipulate the response here
        .json({
          statusCode: status,
          timestamp: new Date().toISOString(),
          path: request.url,
        })
    );
  }
}
