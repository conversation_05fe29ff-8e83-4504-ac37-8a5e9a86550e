import { BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { decrypted } from 'common/util/utils';
import configuration from 'config/configuration';
import { handleTokenizeError } from './exchange-error.helper';
// import * as jwt from 'jsonwebtoken'; // Uncomment when implementing token generation

// Define placeholder interfaces based on Binance parameters,
// but implementations will need to adapt to Tokenize needs.
interface TokenizeOrderDto {
  // Based on Binance DTO structure for name matching
  symbol: string; // Corresponds to Tokenize 'market'
  side: 'BUY' | 'SELL'; // Used to determine Tokenize endpoint (/order/buy or /order/sell)
  type: 'MARKET' | 'LIMIT'; // Corresponds to Tokenize 'orderType' ('market', 'limit')
  quantity?: string; // Corresponds to Tokenize 'units'
  price?: string; // Corresponds to Tokenize 'price' (for limit)
  // Tokenize specific params (not in Binance DTO, add as needed):
  orderType?: 'limit' | 'market' | 'stop limit' | 'trailing stop';
  units?: string;
  distancePrice?: string;
  distanceType?: 'fiat' | 'percent';
  // Binance specific params (may not apply directly to Tokenize):
  timeInForce?: string;
  quoteOrderQty?: string;
  post_only?: string; // Not in Tokenize docs
}

interface TokenizeTradesHistoryParams {
  // Based on Binance params
  symbol: string; // Corresponds to Tokenize 'market'
  startTime?: number; // Optional in Tokenize getAllOrders/getCurrentOpenOrders
  endTime?: number; // Optional in Tokenize getAllOrders/getCurrentOpenOrders
  limit?: number; // Optional in Tokenize getTradesHistory
}

export class TokenizePlatformHelper {
  private readonly baseUrl: string = configuration().exchanges.tokenize.apiUrl;

  private axiosInstance: AxiosInstance | null = null; // Initialize as null or in constructor/init
  private apiKey: string | null = null;
  private secretKey: string | null = null;
  private apiToken: string | null = null;
  private tokenExpiry: number | null = null;

  /**
   * Initialize the helper with Tokenize credentials.
   */
  async init(
    platformType: string,
    apiKey: string,
    secretKey: string,
  ): Promise<void> {
    this.apiKey = apiKey;
    this.secretKey = await decrypted(
      secretKey,
      configuration().encryption.secret,
    );

    // TODO: Initialize Axios instance with base URL and interceptors
    this.axiosInstance = axios.create({
      baseURL: this.baseUrl /* ... other config */,
    });
    this.axiosInstance.interceptors.response.use(
      (res) => this.handleSuccessResponse(res),
      (err) => this.handleErrorResponse(err),
    );

    console.log('TokenizePlatformHelper initialized.');
    // Consider generating the first token here or on the first secured call
    // this.generateApiToken();
  }

  private generateSignature(payload?: any) {
    let jwtService = new JwtService();
    console.log(this.secretKey);
    return jwtService.sign(
      { ...payload, id: this.apiKey },
      {
        secret: this.secretKey,
        // expiresIn: '5m',
      },
    );
  }

  private buildHeaders(signature: string): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    };
    console.log(signature, 'signature');
    headers['X-TKX-TOKEN'] = signature;

    return headers;
  }

  private handleSuccessResponse(response: AxiosResponse): any {
    // TODO: Check Tokenize status field (response.data.status === 'success')
    // Extract and return response.data.data
    // Throw error if status is not 'success'
    if (response.data?.status !== 'success') {
      throw new BadRequestException(
        response.data?.message || 'Tokenize API returned non-success status',
      );
    }
    return response.data.data;
  }

  private handleErrorResponse(error: any): Promise<never> {
    // TODO: Parse error (AxiosError, response data, status code)
    // Throw appropriate NestJS Exception (e.g., BadRequestException)
    const message =
      error.response?.data?.message ||
      error.message ||
      'Tokenize API request failed';
    const status = error.response?.status || 500;
    console.error(
      `Tokenize API Error: [${status}] ${message}`,
      error.response?.data,
    );
    return Promise.reject(new BadRequestException(`[${status}] ${message}`));
  }

  private getClient(): AxiosInstance {
    if (!this.axiosInstance)
      throw new Error('Axios not initialized. Call init() first.');
    return this.axiosInstance;
  }

  // ==================================
  // Public Methods (Matching Binance Helper Names)
  // ==================================

  /**
   * Binance Purpose: Create a listen key for user data stream.
   * Tokenize Equivalent: NO DIRECT EQUIVALENT found in provided docs for WebSocket user streams.
   * Implementation: Likely needs to throw "Not Supported" or remain unimplemented.
   */
  async createListenKey(): Promise<string> {
    console.warn(
      'createListenKey: No equivalent found in Tokenize API docs for user streams.',
    );
    throw new Error('TokenizePlatformHelper.createListenKey is not supported.');
    // return Promise.resolve(''); // Or return empty/null if needed
  }

  /**
   * Binance Purpose: Extend (ping) the listen key to keep the data stream alive.
   * Tokenize Equivalent: NO DIRECT EQUIVALENT found.
   * Implementation: Likely needs to throw "Not Supported".
   * @param listenKey - The key to extend (not applicable here).
   */
  async extendListenKey(listenKey: string): Promise<void> {
    console.warn(
      `extendListenKey: No equivalent found in Tokenize API docs for user streams. Key: ${listenKey}`,
    );
    throw new Error('TokenizePlatformHelper.extendListenKey is not supported.');
    // return Promise.resolve();
  }

  /**
   * Binance Purpose: Create a new order (spot).
   * Tokenize Equivalent: POST /order/buy or POST /order/sell.
   * Implementation: Determine path from orderDto.side. Map parameters:
   *   - Binance `symbol` -> Tokenize `market`
   *   - Binance `quantity` -> Tokenize `units` (if type=LIMIT)
   *   - Binance `quoteOrderQty` -> Tokenize `units` (if type=MARKET, needs clarification if Tokenize supports quote qty)
   *   - Binance `type` -> Tokenize `orderType`
   *   - Handle Tokenize-specific params like `distancePrice`/`distanceType` if needed.
   * @param orderDto - Order details (adapt structure for Tokenize needs).
   */
  async createOrder(orderDto: TokenizeOrderDto): Promise<any> {
    console.log(
      'Calling Tokenize createOrder (mapped from Binance name)',
      orderDto,
    );
    // TODO: Implement logic to call POST /order/buy or /order/sell based on orderDto.side
    // Map parameters carefully. Check Tokenize docs for MARKET order quantity type (base or quote).
    const side = orderDto.side.toLowerCase(); // 'buy' or 'sell'
    const url = `/order/${side}`;
    const payload = {
      // Adapt this payload based on Tokenize API requirements
      market: orderDto.symbol, // Map symbol to market
      units: orderDto.quantity || orderDto.units, // Map quantity/units
      orderType: orderDto.type.toLowerCase(), // Map type to orderType (limit/market)
      price: orderDto.price, // Include if limit order
      // Add distancePrice/distanceType if orderDto contains them for trailing stops
    };
    // Remove undefined fields from payload if necessary
    Object.keys(payload).forEach(
      (key) => payload[key] === undefined && delete payload[key],
    );
    let signature = this.generateSignature(payload);
    let headers = this.buildHeaders(signature);
    return this.getClient().post(url, payload, { headers });
    throw new Error(
      'TokenizePlatformHelper.createOrder not fully implemented.',
    );
  }

  /**
   * Binance Purpose: Cancel an order.
   * Tokenize Equivalent: DELETE /order/:orderid.
   * Implementation: Tokenize only needs orderId in the path, Binance needs symbol+orderId.
   *   This function signature (from Binance) includes `symbol`, which is unused by Tokenize's DELETE endpoint.
   * @param orderId - The ID of the order to cancel.
   * @param symbol - Market symbol (UNUSED by Tokenize DELETE /order/:orderid endpoint).
   */
  async cancelOrder(orderId: string, symbol: string): Promise<any> {
    console.log(
      `Calling Tokenize cancelOrder (mapped from Binance name) for ID: ${orderId}, Symbol: ${symbol} (Symbol is unused by Tokenize API)`,
    );
    // TODO: Implement logic to call DELETE /order/{orderId}
    const url = `/order/${String(orderId)}`;
    let signature = this.generateSignature({ orderId });
    let headers = this.buildHeaders(signature);
    return this.getClient().delete(url, { headers });
  }

  /**
   * Binance Purpose: Read account balance (all assets).
   * Tokenize Equivalent: GET /account/balances.
   * Implementation: Call the endpoint. The response structure needs significant mapping
   *   to potentially match the *expected* format if downstream code relies on Binance format.
   */
  async readBalance(): Promise<any> {
    try {
      console.log('Calling Tokenize readBalance (mapped from Binance name)');
      // TODO: Implement logic to call GET /account/balances
      const url = `${this.baseUrl}/account/balances`;
      let signature = this.generateSignature();
      let header = this.buildHeaders(signature);
      const response = await axios.get(url, { headers: header });
      const formattedBalances = response.data.data.map(
        ({ currency, balance, inOrder }) => ({
          symbol: currency,
          avai_balance: balance,
          freeze_balance: inOrder,
          avai_balance_quote: 0,
          freeze_balance_quote: 0,
        }),
      );
      console.log(formattedBalances);
      return formattedBalances;
    } catch (error) {
      handleTokenizeError(error);
    }
  }

  async readNonZeroBalance(): Promise<any> {
    try {
      console.log('Calling Tokenize readBalance (mapped from Binance name)');
      // TODO: Implement logic to call GET /account/balances
      const url = `${this.baseUrl}/account/balances`;
      let signature = this.generateSignature();
      let header = this.buildHeaders(signature);
      const response = await axios.get(url, { headers: header });
      const formattedBalances = response.data.data.map(
        ({ currency, balance, inOrder }) => ({
          symbol: currency,
          avai_balance: balance,
          freeze_balance: inOrder,
          avai_balance_quote: 0,
          freeze_balance_quote: 0,
        }),
      );
      console.log(formattedBalances);
      return formattedBalances;
    } catch (error) {
      handleTokenizeError(error);
    }
  }

  async readActivePairFromExchange(): Promise<any[]> {
    try {
      const url = `${this.baseUrl}/market/get-markets`;
      const response = await axios.get(url);

      if (!response) {
        throw new BadRequestException('Tokenize API returned no data');
      }

      const flattenResponseData = response.data.data.flatMap((group) =>
        group && Array.isArray(group.list) ? group.list : [],
      );
      // ==========================================================================
      // WARNING: Critical Mapping Logic - Potential Point of Confusion
      // ==========================================================================
      // Developer Note: The following mapping logic takes data from the Tokenize
      // API (`item` object from /market/get-markets) and maps it to our internal
      // database schema fields.
      //
      // *** BE EXTREMELY CAREFUL ***
      // Several fields are INTENTIONALLY SWAPPED here compared to their names
      // in the Tokenize API response. This appears necessary due to how Tokenize
      // defines base/quote and their associated increments/precision, or potentially
      // to match conventions used elsewhere in our system or database.
      //
      // DO NOT change this mapping without fully understanding the Tokenize API's
      // field definitions AND our internal data requirements. When debugging or
      // referencing the API docs, remember that the mapped fields (`mapResponseData`)
      // do NOT directly correspond 1:1 with the raw API field names (`item`).
      // ==========================================================================
      const mapResponseData = flattenResponseData
        .filter((item) => item && item.status === 'active')
        .map((item: any) => ({
          symbol: this.formatSymbolTokenizeToQuoteBase(item.marketName),
          tick: Number(item.quoteIncrement),
          step: Number(item.baseIncrement),
          min_notional: Number(item.floor),
          max_notional: Number(item.ceiling),
          base: item.marketCurrency,
          quote: item.baseCurrency,
          price_dp: Number(item.quoteTruncate),
          qty_dp: Number(item.baseTruncate),
        }));
      return mapResponseData;
    } catch (error) {
      console.error(
        `Error fetching or processing data in Tokenize readActivePairFromExchange: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Binance Purpose: Extend (ping) the listen key (alias/duplicate in original code).
   * Tokenize Equivalent: NO DIRECT EQUIVALENT found.
   * Implementation: Likely needs to throw "Not Supported".
   * @param listenKey - The key to extend (not applicable here).
   */
  async extendUserStreamKey(listenKey: string): Promise<void> {
    console.warn(
      `extendUserStreamKey: No equivalent found in Tokenize API docs for user streams. Key: ${listenKey}`,
    );
    throw new Error(
      'TokenizePlatformHelper.extendUserStreamKey is not supported.',
    );
    // return Promise.resolve();
  }

  /**
   * Binance Purpose: Get all active/open orders for a symbol or all symbols.
   * Tokenize Equivalent: GET /order/buy/open and GET /order/sell/open.
   * Implementation: Needs to call *both* buy/open and sell/open endpoints and combine results.
   *   Parameter mapping: `symbol` (Binance) -> `market` (Tokenize).
   * @param symbol - Optional: Market symbol (maps to Tokenize 'market').
   */
  async getAllActiveOrders(symbol?: string): Promise<any> {
    console.log(
      `Calling Tokenize getAllActiveOrders (mapped from Binance name) for Symbol/Market: ${symbol || 'All'}`,
    );
    // TODO: Implement logic to call GET /order/buy/open and GET /order/sell/open
    // Combine the results. Filter by 'market' if 'symbol' is provided.
    const params = symbol ? { market: symbol } : {};
    // const buyOrders = await this.getClient().get('/order/buy/open', { params, headers: this.buildHeaders(true) });
    // const sellOrders = await this.getClient().get('/order/sell/open', { params, headers: this.buildHeaders(true) });
    // return [...buyOrders, ...sellOrders]; // Combine arrays
    throw new Error(
      'TokenizePlatformHelper.getAllActiveOrders not fully implemented.',
    );
  }

  /**
   * Binance Purpose: Get details of a specific order.
   * Tokenize Equivalent: GET /order/buy?orderId={id} or GET /order/sell?orderId={id}.
   * Implementation: Requires knowing the 'side' (buy/sell) which is *not* in the Binance signature.
   *   This function needs modification (add `side` param) or complex logic (try both endpoints).
   *   Parameter mapping: `symbol` (Binance) -> `market` (Tokenize, but endpoint doesn't use it). `orderId` maps directly.
   * @param symbol - Market symbol (UNUSED by Tokenize GET /order/{side}?orderId=...).
   * @param orderId - The ID of the order.
   * @param side - // <<< ADDED/REQUIRED for Tokenize: 'buy' or 'sell' to determine the correct endpoint.
   */
  async getOrderDetails(
    symbol: string,
    orderId: string,
    side: 'buy' | 'sell',
  ): Promise<any> {
    console.log(
      `Calling Tokenize getOrderDetails (mapped from Binance name) for ID: ${orderId}, Symbol: ${symbol} (Symbol unused), Side: ${side}`,
    );
    if (!side) {
      throw new Error(
        "Tokenize getOrderDetails requires 'side' parameter ('buy' or 'sell').",
      );
    }
    // TODO: Implement logic to call GET /order/{side}?orderId={orderId}
    const url = `/order/${side}`;
    const params = { orderId: String(orderId) }; // Ensure correct param name ('orderId' or 'orderld')
    // return this.getClient().get(url, { params, headers: this.buildHeaders(true) });
    throw new Error(
      'TokenizePlatformHelper.getOrderDetails not fully implemented.',
    );
  }

  /**
   * Binance Purpose: Get user's trade history.
   * Tokenize Equivalent: Possibly GET /order/buy/all and GET /order/sell/all (filtering for completed/cancelled).
   *   The public GET /market/history is for *public* trades, not user-specific history.
   * Implementation: Call GET /order/buy/all and GET /order/sell/all. Filter results by status.
   *   Parameter mapping: `symbol` -> `market`. `startTime`/`endTime`/`limit` might map.
   * @param params - Parameters including symbol (market), and optional time/limit filters.
   */
  async getTradesHistory(params: TokenizeTradesHistoryParams): Promise<any> {
    console.log(
      'Calling Tokenize getTradesHistory (mapped from Binance name)',
      params,
    );
    // TODO: Implement logic to call GET /order/buy/all and /order/sell/all
    // Combine results and potentially filter for completed orders.
    // This might not perfectly match Binance 'myTrades' which shows actual trade executions.
    const queryParams: any = {
      market: params.symbol, // Map symbol to market
      startTime: params.startTime,
      endTime: params.endTime,
      // Tokenize doesn't explicitly list 'limit' for these endpoints, verify if it works.
    };
    Object.keys(queryParams).forEach(
      (key) => queryParams[key] === undefined && delete queryParams[key],
    );

    // const buyHistory = await this.getClient().get('/order/buy/all', { params: queryParams, headers: this.buildHeaders(true) });
    // const sellHistory = await this.getClient().get('/order/sell/all', { params: queryParams, headers: this.buildHeaders(true) });
    // Combine and potentially filter/map buyHistory and sellHistory
    // return combinedHistory;
    throw new Error(
      'TokenizePlatformHelper.getTradesHistory not fully implemented.',
    );
  }

  async getOrderBook(symbol: string, limit = 100): Promise<any> {
    try {
      const params = `market=${this.formatSymbolQuoteBaseToTokenize(symbol)}&limit=${limit}`;
      const url = `${this.baseUrl}/market/orderbook?${params}`;
      const response = await axios.get(url);
      return response.data.data;
    } catch (error) {
      console.error(
        `Error executing Tokenize getOrderBook for market ${symbol}: ${error.message}`,
      );
      throw new Error(
        'TokenizePlatformHelper.getOrderBook not fully implemented.',
      );
    }
  }

  async getTickerBySymbol(symbol: string): Promise<any> {
    try {
      const params = this.formatSymbolQuoteBaseToTokenize(symbol);
      const url = `${this.baseUrl}/market/ticker/24h?market=${params}`;
      const response = await axios.get(url);
      return { close: response.data.data.lastPrice };
    } catch (error) {
      console.error(
        `Error during Tokenize API call in getTickerBySymbol for ${symbol}: ${error.message}`,
      );
    }
  }

  async getAllTicker(): Promise<any[]> {
    try {
      const url = `${this.baseUrl}/market/get-summaries`;
      const response = await axios.get(url);

      if (!response) {
        throw new BadRequestException('Tokenize API returned no data');
      }

      const simplified = response.data.data.map((item: any) => ({
        symbol: this.formatSymbolTokenizeToQuoteBase(item.market),
        price: item.lastPrice,
      }));

      return simplified;
    } catch (error) {
      console.error(
        `Error fetching or processing data in Tokenize getAllTicker: ${error.message}`,
      );
      throw error;
    }
  }

  private formatSymbolTokenizeToQuoteBase(tokenizeMarket: string): string {
    if (typeof tokenizeMarket !== 'string' || !tokenizeMarket.includes('-')) {
      console.warn(
        `formatSymbolTokenizeToQuoteBase: Unexpected market format received: ${tokenizeMarket}. Returning original.`,
      );
      return tokenizeMarket;
    }
    const parts = tokenizeMarket.split('-');
    if (parts.length === 2) {
      return parts[1] + parts[0];
    } else {
      console.warn(
        `formatSymbolTokenizeToQuoteBase: Market string "${tokenizeMarket}" did not split into exactly two parts. Returning original.`,
      );
      return tokenizeMarket;
    }
  }

  private formatSymbolQuoteBaseToTokenize(quoteBaseSymbol: string): string {
    if (typeof quoteBaseSymbol !== 'string' || quoteBaseSymbol.length <= 3) {
      throw new BadRequestException(
        `Invalid symbol format for transformation: ${quoteBaseSymbol}`,
      );
    }
    if (quoteBaseSymbol.endsWith('MYR')) {
      const quote = quoteBaseSymbol.slice(0, -3);
      if (!quote)
        throw new BadRequestException(
          `Invalid symbol format: ${quoteBaseSymbol}`,
        );
      return `MYR-${quote}`;
    }
    throw new BadRequestException(
      `Unsupported symbol format for Tokenize API: ${quoteBaseSymbol}`,
    );
  }

  async proxyCaller() {
    // const proxy = '*******************************************';
    // const agent = new Proxy.HttpsProxyAgent(proxy);
    // axios
    //   .get('https://api.binance.com/api/v3/time', { httpsAgent: agent })
    //   .then((res) => console.log(res.data))
    //   .catch((err) => console.error('Error:', err));
  }
}
