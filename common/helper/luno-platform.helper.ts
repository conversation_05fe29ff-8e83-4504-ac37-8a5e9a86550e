import { BadRequestException } from '@nestjs/common';
import axios from 'axios';
import * as crypto from 'crypto';
import { handleLunoError } from './exchange-error.helper';
import { decrypted } from 'common/util/utils';
import configuration from 'config/configuration';
import * as Proxy from 'https-proxy-agent';

export class LunoPlatformHelper {
  // Update base URL for Luno API
  private readonly baseUrl: string = configuration().exchanges.luno?.apiUrl || 'https://api.luno.com';
  
  private tax: number;
  private apiKey: string;
  private secretKey: string;
  private auth: any;
  private proxy: string;
  private agent: Proxy.HttpsProxyAgent<string>;

  // Global symbol conversion maps
  private readonly INTERNAL_TO_LUNO_SYMBOL = {
    'BTC': 'XBT',
    'POL': 'MATIC'
  };

  private readonly LUNO_TO_INTERNAL_SYMBOL = {
    'XBT': 'BTC',
    'MATIC': 'POL'
  };

  // Trading pair conversion for APIs that need full pairs
  private readonly INTERNAL_TO_LUNO_PAIR = {
    'BTCUSDT': 'XBTUSDT',
    'BTCMYR': 'XBTMYR',
    'POLUSDT': 'MATICUSDT',
    'POLMYR': 'MATICMYR'
  };

  private readonly LUNO_TO_INTERNAL_PAIR = {
    'XBTUSDT': 'BTCUSDT',
    'XBTMYR': 'BTCMYR',
    'MATICUSDT': 'POLUSDT',
    'MATICMYR': 'POLMYR'
  };

  /**
   * Convert internal symbol to Luno symbol for API calls
   */
  private convertToLunoSymbol(symbol: string): string {
    return this.INTERNAL_TO_LUNO_SYMBOL[symbol] || symbol;
  }

  /**
   * Convert Luno symbol to internal symbol for responses
   */
  private convertFromLunoSymbol(symbol: string): string {
    return this.LUNO_TO_INTERNAL_SYMBOL[symbol] || symbol;
  }

  /**
   * Convert internal trading pair to Luno trading pair for API calls
   */
  private convertToLunoPair(pair: string): string {
    return this.INTERNAL_TO_LUNO_PAIR[pair] || pair;
  }

  /**
   * Convert Luno trading pair to internal trading pair for responses
   */
  private convertFromLunoPair(pair: string): string {
    return this.LUNO_TO_INTERNAL_PAIR[pair] || pair;
  }



  async init(
    platformType: string,
    apiKey: string,
    secretKey: string,
  ): Promise<void> {
    if (secretKey != '') {
      this.secretKey = await decrypted(
        secretKey,
        configuration().encryption.secret,
      );
    }
    this.proxy = `http://${configuration().decodo.username}:${configuration().decodo.password}@${configuration().decodo.url}`;
    this.agent = new Proxy.HttpsProxyAgent(this.proxy);
    this.apiKey = apiKey;
    this.tax = 0.01;
    this.auth = { username: this.apiKey, password: this.secretKey };
    return;
  }

  // Luno uses Basic Auth instead of HMAC signing
  private buildHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
    };
  }

  // Luno doesn't use listen keys - implement dummy methods
  async createListenKey(): Promise<string> {
    return 'luno-dummy-listen-key';
  }

  async extendListenKey(listenKey: string): Promise<void> {
    // No-op for Luno
    return;
  }

  // CORRECTED: Side mapping (BUY->BID, SELL->ASK)
async createOrder(orderDto: {
  symbol: string;
  side: 'BUY' | 'SELL';
  type: 'MARKET' | 'LIMIT';
  quantity?: string;
  price?: string;
  post_only?: string;
  timeInForce?: string;
  quoteOrderQty?: string;
}): Promise<any> {
  try {
    console.log('[Luno createOrder] Step 1: Received orderDto:', orderDto);
    
    // Convert internal pair to Luno pair
    const lunoSymbol = this.convertToLunoPair(orderDto.symbol);
    console.log('[Luno createOrder] Step 2: Converted symbol to Luno pair:', lunoSymbol);
    
    // Prepare Luno-specific parameters
    const lunoParams: any = {
      pair: lunoSymbol,
        // CORRECTED: Use ASK for sell, BID for buy
      type: orderDto.side === 'BUY' ? 'BID' : 'ASK'
    };
    console.log('[Luno createOrder] Step 3: Initial lunoParams:', lunoParams);
    
    if (orderDto.type === 'LIMIT') {
      // For limit orders, we need price and volume
      if (!orderDto.price || !orderDto.quantity) {
        throw new BadRequestException('LIMIT orders require both price and quantity');
      }
      lunoParams.price = orderDto.price;
      lunoParams.volume = orderDto.quantity;
      console.log('[Luno createOrder] Step 4: LIMIT order, set price and volume:', lunoParams);
      
      // Add post_only if specified
      if (orderDto.post_only === 'true') {
        lunoParams.post_only = true;
        console.log('[Luno createOrder] Step 5: LIMIT order, set post_only:', lunoParams);
      }
    } else {
      // For market orders, Luno uses different parameters
      if (orderDto.side === 'BUY') {
        // For market buy orders, specify counter_volume (amount in quote currency)
        if (!orderDto.quoteOrderQty) {
          throw new BadRequestException('MARKET BUY orders require quoteOrderQty');
        }
        lunoParams.counter_volume = orderDto.quoteOrderQty;
        console.log('[Luno createOrder] Step 6: MARKET BUY, set counter_volume:', lunoParams);
      } else {
        // For market sell orders, specify base_volume (amount in base currency)
        if (!orderDto.quantity) {
          throw new BadRequestException('MARKET SELL orders require quantity');
        }
        lunoParams.base_volume = orderDto.quantity;
        console.log('[Luno createOrder] Step 7: MARKET SELL, set base_volume:', lunoParams);
      }
    }
    
    console.log('[Luno createOrder] Step 8: Final lunoParams before API call:', lunoParams);
    
    const response = await axios.post(
      `${this.baseUrl}/api/1/postorder`,
      lunoParams,
      {
        auth: this.auth,
        headers: this.buildHeaders(),
        httpsAgent: this.agent,
      }
    );
    
    console.log('[Luno createOrder] Step 9: Luno API response:', response.data);
    
    return { 
      id: response.data.order_id, 
      price: response.data.limit_price || orderDto.price
    };
  } catch (error) {
    // console.error('[Luno createOrder] ERROR:', error);
    if (error.response) {
      console.error('[Luno createOrder] ERROR RESPONSE DATA:', error.response.data);
    }
    console.error('[Luno createOrder] ERROR PARAMS:', orderDto);
    handleLunoError(error);
  }
}

  // Cancel order for Luno - CORRECTED
  async cancelOrder(orderId: string, symbol: string): Promise<any> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/api/1/stoporder`,
        { order_id: orderId },
        {
          auth: this.auth,
          headers: this.buildHeaders(),
          httpsAgent: this.agent,
        }
      );
      return response.data;
    } catch (error) {
      console.error('Luno cancelOrder error:', 
        error.response?.data || error.message,
        'OrderID:', orderId
      );
      handleLunoError(error);
    }
  }

  // FIXED: Balance calculation with symbol conversion
  async readBalance(): Promise<any> {
    try {
      // console.log('[Luno readBalance] - Start');
      const response = await axios.get(`${this.baseUrl}/api/1/balance`, {
        auth: this.auth,
        httpsAgent: this.agent,
      });
      // console.log('[Luno readBalance] - API response:', response.data);
      const formattedBalances = response.data.balance.map((balance: any) => {
        // const total = parseFloat(balance.balance) || 0;
        const reserved = parseFloat(balance.reserved) || 0;
        const unconfirmed = parseFloat(balance.unconfirmed) || 0;
        const available = parseFloat(balance.balance) || 0; // The balance showed is already  the available balance
        return {
          symbol: this.convertFromLunoSymbol(balance.asset), // Convert Luno symbol to internal
          avai_balance: available.toString(),
          freeze_balance: (reserved + unconfirmed).toString(),
          avai_balance_quote: '0',
          freeze_balance_quote: '0',
        };
      });
      // console.log('[Luno readBalance] - Formatted:', formattedBalances);
      return formattedBalances;
    } catch (error) {
      console.error('[Luno readBalance] ERROR:', error.response?.data || error.message);
      handleLunoError(error);
    }
  }

  async readNonZeroBalance(): Promise<any> {
    try {
      const balances = await this.readBalance();
      return balances.filter((balance: any) => 
        parseFloat(balance.avai_balance) > 0 || 
        parseFloat(balance.freeze_balance) > 0
      );
    } catch (error) {
      console.error('Luno readNonZeroBalance error:', error.response?.data || error.message);
      handleLunoError(error);
    }
  }

  // Get active trading pairs using the markets endpoint for cleaner base/quote separation
  async readActivePairFromExchange(): Promise<any[]> {
    try {
      // console.log('[Luno readActivePairFromExchange] - Start');
      // Use markets endpoint which provides clean base_currency and counter_currency separation
      const response = await axios.get(`${this.baseUrl}/api/exchange/1/markets`, {
        httpsAgent: this.agent,
      });
      // console.log('[Luno readActivePairFromExchange] - API response:', response.data);
      const formattedPairs = response.data.markets.map((market: any) => {
        const baseSymbol = this.convertFromLunoSymbol(market.base_currency);
        const quoteSymbol = this.convertFromLunoSymbol(market.counter_currency);
        return {
          symbol: `${baseSymbol}${quoteSymbol}`,
          status: market.trading_status === 'ACTIVE' ? 'TRADING' : 'HALTED'
        };
      });
      // console.log('[Luno readActivePairFromExchange] - Formatted:', formattedPairs);
      return formattedPairs;
    } catch (error) {
      console.error('Luno readActivePairFromExchange error:', error.response?.data || error.message);
      handleLunoError(error);
    }
  }

  // Not needed for Luno but required by interface
  async extendUserStreamKey(listenKey: string): Promise<void> {
    // No-op for Luno
    return;
  }

  // Get all active orders - CORRECTED
  async getAllActiveOrders(symbol?: string): Promise<any> {
    try {
      const params: Record<string, any> = { state: 'PENDING' };
      if (symbol) {
        // Convert internal pair to Luno pair
        params.pair = this.convertToLunoPair(symbol);
      }

      const response = await axios.get(`${this.baseUrl}/api/1/listorders`, {
        auth: this.auth,
        params,
        httpsAgent: this.agent,
      });
      
      // Convert pair symbols in each order from Luno to internal format
      return response.data.orders.map((order: any) => ({
        ...order,
        pair: this.convertFromLunoPair(order.pair)
      }));
    } catch (error) {
      console.error('Luno getAllActiveOrders error:', error.response?.data || error.message);
      handleLunoError(error);
    }
  }

  // Get order details - CORRECTED
  async getOrderDetails(symbol: string, orderId: string): Promise<any> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/api/1/orders/${orderId}`,
        {
          auth: this.auth,
          httpsAgent: this.agent,
        }
      );
      
      // Convert pair symbol from Luno to internal format
      const orderData = response.data;
      if (orderData.pair) {
        orderData.pair = this.convertFromLunoPair(orderData.pair);
      }
      
      return orderData;
    } catch (error) {
      console.error('Luno getOrderDetails error:', error.response?.data || error.message);
      handleLunoError(error);
    }
  }

  // IMPROVED: Trade history with proper limit handling and symbol conversion
  async getTradesHistory(params: {
    symbol: string;
    startTime?: number;
    endTime?: number;
    limit?: number;
  }): Promise<any> {
    try {
      // Convert internal pair to Luno pair
      const lunoSymbol = this.convertToLunoPair(params.symbol);
      
      const requestParams: Record<string, any> = { pair: lunoSymbol };
      
      // Convert startTime from timestamp to since parameter
      if (params.startTime) {
        requestParams.since = params.startTime;
      }
      
      // Apply API-level limiting (Luno max: 1000)
      if (params.limit) {
        requestParams.limit = Math.min(params.limit, 1000);
      }

      const response = await axios.get(`${this.baseUrl}/api/1/listtrades`, {
        auth: this.auth,
        params: requestParams,
        httpsAgent: this.agent,
      });

      let trades = response.data.trades;
      
      // Client-side filtering for endTime
      if (params.endTime) {
        trades = trades.filter((trade: any) => 
          parseInt(trade.timestamp) <= params.endTime
        );
      }

      // Convert pair symbols in each trade from Luno to internal format
      return trades.map((trade: any) => ({
        ...trade,
        pair: this.convertFromLunoPair(trade.pair)
      }));
    } catch (error) {
      console.error('Luno getTradesHistory error:',
        error.response?.data || error.message,
        'Params:', params
      );
      handleLunoError(error);
    }
  }

  // Get order book - CORRECTED
  async getOrderBook(symbol: string, limit = 100): Promise<any> {
    try {
      // Convert internal pair to Luno pair
      const lunoSymbol = this.convertToLunoPair(symbol);
      
      // Use the full orderbook endpoint instead of orderbook_top
      const response = await axios.get(`${this.baseUrl}/api/1/orderbook`, {
        params: { pair: lunoSymbol },
        httpsAgent: this.agent,
      });

      // Transform to match Binance format and apply limit
      const bids = response.data.bids.slice(0, limit).map((bid: any) => ({
        price: bid.price,
        qty: bid.volume,
      }));
      
      const asks = response.data.asks.slice(0, limit).map((ask: any) => ({
        price: ask.price,
        qty: ask.volume,
      }));

      return { bids, asks };
    } catch (error) {
      console.error('Luno getOrderBook error:', error.response?.data || error.message);
      handleLunoError(error);
    }
  }

  // Get ticker data - CORRECTED
  async getTickerBySymbol(symbol: string): Promise<any> {
    try {
      // Convert internal pair to Luno pair
      const lunoSymbol = this.convertToLunoPair(symbol);
      
      const response = await axios.get(`${this.baseUrl}/api/1/ticker`, {
        params: { pair: lunoSymbol },
        httpsAgent: this.agent,
      });
      return { close: response.data.last_trade };
    } catch (error) {
      console.error('Luno getTickerBySymbol error:', error.response?.data || error.message);
      handleLunoError(error);
    }
  }

  // Get all tickers - CORRECTED Not used since it give back a whole pair by not splitting the base and quote
  async getAllTicker(): Promise<any> {
    try {
      console.log('[Luno getAllTicker] - Start');
      const response = await axios.get(`${this.baseUrl}/api/1/tickers`, {
        httpsAgent: this.agent,
      });
      console.log('[Luno getAllTicker] - API response:', response.data);
      const formattedTickers = response.data.tickers.map((ticker: any) => ({
        symbol: this.convertFromLunoPair(ticker.pair),
        lastPrice: ticker.last_trade,
      }));
      console.log('[Luno getAllTicker] - Formatted:', formattedTickers);
      return formattedTickers;
    } catch (error) {
      console.error('Luno getAllTicker error:', error.response?.data || error.message);
      handleLunoError(error);
    }
  }

  // Proxy tester
  async proxyCaller() {
    try {
      console.log('Testing Luno proxy connection...');
      const response = await axios.get(`${this.baseUrl}/api/1/tickers`, {
        httpsAgent: this.agent,
      });
      console.log('Luno proxy test successful:', response.data);
      
      // Convert ticker symbols from Luno to internal format
      const convertedData = {
        ...response.data,
        tickers: response.data.tickers.map((ticker: any) => ({
          ...ticker,
          pair: this.convertFromLunoPair(ticker.pair)
        }))
      };
      
      return convertedData;
    } catch (error) {
      console.error('Luno proxy test failed:', error.response?.data || error.message);
      handleLunoError(error);
    }
  }
}