import { BadRequestException } from '@nestjs/common';
import { EntityManager } from 'typeorm';
import { UserKey } from 'src/user_key/entities/user_key.entity';
import { UserKeyStatus } from 'src/user_key/entities/user_key.entity';
import { CustomErrorMessages } from './errorMessage';

export class UserKeyValidatorHelper {
  constructor(private readonly em: EntityManager) {}

  async validateUserKey(
    userKeyId: string | number,
    operation: string,
  ): Promise<void> {
    const userKey = await this.em
      .createQueryBuilder(UserKey, 'uk')
      .where('uk.id = :id AND uk.status = :status', {
        id: userKeyId,
        status: UserKeyStatus.ACTIVE,
      })
      .getOne();
    console.log(userKeyId);
    if (!userKey) {
      throw new BadRequestException(
        CustomErrorMessages.USER_KEY.INACTIVE_OR_DELETED(operation),
      );
    }
  }

  async validateMultipleUserKeys(
    userKeyIds: (string | number)[],
    operation: string,
  ): Promise<void> {
    await Promise.all(
      userKeyIds.map((keyId) => {
        console.log(keyId);
        return this.validateUserKey(keyId, operation);
      }),
    );
  }
}
