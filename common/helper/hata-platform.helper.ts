import { Injectable, BadRequestException } from '@nestjs/common';
import axios from 'axios';
import { decrypted, sortJson, sortParams } from 'common/util/utils';
import * as crypto from 'crypto';
import { handleHataError } from './exchange-error.helper';
import configuration from 'config/configuration';
import * as Proxy from 'https-proxy-agent';

export class HataPlatformHelper {
  private readonly globalBaseUrl: string =
    configuration().exchanges.hata.globalApiUrl;
  private readonly malaysiaBaseUrl: string =
    configuration().exchanges.hata.myrApiUrl;

  private baseUrl: string;
  private secretKey: string;
  private apiKey: string;
  private proxy: string;
  private agent: Proxy.HttpsProxyAgent<string>;

  async init(
    platformType: 'hata global' | 'hata myr',
    apiKey: string,
    secretKey: string,
  ): Promise<void> {
    this.baseUrl = this.getBaseUrl(platformType);
    if (secretKey) {
      this.secretKey = this.secretKey = await decrypted(
        secretKey,
        configuration().encryption.secret,
      );
    } else {
      this.secretKey = secretKey;
    }
    this.apiKey = apiKey;
    this.proxy = `http://${configuration().decodo.username}:${configuration().decodo.password}@${configuration().decodo.url}`;
    this.agent = new Proxy.HttpsProxyAgent(this.proxy);
  }

  private getBaseUrl(
    platformType: 'hata global' | 'hata myr' = 'hata global',
  ): string {
    return platformType === 'hata myr'
      ? this.malaysiaBaseUrl
      : this.globalBaseUrl;
  }

  private buildQueryString(params: Record<string, any>): string {
    const keys = Object.keys(params).sort();
    return keys.map((key) => `${key}=${params[key]}`).join('&');
  }

  private signParams(
    params: Record<string, any>,
    method: 'POST' | 'GET',
  ): string {
    let queryString;
    if (method == 'GET') {
      console.log(this.secretKey);
      queryString = sortParams(params);
    } else {
      console.log(this.secretKey);
      console.log(this.apiKey);
      queryString = sortJson(params);
    }
    console.log(queryString, 'sign params');
    return crypto
      .createHmac('sha256', this.secretKey)
      .update(queryString)
      .digest('hex');
  }

  private buildHeaders(
    params: Record<string, any>,
    method: 'POST' | 'GET',
  ): Record<string, string> {
    const signature = this.signParams(params, method);
    return {
      'X-API-Key': this.apiKey,
      Signature: signature,
      'Content-Type': 'application/json',
    };
  }

  async readActivePairFromExchange(
    platformType: 'hata global' | 'hata myr' = 'hata global',
  ): Promise<any[]> {
    try {
      const baseUrl = this.baseUrl;
      const params = { timestamp: Math.floor(Date.now() / 1000) };
      const queryString = this.buildQueryString(params);
      const headers = this.buildHeaders(params, 'GET');
      const url = `${baseUrl}/orderbook/sapi/exchange-info?${queryString}`;
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      const data = response.data;
      if (!data.data || !data.data.symbols) {
        throw new BadRequestException(
          'Invalid response from Hata exchange info',
        );
      }
      return data.data.symbols;
    } catch (error) {
      handleHataError(error);
    }
  }

  async createOrder(
    orderDto: {
      symbol: string;
      side: 'BUY' | 'SELL';
      type: 'MARKET' | 'LIMIT';
      quantity: string;
      price?: string;
      post_only?: string;
      quote_qty?: string;
    },
    platformType: 'hata global' | 'hata myr' = 'hata global',
  ): Promise<any> {
    try {
      const baseUrl = this.baseUrl;
      const params = { timestamp: Math.floor(Date.now() / 1000) };
      const queryString = this.buildQueryString(params);
      // Map local orderDto fields to Hata API expected fields
      const body: Record<string, any> = {
        pair: orderDto.symbol,
        is_buy: orderDto.side === 'BUY' ? 'true' : 'false',
        type: orderDto.type.toLowerCase(),
        qty: orderDto.quantity,
        post_only: 'true',
        timestamp: params.timestamp,
      };
      if (orderDto.price && orderDto.type === 'LIMIT') {
        body.price = orderDto.price;
      }
      if (orderDto.post_only) {
        body.post_only = orderDto.post_only;
      }
      if (orderDto.quote_qty) {
        body.quote_qty = orderDto.quote_qty;
        delete body.qty;
      }
      const headers = this.buildHeaders(body, 'POST');
      const url = `${baseUrl}/orderbook/sapi/orders/create?${queryString}`;

      const response = await axios.post(url, body, {
        headers,
        httpsAgent: this.agent,
      });
      return response.data.data;
    } catch (error) {
      handleHataError(error);
    }
  }

  async cancelOrder(
    orderId: string,
    platformType: 'hata global' | 'hata myr' = 'hata global',
  ): Promise<any> {
    try {
      const baseUrl = this.baseUrl;
      const params = { timestamp: Math.floor(Date.now() / 1000) };
      const queryString = this.buildQueryString(params);
      const body = { order_id: orderId, timestamp: params.timestamp };
      const headers = this.buildHeaders(body, 'POST');
      const url = `${baseUrl}/orderbook/sapi/orders/cancel?${queryString}`;
      const response = await axios.post(url, body, {
        headers,
        httpsAgent: this.agent,
      });
      console.log(url);
      return response.data;
    } catch (error) {
      handleHataError(error);
    }
  }

  async readBalance(
    token_symbol?: string,
    platformType: 'hata global' | 'hata myr' = 'hata global',
  ): Promise<any> {
    try {
      const baseUrl = this.baseUrl;
      const params: Record<string, any> = {
        timestamp: Math.floor(Date.now() / 1000),
      };
      if (token_symbol) {
        params.token_symbol = token_symbol;
      }
      const queryString = this.buildQueryString(params);
      const headers = this.buildHeaders(params, 'GET');
      const url = `${baseUrl}/orderbook/sapi/balance?${queryString}`;
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      const formattedBalances = response.data.data.map(
        ({
          symbol,
          available,
          frozen,
          available_in_quote,
          frozen_in_quote,
        }) => ({
          symbol,
          avai_balance: available,
          freeze_balance: frozen,
          avai_balance_quote: available_in_quote,
          freeze_balance_quote: frozen_in_quote,
        }),
      );
      return formattedBalances;
    } catch (error) {
      handleHataError(error);
    }
  }

  async readNonZeroBalance(
    token_symbol?: string,
    platformType: 'hata global' | 'hata myr' = 'hata global',
  ): Promise<any> {
    try {
      const baseUrl = this.baseUrl;
      const params: Record<string, any> = {
        timestamp: Math.floor(Date.now() / 1000),
      };
      if (token_symbol) {
        params.token_symbol = token_symbol;
      }
      const queryString = this.buildQueryString(params);
      const headers = this.buildHeaders(params, 'GET');
      const url = `${baseUrl}/orderbook/sapi/balance?${queryString}`;
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      const formattedBalances = response.data.data.map(
        ({
          symbol,
          available,
          frozen,
          available_in_quote,
          frozen_in_quote,
        }) => ({
          symbol,
          avai_balance: available,
          freeze_balance: frozen,
          avai_balance_quote: available_in_quote,
          freeze_balance_quote: frozen_in_quote,
        }),
      );
      return formattedBalances;
    } catch (error) {
      handleHataError(error);
    }
  }

  async getAllActiveOrders(
    pair_name?: string,
    platformType: 'hata global' | 'hata myr' = 'hata global',
  ): Promise<any[]> {
    try {
      const baseUrl = this.baseUrl;
      const params: Record<string, any> = {
        order_rows: 250,
        timestamp: Math.floor(Date.now() / 1000),
      };
      if (pair_name) {
        params.pair_name = pair_name;
      }
      const queryString = this.buildQueryString(params);
      const headers = this.buildHeaders(params, 'GET');
      const url = `${baseUrl}/orderbook/sapi/users/orders?${queryString}`;
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      return response.data;
    } catch (error) {
      handleHataError(error);
    }
  }

  async createListenKey(): Promise<any> {
    try {
      const params = { timestamp: Math.floor(Date.now() / 1000) };
      const headers = this.buildHeaders(params, 'POST');
      const queryString = this.buildQueryString(params);
      const url = `${this.baseUrl}/auth/sapi/user-stream-key?${queryString}`;
      const response = await axios.post(
        url,
        { timestamp: Math.floor(Date.now() / 1000) },
        { headers, httpsAgent: this.agent },
      );
      return response.data.data;
    } catch (error) {
      console.log(error);
      handleHataError(error);
    }
  }

  async extendUserStreamKey(
    currentListenKey: string,
    platformType: 'hata global' | 'hata myr' = 'hata global',
  ): Promise<string> {
    try {
      const baseUrl = this.getBaseUrl(platformType);
      const params = { timestamp: Math.floor(Date.now() / 1000) };
      const queryString = this.buildQueryString(params);
      const headers = this.buildHeaders(params, 'POST');
      const url = `${baseUrl}/auth/sapi/user-stream-key?${queryString}`;

      const body = { key: currentListenKey };
      const response = await axios.put(url, body, {
        headers,
        httpsAgent: this.agent,
      });
      if (!response.data?.key) {
        throw new BadRequestException(
          'Invalid response from Hata user-stream-key extension',
        );
      }
      return response.data.key; // updated/extended key
    } catch (error) {
      handleHataError(error);
    }
  }

  async cancelAllOrders(
    pairName: string = 'BTCUSDT',
    platformType: 'hata global' | 'hata myr' = 'hata global',
  ): Promise<any> {
    try {
      const params = { timestamp: Math.floor(Date.now() / 1000) };
      const queryString = this.buildQueryString(params);
      const formattedPairName = pairName.includes('/')
        ? pairName.replace('/', '')
        : pairName;
      const body = {
        pair_name: formattedPairName,
        timestamp: params.timestamp,
      };
      // const body = { pair_name: formattedPairName,timeStamp:params.timestamp };
      const headers = this.buildHeaders(body, 'POST');

      const url = `${this.baseUrl}/orderbook/sapi/orders/cancel/all?${queryString}`;

      const response = await axios.post(url, body, {
        headers,
        httpsAgent: this.agent,
      });
      return response.data;
    } catch (error) {
      handleHataError(error);
    }
  }

  async getOrderDetails(
    orderId: string,
    platformType: 'hata global' | 'hata myr' = 'hata global',
  ): Promise<any> {
    try {
      const params: Record<string, any> = {
        order_id: orderId,
        timestamp: Math.floor(Date.now() / 1000),
      };
      const queryString = this.buildQueryString(params);
      const headers = this.buildHeaders(params, 'GET');
      const url = `${this.baseUrl}/orderbook/sapi/order?${queryString}`;
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });

      // response data status can be 'active','fulfilled','cancelled','partially_filled'

      return response.data;
    } catch (error) {
      handleHataError(error);
    }
  }

  // Get trades history
  async getTradesHistory(
    options: {
      pair_name?: string;
      is_buy?: boolean;
      start_time?: number;
      end_time?: number;
      page: number;
      rows: number;
      trade_id?: number;
    },
    platformType: 'hata global' | 'hata myr' = 'hata global',
  ): Promise<any> {
    try {
      const params = {
        ...options,
        timestamp: Math.floor(Date.now() / 1000),
        page: options.page,
        rows: Math.min(options.rows || 100, 100),
      };
      console.log('Timestamp being used:', params.timestamp);
      const queryString = this.buildQueryString(params);
      const headers = this.buildHeaders(params, 'GET');
      const url = `${this.baseUrl}/orderbook/sapi/trades/history?${queryString}`;
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      return response.data;
    } catch (error) {
      handleHataError(error);
    }
  }

  // Get order book
  async getOrderBook(pairName: string, isBuy?: boolean): Promise<any> {
    try {
      const params: Record<string, any> = {
        pair_name: pairName,
        timestamp: Math.floor(Date.now() / 1000),
      };
      if (isBuy !== undefined) params.is_buy = isBuy;
      const queryString = this.buildQueryString(params);
      const headers = this.buildHeaders(params, 'GET');
      const url = `${this.baseUrl}/orderbook/api/orderbook?${queryString}`;
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      return response.data.data;
    } catch (error) {
      handleHataError(error);
    }
  }

  // Get order book
  async getTickerBySymbol(
    pairName: string = 'BTCUSDT',
    platformType: 'hata global' | 'hata myr' = 'hata global',
  ): Promise<any> {
    try {
      const params: Record<string, any> = {
        symbol: pairName,
        timestamp: Math.floor(Date.now() / 1000),
      };
      const queryString = this.buildQueryString(params);
      const headers = this.buildHeaders(params, 'GET');
      const url = `${this.baseUrl}/orderbook/api/ticker?${queryString}`;
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      return response.data.data;
    } catch (error) {
      handleHataError(error);
    }
  }

  async getAllTicker(): Promise<any> {
    try {
      const baseUrl = this.baseUrl;
      const params = { timestamp: Math.floor(Date.now() / 1000) };
      const headers = this.buildHeaders(params, 'GET');
      const url = `${baseUrl}/orderbook/api/ticker/all`;
      const response = await axios.get(url);

      const truncatedData = response.data.data.map(({ symbol, close }) => ({
        symbol,
        price: close,
      }));
      return { data: truncatedData };
    } catch (error) {
      handleHataError(error);
    }
  }
  async proxyCaller() {
    // const proxy = '*******************************************';
    // const agent = new Proxy.HttpsProxyAgent(proxy);
    // axios
    //   .get('https://api.binance.com/api/v3/time', { httpsAgent: agent })
    //   .then((res) => console.log(res.data))
    //   .catch((err) => console.error('Error:', err));
  }
}
