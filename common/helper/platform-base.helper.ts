import { HataPlatformHelper } from './hata-platform.helper';
import { BinancePlatformHelper } from './binance-platform.helper';
import { TokenizePlatformHelper } from './tokenize-platform.helper';
import { LunoPlatformHelper } from './luno-platform.helper';

export function callPlatformHelper(
  platform: string,
):
  | HataPlatformHelper
  | BinancePlatformHelper
  | TokenizePlatformHelper
  | LunoPlatformHelper {
  platform = platform.toLowerCase();
  if (platform === 'hata myr') {
    return new HataPlatformHelper();
  } else if (platform === 'hata global') {
    return new HataPlatformHelper();
  } else if (platform === 'binance') {
    return new BinancePlatformHelper();
  } else if (platform === 'tokenize') {
    return new TokenizePlatformHelper();
  } else if (platform === 'luno') {
    return new LunoPlatformHelper();
  } else {
    throw new Error('Unknown platform for the provided API key');
  }
}
