import {
  OrderType,
  Side,
  Spot,
  TimeInForce,
} from '@binance/connector-typescript';
import { BadRequestException } from '@nestjs/common';
import axios from 'axios';
import * as crypto from 'crypto';
import { handleBinanceError } from './exchange-error.helper';
import { decrypted } from 'common/util/utils';
import configuration from 'config/configuration';
import * as Proxy from 'https-proxy-agent';

export class BinancePlatformHelper {
  // Base URL for Binance Spot API
  private readonly baseUrl: string = configuration().exchanges.binance.apiUrl;

  private client: Spot;
  private tax: number;
  private apiKey: string;
  private secretKey: string;
  private proxy: string;
  private agent: Proxy.HttpsProxyAgent<string>;

  /**
   * Initialize the helper with your Binance API key and secret.
   */
  async init(
    platformType: string,
    apiKey: string,
    secretKey: string,
  ): Promise<void> {
    if (secretKey != '') {
      this.secretKey = await decrypted(
        secretKey,
        configuration().encryption.secret,
      );
    }
    this.proxy = `http://${configuration().decodo.username}:${configuration().decodo.password}@${configuration().decodo.url}`;
    this.agent = new Proxy.HttpsProxyAgent(this.proxy);

    this.client = new Spot(apiKey, this.secretKey, {
      baseURL: this.baseUrl,
    });
    this.apiKey = apiKey;
    this.tax = 0.01;
    return;
  }

  //  Build a query string from an object.

  private buildQueryString(params: Record<string, any>): string {
    const keys = Object.keys(params).sort();
    return keys.map((key) => `${key}=${params[key]}`).join('&');
  }

  //  Sign the query string using HMAC SHA256.

  private signParams(queryString: string): string {
    return crypto
      .createHmac('sha256', this.secretKey)
      .update(queryString)
      .digest('hex');
  }

  // Build headers
  private buildHeaders(): Record<string, string> {
    return {
      'X-MBX-APIKEY': this.apiKey,
      'Content-Type': 'application/json',
    };
  }

  //  Create a listen key for user data stream.

  // User Stream
  async createListenKey(): Promise<string> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/api/v3/userDataStream`,
        null,
        {
          headers: { 'X-MBX-APIKEY': this.apiKey },
          httpsAgent: this.agent,
        },
      );
      return response.data.listenKey;
    } catch (error) {
      handleBinanceError(error);
    }
  }

  // Extend (ping) the listen key to keep the data stream alive.

  async extendListenKey(listenKey: string): Promise<void> {
    try {
      const url = `${this.baseUrl}/api/v3/userDataStream?listenKey=${listenKey}`;
      const headers = this.buildHeaders();
      await axios.put(url, null, { headers, httpsAgent: this.agent });
    } catch (error) {
      handleBinanceError(error);
    }
  }

  // Create a new order.
  async createOrder(orderDto: {
    symbol: string;
    side: 'BUY' | 'SELL';
    type: 'MARKET' | 'LIMIT';
    quantity?: string;
    price?: string;
    post_only?: string;
    timeInForce?: string;
    quoteOrderQty?: string;
  }): Promise<any> {
    try {
      console.log(orderDto);
      let obj = {
        quantity:
          orderDto.type === 'LIMIT'
            ? Number(orderDto.quantity)
            : Number(orderDto.quoteOrderQty),
        recvWindow: 59999,
        type: OrderType.LIMIT_MAKER,
      };
      if (orderDto.type === 'LIMIT') {
        obj['price'] = Number(orderDto.price);
        // obj['timeInForce'] = TimeInForce.GTC;
      }
      if (orderDto.post_only == 'false' && orderDto.type === 'LIMIT') {
        obj.type = OrderType.LIMIT;
      } else if (orderDto.type === 'MARKET') {
        obj['type'] = OrderType.MARKET;
      }

      const params = {
        ...obj,
        symbol: orderDto.symbol,
        side: orderDto.side == 'BUY' ? Side.BUY : Side.SELL,
        type: obj['type'],
        timestamp: Date.now(),
      };
      const queryString = this.buildQueryString(params);
      const signature = this.signParams(queryString);
      const finalQuery = `${queryString}&signature=${signature}`;
      const url = `${this.baseUrl}/api/v3/order?${finalQuery}`;
      const headers = {
        'X-MBX-APIKEY': this.apiKey,
        'Content-Type': 'application/x-www-form-urlencoded',
      };

      const response = await axios.post(url, null, {
        headers,
        httpsAgent: this.agent,
      });
      // console.log(response);

      return { id: response.data.orderId, price: orderDto.price };

      // const response = await this.client.newOrder(
      //   orderDto.symbol,
      //   orderDto.side == 'BUY' ? Side.BUY : Side.SELL,
      //   obj['type'],
      //   obj,
      // );
      // return { id: response.orderId, price: response.price };
    } catch (error) {
      handleBinanceError(error);
    }
  }

  // Cancel an order.

  async cancelOrder(orderId: string, symbol: string): Promise<any> {
    try {
      const params = {
        symbol: symbol.toUpperCase(),
        orderId,
        timestamp: Date.now(),
      };
      const queryString = this.buildQueryString(params);
      const signature = this.signParams(queryString);
      const finalQuery = `${queryString}&signature=${signature}`;

      const url = `${this.baseUrl}/api/v3/order?${finalQuery}`;
      const headers = this.buildHeaders();
      const response = await axios.delete(url, {
        headers,
        httpsAgent: this.agent,
      });
      return response.data;
    } catch (error) {
      handleBinanceError(error);
    }
  }

  // Read account balance.
  async readBalance(): Promise<any> {
    try {
      const params = { timestamp: Date.now() };
      const queryString = this.buildQueryString(params);
      const signature = this.signParams(queryString);
      const finalQuery = `${queryString}&signature=${signature}`;
      const url = `${this.baseUrl}/api/v3/account?${finalQuery}`;
      const headers = this.buildHeaders();
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      const formattedBalances = response.data.balances.map(
        ({ asset, free, locked }) => ({
          symbol: asset,
          avai_balance: free,
          freeze_balance: locked,
          avai_balance_quote: 0,
          freeze_balance_quote: 0,
        }),
      );
      return formattedBalances;
    } catch (error) {
      handleBinanceError(error);
    }
  }

  async readNonZeroBalance(): Promise<any> {
    try {
      const params = { timestamp: Date.now(), omitZeroBalances: true };
      const queryString = this.buildQueryString(params);
      const signature = this.signParams(queryString);
      const finalQuery = `${queryString}&signature=${signature}`;
      const url = `${this.baseUrl}/api/v3/account?${finalQuery}`;
      const headers = this.buildHeaders();
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      const formattedBalances = response.data.balances.map(
        ({ asset, free, locked }) => ({
          symbol: asset,
          avai_balance: free,
          freeze_balance: locked,
          avai_balance_quote: 0,
          freeze_balance_quote: 0,
        }),
      );
      return formattedBalances;
    } catch (error) {
      handleBinanceError(error);
    }
  }

  async readActivePairFromExchange(): Promise<any[]> {
    try {
      const url = `${this.baseUrl}/api/v3/exchangeInfo`;
      const headers = this.buildHeaders();
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      console.log(response);
      return response.data.symbols;
    } catch (error) {
      handleBinanceError(error);
    }
  }

  // User Stream

  async extendUserStreamKey(listenKey: string): Promise<void> {
    try {
      await axios.put(`${this.baseUrl}/api/v3/userDataStream`, null, {
        headers: { 'X-MBX-APIKEY': this.apiKey },
        params: { listenKey },
        httpsAgent: this.agent,
      });
    } catch (error) {
      handleBinanceError(error);
    }
  }

  // Get all active orders.

  async getAllActiveOrders(symbol?: string): Promise<any> {
    try {
      const params: Record<string, any> = { timestamp: Date.now() };
      if (symbol) {
        params.symbol = symbol;
      }
      const queryString = this.buildQueryString(params);
      const signature = this.signParams(queryString);
      const finalQuery = `${queryString}&signature=${signature}`;

      const url = `${this.baseUrl}/api/v3/openOrders?${finalQuery}`;
      const headers = this.buildHeaders();
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      return response.data;
    } catch (error) {
      handleBinanceError(error);
    }
  }

  //  Get order details

  async getOrderDetails(symbol: string, orderId: string): Promise<any> {
    try {
      const params = {
        symbol,
        orderId,
        timestamp: Date.now(),
      };
      const queryString = this.buildQueryString(params);
      const signature = this.signParams(queryString);
      const finalQuery = `${queryString}&signature=${signature}`;

      const url = `${this.baseUrl}/api/v3/order?${finalQuery}`;
      const headers = this.buildHeaders();
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      return response.data;
    } catch (error) {
      handleBinanceError(error);
    }
  }

  //  Get trades history.

  async getTradesHistory(params: {
    symbol: string;
    startTime?: number;
    endTime?: number;
    limit?: number;
  }): Promise<any> {
    try {
      const requestParams: Record<string, any> = {
        symbol: params.symbol,
        timestamp: Date.now(),
      };
      if (params.startTime) requestParams.startTime = params.startTime;
      if (params.endTime) requestParams.endTime = params.endTime;
      if (params.limit) requestParams.limit = params.limit;

      const queryString = this.buildQueryString(requestParams);
      const signature = this.signParams(queryString);
      const finalQuery = `${queryString}&signature=${signature}`;

      const url = `${this.baseUrl}/api/v3/myTrades?${finalQuery}`;
      const headers = this.buildHeaders();
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      return response.data;
    } catch (error) {
      handleBinanceError(error);
    }
  }

  //  Get order book

  async getOrderBook(symbol: string, limit = 100): Promise<any> {
    try {
      const params = { symbol, limit };
      const queryString = this.buildQueryString(params);
      const url = `${this.baseUrl}/api/v3/depth?${queryString}`;
      // Public endpoint; headers are optional but included for consistency.
      const headers = this.buildHeaders();
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      let bids = response.data.bids.map((item: any) => ({
        price: item[0],
        qty: item[1],
      }));
      let asks = response.data.asks.map((item: any) => ({
        price: item[0],
        qty: item[1],
      }));
      return { bids, asks };
    } catch (error) {
      handleBinanceError(error);
    }
  }

  // Get 24hr ticker price change statistics for a symbol.

  async getTickerBySymbol(symbol: string): Promise<any> {
    try {
      const params = { symbol };
      const queryString = this.buildQueryString(params);
      const url = `${this.baseUrl}/api/v3/ticker/24hr?${queryString}`;
      const headers = this.buildHeaders();
      const response = await axios.get(url, {
        headers,
        httpsAgent: this.agent,
      });
      return { close: response.data.lastPrice };
    } catch (error) {
      handleBinanceError(error);
    }
  }

  async getAllTicker(): Promise<any> {
    try {
      const url = `${this.baseUrl}/api/v3/ticker/24hr`;
      const response = await axios.get(url, { httpsAgent: this.agent });

      // Truncate each item to just symbol and lastPrice
      const simplified = response.data.map((item: any) => ({
        symbol: item.symbol,
        lastPrice: item.lastPrice,
      }));
      return simplified; // Return as array
    } catch (error) {
      handleBinanceError(error);
    }
  }

  async proxyCaller() {
    console.log('proxy caller');
    // const proxy = '********************************************************';
    // const agent = new Proxy.HttpsProxyAgent(proxy);
    console.log(this.agent);
    let data = await axios.get('https://api.binance.com/api/v3/time', {
      httpsAgent: this.agent,
    });
    console.log('data', data.data);
    return data.data;
    // axios
    //   .get('https://api.binance.com/api/v3/time', { httpsAgent: agent })
    //   .then((res) => console.log(res.data))
    //   .catch((err) => console.error('Proxy:', err));
  }
}
