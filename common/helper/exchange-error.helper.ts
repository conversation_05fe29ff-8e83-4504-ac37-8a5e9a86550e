import {
  BadRequestException,
  UnauthorizedException,
  InternalServerErrorException,
} from '@nestjs/common';
import { CustomErrorMessages } from './errorMessage';

//Binance error handler
export function handleBinanceError(error: any): never {
  const errorData = error?.response?.data || error;
  console.log(errorData);
  const message =
    errorData?.msg || errorData?.message || 'Unknown error from Binance';

  if (message.toLowerCase().includes('invalid symbol')) {
    throw new BadRequestException(CustomErrorMessages.RATE.INVALID_SYMBOL);
  } else if (message.toLowerCase().includes('insufficient balance')) {
    throw new BadRequestException(CustomErrorMessages.BOT.INSUFFICIENT_FUNDS);
  } else if (
    message.toLowerCase().includes('api-key') ||
    message.toLowerCase().includes('unauthorized')
  ) {
    throw new BadRequestException(CustomErrorMessages.USER_KEY.INVALID_USERKEY);
  } else if (message.toLowerCase().includes('signature')) {
    throw new BadRequestException(
      CustomErrorMessages.PLATFORM.USERKEY_NOT_LOADED,
    );
  } else if (
    message.toLowerCase().includes('minimum') ||
    message.toLowerCase().includes('notional')
  ) {
    throw new BadRequestException('order size below minimum');
  }

  throw new InternalServerErrorException(message);
}

// Hata error handler
export function handleHataError(error: any): never {
  const res = error?.response;
  const status = res?.status;
  const data = res.data;

  const message =
    data?.message ||
    error?.message ||
    (typeof error === 'string' ? error : 'Unknown error occurred');

  console.log(data, 'data');

  if (status === 401 || status === 403) {
    throw new BadRequestException(CustomErrorMessages.USER_KEY.INVALID_USERKEY);
  }

  if (status === 404) {
    throw new BadRequestException(CustomErrorMessages.RATE.INVALID_SYMBOL);
  }

  if (data.error.toLowerCase().includes('insufficient balance')) {
    throw new BadRequestException('insufficient balance');
  } else if (
    data.error.toLowerCase().includes('minimum') ||
    data.error.toLowerCase().includes('notional')
  ) {
    throw new BadRequestException('order size below minimum');
  } else if (status >= 400 && status < 500) {
    throw new BadRequestException(data.error);
  }

  throw new InternalServerErrorException(message);
}

export function handleTokenizeError(error: any): never {
  const res = error?.response;
  const status = res?.status;
  const data = res?.data;

  const message =
    data?.message ||
    error?.message ||
    (typeof error === 'string' ? error : 'Unknown error occurred');

  if (status === 401 || status === 403) {
    throw new BadRequestException(CustomErrorMessages.USER_KEY.INVALID_USERKEY);
  }

  if (status === 404) {
    throw new BadRequestException(CustomErrorMessages.RATE.INVALID_SYMBOL);
  }

  if (status >= 400 && status < 500) {
    throw new BadRequestException(CustomErrorMessages.COMMON.INVALID_INPUT);
  }

  throw new InternalServerErrorException(message);
}

export function handleLunoError(error: any): never {
  const response = error?.response;
  const status = response?.status;
  const errorData = response?.data || error;
  
  console.log('[Luno Error]', errorData);
  
  // Extract error code and message
  const errorCode = errorData?.error_code || '';
  const message = errorData?.error || 
                  errorData?.message || 
                  (typeof error === 'string' ? error : 'Unknown error from Luno');

  // Convert to lowercase for case-insensitive matching
  const lowerMessage = message.toLowerCase();

  // Authentication errors (API key issues)
  if (status === 401 || 
      errorCode === 'ErrInvalidAuth' || 
      lowerMessage.includes('invalid credentials') ||
      lowerMessage.includes('authentication failed')) {
    throw new BadRequestException(CustomErrorMessages.USER_KEY.INVALID_USERKEY);
  }
  
  // Insufficient balance errors
  if (errorCode === 'ErrInsufficientBalance' || 
      lowerMessage.includes('insufficient') || 
      lowerMessage.includes('not enough')) {
    throw new BadRequestException(CustomErrorMessages.ORDER.INSUFFICIENT_FUNDS);
  }
  
  // Invalid trading pair errors
  if (errorCode === 'ErrInvalidPair' || 
      lowerMessage.includes('invalid pair') || 
      lowerMessage.includes('invalid symbol') || 
      lowerMessage.includes('unknown asset')) {
    throw new BadRequestException(CustomErrorMessages.RATE.INVALID_SYMBOL);
  }
  
  // Order size errors
  if (errorCode === 'ErrInvalidVolume' || 
      errorCode === 'ErrInvalidCounterVolume' || 
      lowerMessage.includes('minimum') || 
      lowerMessage.includes('too small') || 
      lowerMessage.includes('below minimum')) {
    throw new BadRequestException('Order size below minimum');
  }
  
  // Order not found errors
  if (errorCode === 'ErrInvalidOrder' || 
      lowerMessage.includes('order not found') || 
      lowerMessage.includes('unknown order')) {
    throw new BadRequestException('Order not found');
  }
  
  // Rate limit errors
  if (status === 429 || 
      errorCode === 'ErrRateLimitExceeded' || 
      lowerMessage.includes('too many requests')) {
    throw new BadRequestException('Too many requests - rate limit exceeded');
  }
  
  // Bad request errors
  if (status === 400) {
    throw new BadRequestException(message || 'Invalid request to Luno API');
  }
  
  // Signature errors (though Luno uses Basic Auth)
  if (lowerMessage.includes('signature')) {
    throw new BadRequestException(CustomErrorMessages.PLATFORM.USERKEY_NOT_LOADED);
  }

  // Server-side errors
  if (status >= 500) {
    throw new InternalServerErrorException('Luno API is currently unavailable');
  }

  throw new InternalServerErrorException(message);
}
