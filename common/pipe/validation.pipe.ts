import { ValidationPipe } from '@nestjs/common';
import { BadRequestException } from '@nestjs/common';
import { HttpException } from '@nestjs/common';
import { PipeTransform, Injectable, ArgumentMetadata } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';

@Injectable()
export class GlobalValidationPipes implements PipeTransform<any> {
  async transform(value: any, { metatype }: ArgumentMetadata) {
    if (!metatype || !this.toValidate(metatype)) {
      return value;
    }

    const object = plainToClass(metatype, value, {});
    const errors = await validate(object);
    if (errors.length > 0) {
      throw new HttpException(
        errors[0].constraints[Object.keys(errors[0].constraints)[0]],
        400,
      );
    }
    return value;
  }

  private toValidate(metatype: Function): boolean {
    const types: Function[] = [String, <PERSON><PERSON>an, Number, Array, Object];
    return !types.includes(metatype);
  }
}
