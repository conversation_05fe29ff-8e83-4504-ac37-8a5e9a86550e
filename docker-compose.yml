services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - .env
    container_name: zedex-v2
    environment:
      - PORT=${PORT}
      - REDIS_HOST = redis
      - REDIS_PORT = 6379

    ports:
      - "3000:3000"
    depends_on:
      - redis

    volumes:
      - ./src:/app/src
  redis:
    image: redis:latest
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD}"]
    ports:
      - "6379:6379"
    environment:
      REDIS_ARGS: "--save 60 1 --appendonly yes"
