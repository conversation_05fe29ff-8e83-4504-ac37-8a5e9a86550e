{"name": "zedex-v2", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@binance/connector-typescript": "^0.3.14", "@bull-board/api": "^6.7.10", "@bull-board/express": "^6.7.10", "@logtail/pino": "^0.5.5", "@nestjs/bull": "^11.0.2", "@nestjs/common": "^10.4.15", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.15", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.4.15", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^8.1.0", "@nestjs/typeorm": "^10.0.2", "ali-oss": "^6.22.0", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bull": "^4.16.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.8.1", "cookie-parser": "^1.4.7", "decimal.js": "^10.5.0", "dotenv": "^16.4.7", "hi-base32": "^0.5.1", "https-proxy-agent": "^7.0.6", "ioredis": "^5.4.2", "moment-timezone": "^0.5.47", "mysql2": "^3.12.0", "nestjs-pino": "^4.4.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pino-http": "^10.5.0", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "typeorm": "^0.3.20"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/compression": "^1.8.1", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "eslint": "^9.9.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.5.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/src/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/platform(|/.*)$": "<rootDir>/libs/platform/src/$1", "^@app/bot(|/.*)$": "<rootDir>/libs/bot/src/$1"}}}